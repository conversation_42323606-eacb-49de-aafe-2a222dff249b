/*******************************************************************************
 * Size: 12 px
 * Bpp: 4
 * Opts: 
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_SIMSUN_12
#define LV_FONT_SIMSUN_12 1
#endif

#if LV_FONT_SIMSUN_12

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0021 "!" */
    0x75, 0x86, 0x75, 0x54, 0x43, 0x32, 0x0, 0x22,
    0x87,

    /* U+0022 "\"" */
    0x0, 0xd2, 0xd0, 0x6, 0x88, 0x60, 0x7, 0x16,
    0x0, 0x0, 0x0, 0x0,

    /* U+0023 "#" */
    0x4, 0x20, 0x60, 0x5, 0x10, 0x60, 0x8f, 0xff,
    0xf8, 0x6, 0x0, 0x60, 0x6, 0x0, 0x60, 0x6,
    0x0, 0x60, 0x8f, 0xff, 0xf8, 0x6, 0x2, 0x40,
    0x6, 0x3, 0x30,

    /* U+0024 "$" */
    0x0, 0x11, 0x0, 0x3, 0x88, 0x50, 0xa, 0x34,
    0xd0, 0xc, 0x34, 0x40, 0x6, 0xd4, 0x0, 0x0,
    0x7e, 0x10, 0x0, 0x38, 0xb0, 0x7, 0x33, 0xb0,
    0x1c, 0x33, 0x90, 0x6, 0x88, 0x30, 0x0, 0x33,
    0x0, 0x0, 0x11, 0x0,

    /* U+0025 "%" */
    0x57, 0x30, 0x50, 0x80, 0x82, 0x40, 0x90, 0x95,
    0x0, 0x80, 0x85, 0x0, 0x25, 0x53, 0x61, 0x0,
    0x58, 0x8, 0x0, 0x49, 0x9, 0x5, 0x8, 0x9,
    0x5, 0x4, 0x64,

    /* U+0026 "&" */
    0x5, 0x64, 0x0, 0x8, 0x9, 0x0, 0xa, 0x28,
    0x0, 0xb, 0x62, 0x0, 0xc, 0x42, 0x81, 0x74,
    0x80, 0x70, 0xb0, 0xa2, 0x30, 0xb0, 0x2d, 0x1,
    0x3b, 0x76, 0xb5,

    /* U+0027 "'" */
    0x1e, 0x20, 0x93, 0x8, 0x0, 0x0,

    /* U+0028 "(" */
    0x0, 0x2, 0x0, 0x70, 0x4, 0x40, 0xa, 0x0,
    0x28, 0x0, 0x55, 0x0, 0x64, 0x0, 0x46, 0x0,
    0x19, 0x0, 0x9, 0x0, 0x2, 0x60, 0x0, 0x52,
    0x0, 0x0,

    /* U+0029 ")" */
    0x20, 0x0, 0x17, 0x0, 0x5, 0x40, 0x0, 0xa0,
    0x0, 0x81, 0x0, 0x64, 0x0, 0x55, 0x0, 0x64,
    0x0, 0x90, 0x0, 0x90, 0x7, 0x10, 0x34, 0x0,
    0x0, 0x0,

    /* U+002A "*" */
    0x0, 0x22, 0x0, 0x0, 0x66, 0x0, 0x6a, 0x44,
    0xa6, 0x3, 0x98, 0x30, 0x3b, 0x77, 0xb3, 0x32,
    0x64, 0x33, 0x0, 0x75, 0x0,

    /* U+002B "+" */
    0x0, 0x13, 0x0, 0x0, 0x24, 0x0, 0x0, 0x24,
    0x0, 0x37, 0x89, 0x74, 0x0, 0x24, 0x0, 0x0,
    0x24, 0x0, 0x0, 0x13, 0x0,

    /* U+002D "-" */
    0x47, 0x77, 0x74,

    /* U+002E "." */
    0x6, 0x1, 0xe1,

    /* U+002F "/" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x15, 0x0, 0x0,
    0x60, 0x0, 0x0, 0x60, 0x0, 0x5, 0x10, 0x0,
    0x6, 0x0, 0x0, 0x42, 0x0, 0x0, 0x60, 0x0,
    0x2, 0x40, 0x0, 0x6, 0x0, 0x0, 0x15, 0x0,
    0x0, 0x50, 0x0, 0x0,

    /* U+0030 "0" */
    0x3, 0x88, 0x30, 0xc, 0x0, 0xb0, 0x3a, 0x0,
    0xa2, 0x58, 0x0, 0x84, 0x67, 0x0, 0x85, 0x58,
    0x0, 0x84, 0x3a, 0x0, 0xa2, 0xb, 0x0, 0xb0,
    0x3, 0x88, 0x30,

    /* U+0031 "1" */
    0x0, 0x50, 0x2a, 0x60, 0x5, 0x60, 0x5, 0x60,
    0x5, 0x60, 0x5, 0x60, 0x5, 0x60, 0x5, 0x60,
    0x3a, 0xc4,

    /* U+0032 "2" */
    0x5, 0x57, 0x60, 0x18, 0x0, 0xb0, 0x2b, 0x0,
    0xa1, 0x0, 0x0, 0xb0, 0x0, 0x7, 0x30, 0x0,
    0x44, 0x0, 0x2, 0x60, 0x0, 0x7, 0x0, 0x32,
    0x5c, 0xcc, 0xe0,

    /* U+0033 "3" */
    0x5, 0x58, 0x30, 0x1a, 0x0, 0xb0, 0x6, 0x0,
    0xc0, 0x0, 0x3, 0x80, 0x0, 0x5b, 0x10, 0x0,
    0x0, 0xb0, 0x2, 0x0, 0xa3, 0x3b, 0x0, 0xb1,
    0x7, 0x56, 0x50,

    /* U+0034 "4" */
    0x0, 0x6, 0x50, 0x0, 0xc, 0x50, 0x0, 0x67,
    0x50, 0x1, 0x57, 0x50, 0x6, 0x7, 0x50, 0x24,
    0x7, 0x50, 0x55, 0x59, 0x82, 0x0, 0x7, 0x50,
    0x0, 0x3b, 0x92,

    /* U+0035 "5" */
    0xa, 0xcc, 0xb0, 0x5, 0x0, 0x0, 0x5, 0x0,
    0x0, 0x8, 0x79, 0x60, 0x5, 0x0, 0xc0, 0x0,
    0x0, 0x93, 0x17, 0x0, 0x92, 0x39, 0x0, 0xb0,
    0x5, 0x57, 0x40,

    /* U+0036 "6" */
    0x1, 0x76, 0x90, 0x9, 0x0, 0x70, 0x29, 0x0,
    0x0, 0x59, 0x88, 0x70, 0x6c, 0x0, 0xa2, 0x67,
    0x0, 0x75, 0x49, 0x0, 0x64, 0xc, 0x0, 0x81,
    0x4, 0x85, 0x50,

    /* U+0037 "7" */
    0xe, 0xcc, 0xd3, 0x24, 0x0, 0x60, 0x0, 0x3,
    0x30, 0x0, 0x7, 0x0, 0x0, 0x18, 0x0, 0x0,
    0x64, 0x0, 0x0, 0xa2, 0x0, 0x0, 0xd2, 0x0,
    0x0, 0xc2, 0x0,

    /* U+0038 "8" */
    0x6, 0x66, 0x50, 0x27, 0x0, 0x91, 0x38, 0x0,
    0x82, 0xb, 0x72, 0x70, 0x5, 0x9e, 0x30, 0x27,
    0x2, 0xd0, 0x73, 0x0, 0x74, 0x45, 0x0, 0x81,
    0x6, 0x55, 0x50,

    /* U+0039 "9" */
    0x6, 0x67, 0x30, 0x38, 0x0, 0xa0, 0x65, 0x0,
    0x92, 0x65, 0x0, 0x94, 0x3a, 0x2, 0xc4, 0x5,
    0x85, 0xa3, 0x0, 0x0, 0xb0, 0x7, 0x2, 0x80,
    0xa, 0x68, 0x0,

    /* U+003A ":" */
    0x96, 0x31, 0x0, 0x0, 0x31, 0x96,

    /* U+003B ";" */
    0x97, 0x10, 0x0, 0x0, 0x10, 0x97, 0x44, 0x30,

    /* U+003C "<" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x70, 0x0, 0x7,
    0x10, 0x0, 0x71, 0x0, 0x6, 0x20, 0x0, 0x17,
    0x0, 0x0, 0x3, 0x50, 0x0, 0x0, 0x44, 0x0,
    0x0, 0x4, 0x40, 0x0, 0x0, 0x50,

    /* U+003D "=" */
    0x47, 0x77, 0x74, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x47, 0x77, 0x74,

    /* U+003E ">" */
    0x0, 0x0, 0x0, 0x7, 0x0, 0x0, 0x1, 0x70,
    0x0, 0x0, 0x17, 0x0, 0x0, 0x2, 0x60, 0x0,
    0x0, 0x71, 0x0, 0x5, 0x30, 0x0, 0x44, 0x0,
    0x4, 0x40, 0x0, 0x5, 0x0, 0x0,

    /* U+003F "?" */
    0x4, 0x78, 0x70, 0x24, 0x0, 0x93, 0x59, 0x0,
    0x85, 0x2, 0x0, 0xc1, 0x0, 0x9, 0x20, 0x0,
    0x42, 0x0, 0x0, 0x30, 0x0, 0x0, 0x31, 0x0,
    0x0, 0x96, 0x0,

    /* U+0040 "@" */
    0x2, 0x75, 0x60, 0x8, 0x16, 0x75, 0x35, 0x71,
    0x86, 0x63, 0x82, 0x66, 0x84, 0x74, 0x46, 0x65,
    0x68, 0x35, 0x45, 0x74, 0x71, 0x8, 0x0, 0x24,
    0x3, 0x86, 0x60,

    /* U+0041 "A" */
    0x0, 0x66, 0x0, 0x0, 0x79, 0x0, 0x0, 0x6b,
    0x0, 0x2, 0x3a, 0x0, 0x5, 0x7, 0x40, 0x7,
    0x57, 0x70, 0x6, 0x0, 0xb0, 0x14, 0x0, 0xc0,
    0x87, 0x1, 0xd6,

    /* U+0042 "B" */
    0x4d, 0x46, 0x70, 0xb, 0x0, 0xb1, 0xb, 0x0,
    0xa2, 0xb, 0x0, 0xb0, 0xc, 0x49, 0x40, 0xb,
    0x0, 0x93, 0xb, 0x0, 0x58, 0xb, 0x0, 0x77,
    0x4d, 0x55, 0x90,

    /* U+0043 "C" */
    0x2, 0x76, 0xd3, 0xb, 0x0, 0x16, 0x48, 0x0,
    0x1, 0x85, 0x0, 0x0, 0x94, 0x0, 0x0, 0x95,
    0x0, 0x0, 0x67, 0x0, 0x2, 0x1c, 0x0, 0x23,
    0x4, 0x97, 0x50,

    /* U+0044 "D" */
    0x4d, 0x57, 0x30, 0xb, 0x0, 0xb0, 0xb, 0x0,
    0x75, 0xb, 0x0, 0x58, 0xb, 0x0, 0x59, 0xb,
    0x0, 0x58, 0xb, 0x0, 0x85, 0xb, 0x0, 0xb0,
    0x4d, 0x58, 0x20,

    /* U+0045 "E" */
    0x3e, 0x45, 0xc1, 0xc, 0x0, 0x15, 0xc, 0x0,
    0x0, 0xc, 0x0, 0x40, 0xc, 0x48, 0x50, 0xc,
    0x0, 0x40, 0xc, 0x0, 0x0, 0xc, 0x0, 0x5,
    0x3d, 0x55, 0xb3,

    /* U+0046 "F" */
    0x3e, 0x45, 0xa5, 0xc, 0x0, 0x6, 0xc, 0x0,
    0x0, 0xc, 0x0, 0x50, 0xd, 0x56, 0x70, 0xc,
    0x0, 0x50, 0xc, 0x0, 0x0, 0xc, 0x0, 0x0,
    0x4e, 0x20, 0x0,

    /* U+0047 "G" */
    0x3, 0x78, 0xb0, 0xa, 0x0, 0x80, 0x56, 0x0,
    0x20, 0x83, 0x0, 0x0, 0xa2, 0x0, 0x0, 0x93,
    0x2, 0x94, 0x66, 0x0, 0xb0, 0x1a, 0x0, 0xb0,
    0x5, 0x76, 0x70,

    /* U+0048 "H" */
    0x6c, 0x1, 0xc5, 0x29, 0x0, 0xa1, 0x29, 0x0,
    0xa1, 0x29, 0x0, 0xa1, 0x2b, 0x55, 0xc1, 0x29,
    0x0, 0xa1, 0x29, 0x0, 0xa1, 0x29, 0x0, 0xa1,
    0x6c, 0x11, 0xc5,

    /* U+0049 "I" */
    0x49, 0x94, 0x6, 0x60, 0x6, 0x60, 0x6, 0x60,
    0x6, 0x60, 0x6, 0x60, 0x6, 0x60, 0x6, 0x60,
    0x49, 0x94,

    /* U+004A "J" */
    0x0, 0x4b, 0x73, 0x0, 0x9, 0x20, 0x0, 0x9,
    0x20, 0x0, 0x9, 0x20, 0x0, 0x9, 0x20, 0x0,
    0x9, 0x20, 0x0, 0x9, 0x20, 0x0, 0x9, 0x20,
    0x0, 0x9, 0x20, 0x53, 0xa, 0x0, 0x49, 0x54,
    0x0,

    /* U+004B "K" */
    0x4d, 0x22, 0xc2, 0xb, 0x4, 0x20, 0xb, 0x15,
    0x0, 0xb, 0x91, 0x0, 0xe, 0x87, 0x0, 0xc,
    0xc, 0x0, 0xb, 0x8, 0x40, 0xb, 0x1, 0xb0,
    0x4d, 0x21, 0xe5,

    /* U+004C "L" */
    0x2e, 0x30, 0x0, 0xc, 0x0, 0x0, 0xc, 0x0,
    0x0, 0xc, 0x0, 0x0, 0xc, 0x0, 0x0, 0xc,
    0x0, 0x0, 0xc, 0x0, 0x0, 0xc, 0x0, 0x5,
    0x3e, 0x55, 0xa4,

    /* U+004D "M" */
    0x88, 0x0, 0xb7, 0x4a, 0x0, 0xd4, 0x4a, 0x2,
    0xa4, 0x48, 0x24, 0x84, 0x45, 0x55, 0x84, 0x42,
    0x85, 0x84, 0x40, 0xa4, 0x84, 0x40, 0xc1, 0x84,
    0x83, 0x70, 0xb7,

    /* U+004E "N" */
    0x5c, 0x0, 0x76, 0x1b, 0x30, 0x21, 0x14, 0xa0,
    0x21, 0x12, 0xa2, 0x21, 0x12, 0x39, 0x21, 0x12,
    0xb, 0x41, 0x12, 0x4, 0xb1, 0x12, 0x0, 0xd1,
    0x67, 0x0, 0x61,

    /* U+004F "O" */
    0x4, 0x76, 0x50, 0xa, 0x0, 0x91, 0x57, 0x0,
    0x66, 0x75, 0x0, 0x49, 0x85, 0x0, 0x3a, 0x75,
    0x0, 0x49, 0x47, 0x0, 0x56, 0xa, 0x0, 0x91,
    0x3, 0x66, 0x50,

    /* U+0050 "P" */
    0x3d, 0x45, 0x70, 0xc, 0x0, 0x65, 0xc, 0x0,
    0x47, 0xc, 0x0, 0x93, 0xd, 0x56, 0x40, 0xc,
    0x0, 0x0, 0xc, 0x0, 0x0, 0xc, 0x0, 0x0,
    0x4e, 0x20, 0x0,

    /* U+0051 "Q" */
    0x5, 0x76, 0x40, 0xa, 0x0, 0xa1, 0x66, 0x0,
    0x66, 0x84, 0x0, 0x59, 0x94, 0x0, 0x49, 0x94,
    0x0, 0x48, 0x67, 0x84, 0x66, 0x1e, 0xb, 0x91,
    0x5, 0x6b, 0x70, 0x0, 0x2, 0xc3,

    /* U+0052 "R" */
    0x3e, 0x56, 0x80, 0xc, 0x0, 0xa2, 0xc, 0x0,
    0x83, 0xc, 0x0, 0xb0, 0xc, 0x4a, 0x20, 0xc,
    0xb, 0x0, 0xc, 0x6, 0x50, 0xc, 0x1, 0xa0,
    0x3e, 0x20, 0xb4,

    /* U+0053 "S" */
    0x6, 0x58, 0xd0, 0x35, 0x0, 0x70, 0x55, 0x0,
    0x0, 0x1c, 0x60, 0x0, 0x0, 0x8c, 0x30, 0x0,
    0x2, 0xc0, 0x20, 0x0, 0x64, 0x53, 0x0, 0x71,
    0x3e, 0x75, 0x50,

    /* U+0054 "T" */
    0x58, 0xa9, 0x84, 0x50, 0x65, 0x5, 0x0, 0x65,
    0x0, 0x0, 0x65, 0x0, 0x0, 0x65, 0x0, 0x0,
    0x65, 0x0, 0x0, 0x65, 0x0, 0x0, 0x65, 0x0,
    0x1, 0xa9, 0x0,

    /* U+0055 "U" */
    0x6c, 0x10, 0x84, 0x29, 0x0, 0x40, 0x29, 0x0,
    0x40, 0x29, 0x0, 0x40, 0x29, 0x0, 0x40, 0x29,
    0x0, 0x40, 0x29, 0x0, 0x40, 0xa, 0x0, 0x50,
    0x6, 0x76, 0x30,

    /* U+0056 "V" */
    0x6d, 0x0, 0xa4, 0xc, 0x0, 0x50, 0xb, 0x0,
    0x50, 0x8, 0x31, 0x40, 0x5, 0x74, 0x0, 0x1,
    0xa5, 0x0, 0x0, 0xc5, 0x0, 0x0, 0xa4, 0x0,
    0x0, 0x61, 0x0,

    /* U+0057 "W" */
    0x96, 0x78, 0x38, 0x55, 0x37, 0x13, 0x37, 0x39,
    0x31, 0x9, 0x5a, 0x50, 0xa, 0x5a, 0x50, 0xa,
    0x5a, 0x50, 0x9, 0x58, 0x60, 0x8, 0x36, 0x40,
    0x6, 0x4, 0x20,

    /* U+0058 "X" */
    0x2e, 0x21, 0xb2, 0x8, 0x30, 0x50, 0x2, 0x95,
    0x0, 0x0, 0xa6, 0x0, 0x0, 0x66, 0x0, 0x0,
    0x6b, 0x0, 0x1, 0x48, 0x30, 0x6, 0x2, 0xa0,
    0x4b, 0x1, 0xe4,

    /* U+0059 "Y" */
    0x5d, 0x10, 0xb4, 0xb, 0x0, 0x50, 0x7, 0x41,
    0x40, 0x1, 0x95, 0x0, 0x0, 0xb5, 0x0, 0x0,
    0x75, 0x0, 0x0, 0x65, 0x0, 0x0, 0x65, 0x0,
    0x1, 0xa9, 0x0,

    /* U+005A "Z" */
    0xc, 0x64, 0xb3, 0x32, 0x1, 0xb0, 0x0, 0x7,
    0x40, 0x0, 0xb, 0x0, 0x0, 0x75, 0x0, 0x0,
    0xc0, 0x0, 0x6, 0x60, 0x0, 0xc, 0x0, 0x24,
    0x5a, 0x55, 0xc0,

    /* U+005B "[" */
    0x67, 0x71, 0x70, 0x0, 0x70, 0x0, 0x70, 0x0,
    0x70, 0x0, 0x70, 0x0, 0x70, 0x0, 0x70, 0x0,
    0x70, 0x0, 0x70, 0x0, 0x70, 0x0, 0x67, 0x71,

    /* U+005C "\\" */
    0x8, 0x0, 0x0, 0x8, 0x0, 0x0, 0x3, 0x50,
    0x0, 0x0, 0x80, 0x0, 0x0, 0x71, 0x0, 0x0,
    0x17, 0x0, 0x0, 0x9, 0x0, 0x0, 0x6, 0x20,
    0x0, 0x1, 0x70, 0x0, 0x0, 0x80, 0x0, 0x0,
    0x44,

    /* U+005D "]" */
    0x17, 0x76, 0x0, 0x7, 0x0, 0x7, 0x0, 0x7,
    0x0, 0x7, 0x0, 0x7, 0x0, 0x7, 0x0, 0x7,
    0x0, 0x7, 0x0, 0x7, 0x0, 0x7, 0x17, 0x76,

    /* U+005E "^" */
    0x1a, 0xa0, 0x20, 0x2,

    /* U+005F "_" */
    0x55, 0x55, 0x54,

    /* U+0060 "`" */
    0x6, 0xa0, 0x0, 0x1,

    /* U+0061 "a" */
    0x7, 0x55, 0x50, 0x17, 0x0, 0xa0, 0x2, 0x65,
    0xb0, 0x2a, 0x0, 0xa0, 0x66, 0x0, 0xa1, 0x19,
    0x55, 0xa5,

    /* U+0062 "b" */
    0x2, 0x0, 0x0, 0x4a, 0x0, 0x0, 0xa, 0x0,
    0x0, 0xa, 0x0, 0x0, 0xa, 0x66, 0x70, 0xd,
    0x0, 0x92, 0xa, 0x0, 0x74, 0xa, 0x0, 0x74,
    0xb, 0x0, 0x91, 0xa, 0x56, 0x50,

    /* U+0063 "c" */
    0x3, 0x65, 0x60, 0xa, 0x0, 0xb0, 0x38, 0x0,
    0x0, 0x37, 0x0, 0x0, 0xa, 0x0, 0x31, 0x5,
    0x86, 0x50,

    /* U+0064 "d" */
    0x0, 0x0, 0x20, 0x0, 0x2, 0xc0, 0x0, 0x0,
    0xa0, 0x0, 0x0, 0xa0, 0x4, 0x76, 0xc0, 0xa,
    0x0, 0xa0, 0x38, 0x0, 0xa0, 0x37, 0x0, 0xa0,
    0x19, 0x0, 0xc0, 0x5, 0x76, 0xa3,

    /* U+0065 "e" */
    0x3, 0x66, 0x60, 0x8, 0x0, 0x91, 0x2a, 0x55,
    0x72, 0x29, 0x0, 0x0, 0xb, 0x0, 0x40, 0x4,
    0x86, 0x40,

    /* U+0066 "f" */
    0x0, 0x35, 0x66, 0x0, 0x90, 0x14, 0x0, 0xa0,
    0x0, 0x14, 0xc4, 0x30, 0x0, 0xa0, 0x0, 0x0,
    0xa0, 0x0, 0x0, 0xa0, 0x0, 0x0, 0xa0, 0x0,
    0x5, 0xd5, 0x10,

    /* U+0067 "g" */
    0x5, 0x57, 0x87, 0xa, 0x0, 0xa0, 0x9, 0x0,
    0x80, 0x7, 0x55, 0x0, 0xb, 0x85, 0x10, 0xa,
    0x7a, 0xd2, 0x45, 0x0, 0x53, 0x7, 0x54, 0x60,

    /* U+0068 "h" */
    0x3a, 0x0, 0x0, 0xa, 0x0, 0x0, 0xa, 0x0,
    0x0, 0xb, 0x77, 0x70, 0xc, 0x0, 0xa0, 0xa,
    0x0, 0xa0, 0xa, 0x0, 0xa0, 0xa, 0x0, 0xa0,
    0x3d, 0x11, 0xd3,

    /* U+0069 "i" */
    0x6, 0x80, 0x0, 0x10, 0x1, 0x10, 0x39, 0x50,
    0x5, 0x50, 0x5, 0x50, 0x5, 0x50, 0x5, 0x50,
    0x39, 0x93,

    /* U+006A "j" */
    0x0, 0x3, 0xb0, 0x0, 0x2, 0x0, 0x0, 0x20,
    0x1, 0x5a, 0x0, 0x0, 0xa0, 0x0, 0xa, 0x0,
    0x0, 0xa0, 0x0, 0xa, 0x0, 0x0, 0x90, 0x30,
    0x27, 0xb, 0x66, 0x0,

    /* U+006B "k" */
    0x3b, 0x0, 0x0, 0xa, 0x0, 0x0, 0xa, 0x0,
    0x0, 0xa, 0x6, 0x81, 0xa, 0x7, 0x0, 0xa,
    0x95, 0x0, 0xc, 0x1b, 0x0, 0xa, 0x5, 0x70,
    0x3d, 0x21, 0xe3,

    /* U+006C "l" */
    0x39, 0x50, 0x5, 0x50, 0x5, 0x50, 0x5, 0x50,
    0x5, 0x50, 0x5, 0x50, 0x5, 0x50, 0x5, 0x50,
    0x49, 0x94,

    /* U+006D "m" */
    0x98, 0x97, 0x83, 0x65, 0x46, 0x27, 0x64, 0x46,
    0x27, 0x64, 0x46, 0x27, 0x64, 0x46, 0x27, 0x97,
    0x78, 0x5a,

    /* U+006E "n" */
    0x3b, 0x66, 0x70, 0xc, 0x0, 0xa0, 0xa, 0x0,
    0xa0, 0xa, 0x0, 0xa0, 0xa, 0x0, 0xa0, 0x3d,
    0x11, 0xd3,

    /* U+006F "o" */
    0x4, 0x66, 0x50, 0x19, 0x0, 0x91, 0x55, 0x0,
    0x55, 0x55, 0x0, 0x55, 0x18, 0x0, 0x81, 0x5,
    0x55, 0x40,

    /* U+0070 "p" */
    0x4b, 0x66, 0x70, 0xc, 0x0, 0x83, 0xa, 0x0,
    0x56, 0xa, 0x0, 0x55, 0xb, 0x0, 0x92, 0xc,
    0x56, 0x60, 0xa, 0x0, 0x0, 0x3d, 0x20, 0x0,

    /* U+0071 "q" */
    0x5, 0x65, 0x90, 0x19, 0x0, 0xb0, 0x56, 0x0,
    0xa0, 0x56, 0x0, 0xa0, 0x28, 0x0, 0xb0, 0x6,
    0x66, 0xb0, 0x0, 0x0, 0xa0, 0x0, 0x2, 0xd3,

    /* U+0072 "r" */
    0x39, 0x66, 0x95, 0x4, 0xb0, 0x11, 0x4, 0x60,
    0x0, 0x4, 0x60, 0x0, 0x4, 0x60, 0x0, 0x38,
    0xa4, 0x0,

    /* U+0073 "s" */
    0x4, 0x56, 0xc0, 0x9, 0x0, 0x50, 0x6, 0xb4,
    0x0, 0x0, 0x18, 0xa0, 0x6, 0x0, 0x80, 0xd,
    0x64, 0x60,

    /* U+0074 "t" */
    0x0, 0x20, 0x0, 0x1, 0x90, 0x0, 0x14, 0xc4,
    0x30, 0x0, 0xa0, 0x0, 0x0, 0xa0, 0x0, 0x0,
    0xa0, 0x0, 0x0, 0xa0, 0x20, 0x0, 0x67, 0x50,

    /* U+0075 "u" */
    0x4a, 0x2, 0xc0, 0xa, 0x0, 0xa0, 0xa, 0x0,
    0xa0, 0xa, 0x0, 0xa0, 0xa, 0x0, 0xc0, 0x8,
    0x86, 0xa3,

    /* U+0076 "v" */
    0x3d, 0x21, 0xb2, 0x9, 0x10, 0x50, 0x4, 0x62,
    0x20, 0x0, 0xa5, 0x0, 0x0, 0xa5, 0x0, 0x0,
    0x63, 0x0,

    /* U+0077 "w" */
    0x97, 0x88, 0x48, 0x36, 0x47, 0x31, 0x9, 0x49,
    0x40, 0x9, 0x49, 0x40, 0x8, 0x48, 0x50, 0x5,
    0x24, 0x30,

    /* U+0078 "x" */
    0x1b, 0x72, 0xa1, 0x1, 0xb6, 0x10, 0x0, 0x87,
    0x0, 0x0, 0x7a, 0x0, 0x3, 0x36, 0x40, 0x3b,
    0x13, 0xd2,

    /* U+0079 "y" */
    0x2d, 0x21, 0xb3, 0x8, 0x20, 0x60, 0x2, 0x73,
    0x20, 0x0, 0x96, 0x0, 0x0, 0x76, 0x0, 0x0,
    0x34, 0x0, 0x0, 0x50, 0x0, 0xc, 0x40, 0x0,

    /* U+007A "z" */
    0xc, 0x46, 0xa0, 0x13, 0xa, 0x10, 0x0, 0x47,
    0x0, 0x0, 0xb0, 0x0, 0x7, 0x40, 0x51, 0x1d,
    0x55, 0xc0,

    /* U+007B "{" */
    0x0, 0x60, 0x5, 0x10, 0x6, 0x0, 0x6, 0x0,
    0x6, 0x0, 0x16, 0x0, 0x16, 0x0, 0x7, 0x0,
    0x6, 0x0, 0x6, 0x0, 0x5, 0x10, 0x0, 0x60,

    /* U+007C "|" */
    0x31, 0x31, 0x31, 0x31, 0x31, 0x31, 0x31, 0x31,
    0x31, 0x31, 0x31, 0x31, 0x31,

    /* U+007D "}" */
    0x5, 0x0, 0x1, 0x50, 0x0, 0x60, 0x0, 0x60,
    0x0, 0x60, 0x0, 0x61, 0x0, 0x61, 0x0, 0x70,
    0x0, 0x60, 0x0, 0x60, 0x1, 0x50, 0x6, 0x0,

    /* U+007E "~" */
    0x6, 0x60, 0x0, 0x40, 0x57, 0x4, 0x0, 0x5,
    0x70,

    /* U+2103 "℃" */
    0x26, 0x40, 0x17, 0x74, 0x0, 0x52, 0x62, 0xa1,
    0x1, 0x86, 0x3, 0xb, 0x10, 0x0, 0x16, 0x0,
    0x1b, 0x0, 0x0, 0x3, 0x0, 0x39, 0x0, 0x0,
    0x0, 0x0, 0x2a, 0x0, 0x0, 0x0, 0x0, 0xd,
    0x0, 0x0, 0x0, 0x0, 0x6, 0x70, 0x0, 0x51,
    0x0, 0x0, 0x6a, 0x88, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+4E1C "东" */
    0x0, 0x0, 0x8, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x2b, 0x0, 0x2, 0x10, 0x4, 0x44, 0xb6, 0x44,
    0x45, 0x40, 0x0, 0x2, 0x90, 0x40, 0x0, 0x0,
    0x0, 0xa, 0x10, 0xb0, 0x0, 0x0, 0x0, 0x98,
    0x44, 0xc4, 0x49, 0x0, 0x0, 0x10, 0x50, 0xa1,
    0x0, 0x0, 0x0, 0x8, 0x80, 0xa0, 0x73, 0x0,
    0x0, 0x55, 0x0, 0xa0, 0xa, 0x60, 0x4, 0x30,
    0x0, 0xa0, 0x0, 0xb0, 0x0, 0x0, 0x4c, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5317 "北" */
    0x0, 0x0, 0x80, 0x6, 0x0, 0x0, 0x0, 0x0,
    0xa0, 0xb, 0x0, 0x0, 0x0, 0x0, 0xa0, 0xa,
    0x0, 0x10, 0x4, 0x44, 0xa0, 0xa, 0x6, 0xa0,
    0x0, 0x0, 0xa0, 0xb, 0x64, 0x0, 0x0, 0x0,
    0xa0, 0xb, 0x0, 0x0, 0x0, 0x0, 0xa0, 0xa,
    0x0, 0x0, 0x0, 0x0, 0xa0, 0xa, 0x0, 0x10,
    0x0, 0x35, 0xa0, 0xa, 0x0, 0x50, 0x1c, 0x50,
    0xa0, 0xa, 0x0, 0x80, 0x0, 0x0, 0xb0, 0xb,
    0x99, 0xd2, 0x0, 0x0, 0x30, 0x0, 0x0, 0x0,

    /* U+5357 "南" */
    0x0, 0x0, 0x5, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x7, 0x40, 0x0, 0x61, 0x5, 0x44, 0x4a, 0x54,
    0x44, 0x43, 0x0, 0x64, 0x4b, 0x54, 0x44, 0x60,
    0x0, 0xa0, 0x40, 0x9, 0x10, 0x90, 0x0, 0x90,
    0x73, 0x46, 0x0, 0x90, 0x0, 0x93, 0x57, 0x74,
    0x81, 0x90, 0x0, 0x94, 0x49, 0x64, 0x83, 0x90,
    0x0, 0x91, 0x7, 0x20, 0x0, 0x90, 0x0, 0xa0,
    0x7, 0x20, 0x34, 0x80, 0x0, 0x80, 0x4, 0x10,
    0x2b, 0x30,

    /* U+5929 "天" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x54,
    0x49, 0x44, 0x67, 0x0, 0x0, 0x0, 0xb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0x0, 0x0, 0x0,
    0x4, 0x44, 0x4c, 0x44, 0x4a, 0x70, 0x1, 0x0,
    0xa, 0x40, 0x0, 0x0, 0x0, 0x0, 0x55, 0x41,
    0x0, 0x0, 0x0, 0x0, 0xa0, 0x8, 0x0, 0x0,
    0x0, 0x6, 0x40, 0x4, 0x80, 0x0, 0x0, 0x54,
    0x0, 0x0, 0x6c, 0x50, 0x15, 0x20, 0x0, 0x0,
    0x4, 0x81, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5DDE "州" */
    0x0, 0x1, 0x0, 0x0, 0x3, 0x10, 0x0, 0xb0,
    0x8, 0x10, 0x73, 0x0, 0x9, 0x0, 0xa0, 0x7,
    0x20, 0x0, 0xa0, 0x9, 0x0, 0x72, 0x4, 0x1a,
    0x52, 0x92, 0x57, 0x20, 0xb1, 0x92, 0x89, 0xa,
    0x72, 0x4, 0x27, 0x1, 0x90, 0x7, 0x20, 0x6,
    0x40, 0x9, 0x0, 0x72, 0x0, 0xa0, 0x0, 0xa0,
    0x7, 0x20, 0x35, 0x0, 0x8, 0x0, 0x72, 0x15,
    0x0, 0x0, 0x0, 0x7, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+5E95 "底" */
    0x0, 0x0, 0x3, 0x20, 0x0, 0x0, 0x0, 0x20,
    0x0, 0xb0, 0x1, 0x50, 0x0, 0xc4, 0x44, 0x44,
    0x45, 0x40, 0x0, 0xb1, 0x1, 0x47, 0xb9, 0x0,
    0x0, 0xb3, 0x83, 0x75, 0x0, 0x0, 0x0, 0xb3,
    0x60, 0x37, 0x2, 0x30, 0x0, 0xa3, 0x94, 0x5c,
    0x44, 0x30, 0x2, 0x73, 0x60, 0xb, 0x0, 0x0,
    0x5, 0x33, 0x63, 0x55, 0x80, 0x30, 0x7, 0x4,
    0xd6, 0x60, 0xa7, 0x70, 0x13, 0x0, 0x20, 0x64,
    0x7, 0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5EA6 "度" */
    0x0, 0x0, 0x1, 0x70, 0x0, 0x0, 0x0, 0x74,
    0x44, 0xa5, 0x44, 0xa1, 0x0, 0xb0, 0x7, 0x0,
    0x70, 0x0, 0x0, 0xb4, 0x4b, 0x44, 0xb5, 0x90,
    0x0, 0xa0, 0x9, 0x0, 0x90, 0x0, 0x1, 0x90,
    0xb, 0x44, 0xa0, 0x0, 0x2, 0x80, 0x45, 0x44,
    0x64, 0x0, 0x4, 0x50, 0x5, 0x0, 0xb2, 0x0,
    0x7, 0x10, 0x0, 0x79, 0x30, 0x0, 0x6, 0x0,
    0x3, 0x9a, 0x50, 0x0, 0x21, 0x24, 0x64, 0x0,
    0x6b, 0xb1, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+65E5 "日" */
    0x10, 0x0, 0x0, 0x20, 0xa4, 0x44, 0x44, 0xb2,
    0xa0, 0x0, 0x0, 0xa0, 0xa0, 0x0, 0x0, 0xa0,
    0xa0, 0x0, 0x0, 0xa0, 0xa4, 0x44, 0x44, 0xb0,
    0xa0, 0x0, 0x0, 0xa0, 0xa0, 0x0, 0x0, 0xa0,
    0xa0, 0x0, 0x0, 0xa0, 0xa4, 0x44, 0x44, 0xb0,
    0x70, 0x0, 0x0, 0x60,

    /* U+661F "星" */
    0x0, 0x54, 0x44, 0x44, 0x71, 0x0, 0x9, 0x10,
    0x0, 0xa, 0x0, 0x0, 0x95, 0x44, 0x44, 0xc0,
    0x0, 0x9, 0x10, 0x0, 0xa, 0x0, 0x0, 0x74,
    0x47, 0x44, 0x90, 0x0, 0x7, 0x40, 0xa1, 0x1,
    0x10, 0x0, 0xb4, 0x4c, 0x44, 0x65, 0x0, 0x62,
    0x0, 0xa0, 0x3, 0x0, 0x12, 0x34, 0x4c, 0x44,
    0x70, 0x0, 0x0, 0x0, 0xa0, 0x0, 0x30, 0x25,
    0x44, 0x46, 0x44, 0x47, 0x40,

    /* U+671F "期" */
    0x0, 0x50, 0x6, 0x0, 0x0, 0x10, 0x0, 0xa0,
    0xa, 0xa, 0x44, 0xc0, 0x4, 0xc4, 0x4c, 0x6a,
    0x0, 0xa0, 0x0, 0xa4, 0x4a, 0xa, 0x44, 0xb0,
    0x0, 0xa0, 0xa, 0xa, 0x0, 0xa0, 0x0, 0xa4,
    0x4a, 0xa, 0x0, 0xa0, 0x0, 0xa0, 0xa, 0x1b,
    0x44, 0xb0, 0x5, 0x75, 0x47, 0x5a, 0x0, 0xa0,
    0x0, 0x96, 0x37, 0x27, 0x0, 0xa0, 0x4, 0x60,
    0x7, 0x80, 0x0, 0xa0, 0x14, 0x0, 0x4, 0x20,
    0x18, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+68C0 "检" */
    0x0, 0x71, 0x0, 0x37, 0x0, 0x0, 0x0, 0xa0,
    0x0, 0x87, 0x0, 0x0, 0x0, 0xa0, 0x21, 0x90,
    0x80, 0x0, 0x24, 0xd4, 0x48, 0x10, 0x3c, 0x51,
    0x0, 0xf4, 0x55, 0x44, 0x65, 0x60, 0x4, 0xf7,
    0x50, 0x10, 0x5, 0x0, 0x7, 0xa0, 0x21, 0x34,
    0xb, 0x0, 0x23, 0xa0, 0x8, 0xb, 0x36, 0x0,
    0x10, 0xa0, 0x9, 0x16, 0x61, 0x0, 0x0, 0xb0,
    0x3, 0x0, 0x60, 0x20, 0x0, 0xb1, 0x54, 0x44,
    0x54, 0x70, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+6C14 "气" */
    0x0, 0x8, 0x10, 0x0, 0x0, 0x0, 0x0, 0x2a,
    0x0, 0x0, 0x5, 0x10, 0x0, 0x85, 0x44, 0x44,
    0x44, 0x20, 0x2, 0x63, 0x44, 0x44, 0x83, 0x0,
    0x6, 0x0, 0x0, 0x0, 0x40, 0x0, 0x10, 0x44,
    0x44, 0x44, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa0, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x57, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,

    /* U+6D4B "测" */
    0x2, 0x0, 0x10, 0x1, 0x0, 0x50, 0x0, 0xb0,
    0xb4, 0x4a, 0x0, 0xa0, 0x0, 0x20, 0x92, 0x9,
    0x55, 0x90, 0x27, 0x2, 0x99, 0x29, 0x54, 0x90,
    0x8, 0x4, 0x99, 0x19, 0x54, 0x90, 0x0, 0x23,
    0x99, 0x9, 0x54, 0x90, 0x0, 0x60, 0x99, 0x9,
    0x54, 0x90, 0x6, 0x90, 0x89, 0x5, 0x54, 0x90,
    0x3, 0x80, 0x19, 0x40, 0x21, 0x90, 0x4, 0x70,
    0x80, 0x74, 0x0, 0x90, 0x1, 0x45, 0x10, 0x4,
    0x19, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+6E29 "温" */
    0x1, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0xb0,
    0x39, 0x44, 0x4c, 0x0, 0x0, 0x11, 0x46, 0x0,
    0xa, 0x0, 0x15, 0x4, 0x39, 0x44, 0x4b, 0x0,
    0xa, 0x5, 0x39, 0x44, 0x4b, 0x0, 0x0, 0x15,
    0x23, 0x0, 0x4, 0x0, 0x0, 0x61, 0x84, 0x64,
    0x64, 0x80, 0x3, 0xa0, 0x90, 0x90, 0x90, 0x90,
    0x5, 0xa0, 0x90, 0x90, 0x90, 0x90, 0x4, 0x90,
    0x90, 0x90, 0x90, 0x90, 0x2, 0x74, 0xb4, 0xa4,
    0xa4, 0xb8, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,

    /* U+7A7A "空" */
    0x0, 0x0, 0x6, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x70, 0x0, 0x0, 0x6, 0x44, 0x54, 0x44,
    0x47, 0x80, 0x1c, 0x4, 0xb0, 0x5, 0x45, 0x0,
    0x0, 0x48, 0x0, 0x0, 0x5c, 0x10, 0x5, 0x30,
    0x0, 0x0, 0x43, 0x30, 0x0, 0x45, 0x4a, 0x54,
    0x51, 0x0, 0x0, 0x0, 0x9, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x9, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x9, 0x10, 0x0, 0x0, 0x14, 0x44, 0x4b, 0x54,
    0x48, 0x80, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+897F "西" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x5, 0x44,
    0x47, 0x48, 0x44, 0x91, 0x0, 0x0, 0x18, 0x9,
    0x0, 0x0, 0x0, 0x64, 0x5a, 0x4b, 0x44, 0x70,
    0x0, 0x90, 0x18, 0x9, 0x0, 0x90, 0x0, 0x90,
    0x36, 0x9, 0x0, 0x90, 0x0, 0x90, 0x72, 0x9,
    0x0, 0x90, 0x0, 0x91, 0x70, 0x9, 0x95, 0x90,
    0x0, 0xa4, 0x0, 0x0, 0x0, 0x90, 0x0, 0xb4,
    0x44, 0x44, 0x44, 0xa0, 0x0, 0x70, 0x0, 0x0,
    0x0, 0x70,

    /* U+8D28 "质" */
    0x0, 0x0, 0x0, 0x2, 0x67, 0x0, 0x8, 0x34,
    0x47, 0x53, 0x10, 0x0, 0x90, 0x0, 0xa1, 0x2,
    0x0, 0x9, 0x44, 0x4b, 0x44, 0x63, 0x0, 0x90,
    0x64, 0xb4, 0x47, 0x0, 0x9, 0xa, 0x3, 0x0,
    0x90, 0x0, 0x90, 0x90, 0xa0, 0x9, 0x0, 0x36,
    0x9, 0xa, 0x0, 0xa0, 0x7, 0x10, 0x73, 0x82,
    0x5, 0x0, 0x60, 0x2, 0x90, 0x39, 0x80, 0x20,
    0x15, 0x50, 0x0, 0x7, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+90D1 "郑" */
    0x0, 0x0, 0x3, 0x0, 0x0, 0x0, 0x0, 0xa0,
    0x56, 0x7, 0x44, 0xa0, 0x0, 0x40, 0x62, 0x29,
    0x4, 0x70, 0x5, 0x4a, 0x54, 0x39, 0x7, 0x0,
    0x0, 0x9, 0x10, 0x9, 0x4, 0x0, 0x15, 0x4b,
    0x56, 0x79, 0x6, 0x0, 0x0, 0xa, 0x0, 0x9,
    0x3, 0x50, 0x0, 0x1b, 0x60, 0x9, 0x0, 0xb0,
    0x0, 0x72, 0x2d, 0x19, 0x49, 0x90, 0x3, 0x50,
    0x4, 0x39, 0x5, 0x0, 0x23, 0x0, 0x0, 0x9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+91CF "量" */
    0x0, 0x34, 0x44, 0x44, 0x70, 0x0, 0x0, 0x74,
    0x22, 0x22, 0xa0, 0x0, 0x0, 0x73, 0x22, 0x22,
    0xa0, 0x0, 0x0, 0x65, 0x44, 0x44, 0x70, 0x20,
    0x34, 0x44, 0x44, 0x44, 0x44, 0x71, 0x0, 0x94,
    0x49, 0x44, 0xa2, 0x0, 0x0, 0x94, 0x4b, 0x44,
    0xa1, 0x0, 0x0, 0xa4, 0x4b, 0x44, 0xa1, 0x0,
    0x0, 0x30, 0x9, 0x0, 0x24, 0x0, 0x2, 0x54,
    0x4b, 0x44, 0x44, 0x0, 0x14, 0x44, 0x4b, 0x44,
    0x45, 0xc0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+98CE "风" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x94,
    0x44, 0x44, 0xa2, 0x0, 0x0, 0xa0, 0x0, 0x0,
    0x90, 0x0, 0x0, 0xa1, 0x0, 0x67, 0x90, 0x0,
    0x0, 0xa0, 0x51, 0xa0, 0x90, 0x0, 0x0, 0xa0,
    0xa, 0x60, 0x81, 0x0, 0x0, 0xa0, 0x8, 0xa0,
    0x82, 0x0, 0x0, 0x90, 0x70, 0x58, 0x54, 0x0,
    0x2, 0x65, 0x10, 0x8, 0x19, 0x13, 0x6, 0x20,
    0x0, 0x0, 0x9, 0xa2, 0x13, 0x0, 0x0, 0x0,
    0x0, 0x93, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+9AD8 "高" */
    0x0, 0x0, 0x17, 0x0, 0x0, 0x0, 0x2, 0x22,
    0x28, 0x32, 0x23, 0x60, 0x3, 0x13, 0x11, 0x12,
    0x31, 0x10, 0x0, 0xc, 0x44, 0x47, 0x50, 0x0,
    0x0, 0xc, 0x44, 0x47, 0x50, 0x0, 0x2, 0x14,
    0x11, 0x12, 0x24, 0x0, 0x9, 0x44, 0x33, 0x35,
    0x3b, 0x10, 0x8, 0x15, 0x84, 0x4b, 0xa, 0x0,
    0x8, 0x14, 0x50, 0xa, 0xa, 0x0, 0x8, 0x14,
    0x74, 0x48, 0xa, 0x0, 0x8, 0x10, 0x0, 0x0,
    0x6d, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x12, 0x0, 0x0,
    0x0, 0x3, 0x7c, 0xff, 0x0, 0x0, 0x59, 0xef,
    0xff, 0xff, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xf, 0xff, 0xfd, 0x84, 0x8f, 0x0, 0xf,
    0xd7, 0x20, 0x0, 0x8f, 0x0, 0xf, 0x80, 0x0,
    0x0, 0x8f, 0x0, 0xf, 0x80, 0x0, 0x0, 0x8f,
    0x0, 0xf, 0x80, 0x0, 0x7b, 0xdf, 0x2, 0x3f,
    0x80, 0x6, 0xff, 0xff, 0xaf, 0xff, 0x80, 0x2,
    0xef, 0xf9, 0xef, 0xff, 0x60, 0x0, 0x2, 0x10,
    0x29, 0xa7, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0xb4, 0xdf, 0xff, 0xff, 0xfd, 0x4b, 0xe8, 0xe7,
    0x22, 0x22, 0x7e, 0x8e, 0xc0, 0xc5, 0x0, 0x0,
    0x6c, 0xc, 0xfc, 0xf6, 0x11, 0x11, 0x7f, 0xcf,
    0xc0, 0xcf, 0xff, 0xff, 0xfb, 0xc, 0xfc, 0xf6,
    0x11, 0x11, 0x7f, 0xcf, 0xc0, 0xc5, 0x0, 0x0,
    0x6c, 0xc, 0xe8, 0xe7, 0x22, 0x22, 0x7e, 0x8e,
    0xb4, 0xdf, 0xff, 0xff, 0xfd, 0x4b,

    /* U+F00B "" */
    0xdf, 0xf6, 0x9f, 0xff, 0xff, 0xfd, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xef, 0xf6, 0xaf, 0xff,
    0xff, 0xfe, 0x13, 0x20, 0x3, 0x33, 0x33, 0x31,
    0xff, 0xf7, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xbf, 0xff,
    0xff, 0xff, 0x13, 0x20, 0x3, 0x33, 0x33, 0x31,
    0xef, 0xf6, 0xaf, 0xff, 0xff, 0xfe, 0xff, 0xf8,
    0xcf, 0xff, 0xff, 0xff, 0xdf, 0xf6, 0xaf, 0xff,
    0xff, 0xfd,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xf4, 0x4d, 0x30, 0x0, 0x3f, 0xff, 0x40,
    0xef, 0xf3, 0x3, 0xff, 0xf4, 0x0, 0x4f, 0xff,
    0x6f, 0xff, 0x40, 0x0, 0x4, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x3, 0xd3, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x14, 0x0, 0x0, 0x22, 0xd, 0xf7, 0x0, 0x4f,
    0xf1, 0x9f, 0xf7, 0x4f, 0xfd, 0x0, 0xaf, 0xff,
    0xfd, 0x10, 0x0, 0xbf, 0xfe, 0x10, 0x0, 0x4f,
    0xff, 0xf7, 0x0, 0x4f, 0xfd, 0xaf, 0xf7, 0xe,
    0xfd, 0x10, 0xaf, 0xf2, 0x5b, 0x10, 0x0, 0x99,
    0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x7, 0x70, 0x0, 0x0, 0x0, 0x32,
    0xf, 0xf0, 0x24, 0x0, 0x5, 0xfc, 0xf, 0xf0,
    0xcf, 0x50, 0x1f, 0xf4, 0xf, 0xf0, 0x5f, 0xf1,
    0x7f, 0x80, 0xf, 0xf0, 0x8, 0xf7, 0xbf, 0x20,
    0xf, 0xf0, 0x2, 0xfb, 0xcf, 0x10, 0xe, 0xe0,
    0x1, 0xfc, 0xaf, 0x40, 0x1, 0x10, 0x4, 0xfa,
    0x5f, 0xb0, 0x0, 0x0, 0xb, 0xf6, 0xd, 0xfa,
    0x10, 0x1, 0xaf, 0xd0, 0x2, 0xdf, 0xfc, 0xcf,
    0xfd, 0x20, 0x0, 0x8, 0xef, 0xfe, 0x91, 0x0,
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x14, 0x41, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf7, 0x0, 0x0, 0x3, 0x43, 0xdf, 0xfd,
    0x34, 0x30, 0xe, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0x6f, 0xff, 0xfb, 0xbf, 0xff, 0xf6, 0x1b, 0xff,
    0x70, 0x7, 0xff, 0xb1, 0x7, 0xff, 0x20, 0x2,
    0xff, 0x70, 0x1b, 0xff, 0x70, 0x7, 0xff, 0xb1,
    0x6f, 0xff, 0xfb, 0xbf, 0xff, 0xf6, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x3, 0x42, 0xcf, 0xfc,
    0x23, 0x30, 0x0, 0x0, 0x7f, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x41, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x73, 0x3, 0x83, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0x67, 0xf7, 0x0, 0x0, 0x3,
    0xee, 0x5a, 0xfe, 0xf7, 0x0, 0x0, 0x6f, 0xd3,
    0xb5, 0x7f, 0xf7, 0x0, 0x9, 0xfb, 0x3d, 0xff,
    0x85, 0xfe, 0x30, 0xbf, 0x95, 0xff, 0xff, 0xfb,
    0x3e, 0xf4, 0x76, 0x6f, 0xff, 0xff, 0xff, 0xd2,
    0xa1, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0xcf, 0xfa, 0x2, 0xff, 0xf4, 0x0, 0x0,
    0xcf, 0xfa, 0x2, 0xff, 0xf4, 0x0, 0x0, 0xaf,
    0xf8, 0x1, 0xff, 0xf3, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x27, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x4, 0xff, 0xff, 0x40, 0x0,
    0x23, 0x33, 0x5f, 0xf5, 0x33, 0x32, 0xff, 0xff,
    0xa4, 0x4a, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5c, 0x8f,
    0x9a, 0xaa, 0xaa, 0xaa, 0xaa, 0xa8,

    /* U+F01C "" */
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x1,
    0xed, 0x88, 0x88, 0x89, 0xf8, 0x0, 0xa, 0xf2,
    0x0, 0x0, 0x0, 0xaf, 0x30, 0x5f, 0x70, 0x0,
    0x0, 0x0, 0x1e, 0xc0, 0xef, 0x88, 0x60, 0x0,
    0x28, 0x8b, 0xf6, 0xff, 0xff, 0xf3, 0x0, 0xbf,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,

    /* U+F021 "" */
    0x0, 0x0, 0x1, 0x10, 0x0, 0x59, 0x0, 0x19,
    0xef, 0xfd, 0x70, 0x9f, 0x3, 0xef, 0xda, 0x9d,
    0xfe, 0xbf, 0xe, 0xf6, 0x0, 0x0, 0x5f, 0xff,
    0x7f, 0x70, 0x0, 0x3f, 0xff, 0xff, 0x69, 0x0,
    0x0, 0x2a, 0xaa, 0xa9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaa, 0xaa, 0xa2, 0x0, 0x0, 0xa6,
    0xff, 0xfe, 0xf3, 0x0, 0x7, 0xf7, 0xff, 0xf5,
    0x0, 0x0, 0x7f, 0xe0, 0xfb, 0xef, 0xd9, 0xad,
    0xfe, 0x30, 0xfa, 0x8, 0xef, 0xfe, 0x91, 0x0,
    0x95, 0x0, 0x1, 0x10, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x2a, 0x0, 0x2, 0xef, 0x78, 0x8e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xdf, 0xff, 0xff, 0x0, 0x7, 0xff,
    0x0, 0x0, 0x7f, 0x0, 0x0, 0x1,

    /* U+F027 "" */
    0x0, 0x0, 0x2a, 0x0, 0x0, 0x0, 0x2e, 0xf0,
    0x0, 0x78, 0x8e, 0xff, 0x3, 0xf, 0xff, 0xff,
    0xf0, 0xba, 0xff, 0xff, 0xff, 0x3, 0xff, 0xff,
    0xff, 0xf0, 0xaa, 0xdf, 0xff, 0xff, 0x4, 0x0,
    0x0, 0x8f, 0xf0, 0x0, 0x0, 0x0, 0x8f, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xd2, 0x0, 0x0, 0x0,
    0x2a, 0x0, 0x11, 0x8e, 0x10, 0x0, 0x2, 0xef,
    0x0, 0x7d, 0x2b, 0x90, 0x78, 0x8e, 0xff, 0x3,
    0xa, 0xb3, 0xf0, 0xff, 0xff, 0xff, 0xb, 0xa1,
    0xf1, 0xe3, 0xff, 0xff, 0xff, 0x3, 0xf0, 0xe3,
    0xc5, 0xff, 0xff, 0xff, 0xb, 0xa1, 0xf1, 0xe3,
    0xdf, 0xff, 0xff, 0x3, 0xa, 0xb3, 0xf0, 0x0,
    0x7, 0xff, 0x0, 0x7d, 0x2b, 0x90, 0x0, 0x0,
    0x7f, 0x0, 0x11, 0x9e, 0x10, 0x0, 0x0, 0x1,
    0x0, 0x6, 0xd2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F03E "" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb, 0xfd, 0x5b,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x1, 0xff, 0xff,
    0xef, 0xff, 0xfb, 0x18, 0xff, 0xf6, 0x1c, 0xff,
    0xff, 0xfc, 0xff, 0x60, 0x1, 0xdf, 0xff, 0x60,
    0x96, 0x0, 0x0, 0x8f, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xfc, 0x88, 0x88, 0x88, 0x88, 0xcf,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F048 "" */
    0x58, 0x0, 0x0, 0x35, 0x9f, 0x10, 0x5, 0xfe,
    0x9f, 0x10, 0x6f, 0xfe, 0x9f, 0x17, 0xff, 0xfe,
    0x9f, 0x9f, 0xff, 0xfe, 0x9f, 0xff, 0xff, 0xfe,
    0x9f, 0xef, 0xff, 0xfe, 0x9f, 0x2d, 0xff, 0xfe,
    0x9f, 0x10, 0xcf, 0xfe, 0x9f, 0x10, 0xb, 0xfe,
    0x8f, 0x0, 0x0, 0x9b, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x46, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfd, 0x40,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xa1, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xfd, 0x50, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xb1, 0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xb1, 0xff, 0xff, 0xff, 0xfd,
    0x40, 0xf, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xff,
    0xff, 0xa1, 0x0, 0x0, 0xf, 0xfd, 0x40, 0x0,
    0x0, 0x0, 0x36, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0xaf, 0xfe, 0x30, 0xaf, 0xfe, 0x3f, 0xff, 0xf7,
    0xf, 0xff, 0xf7, 0xff, 0xff, 0x80, 0xff, 0xff,
    0x8f, 0xff, 0xf8, 0xf, 0xff, 0xf8, 0xff, 0xff,
    0x80, 0xff, 0xff, 0x8f, 0xff, 0xf8, 0xf, 0xff,
    0xf8, 0xff, 0xff, 0x80, 0xff, 0xff, 0x8f, 0xff,
    0xf8, 0xf, 0xff, 0xf8, 0xff, 0xff, 0x80, 0xff,
    0xff, 0x8f, 0xff, 0xf7, 0xf, 0xff, 0xf7, 0x48,
    0x98, 0x10, 0x48, 0x98, 0x10,

    /* U+F04D "" */
    0x48, 0x88, 0x88, 0x88, 0x88, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf7, 0xaf,
    0xff, 0xff, 0xff, 0xfe, 0x30,

    /* U+F051 "" */
    0x26, 0x0, 0x0, 0x58, 0x7f, 0xa0, 0x0, 0xbf,
    0x8f, 0xfb, 0x0, 0xbf, 0x8f, 0xff, 0xc1, 0xbf,
    0x8f, 0xff, 0xfd, 0xcf, 0x8f, 0xff, 0xff, 0xff,
    0x8f, 0xff, 0xff, 0xef, 0x8f, 0xff, 0xf4, 0xbf,
    0x8f, 0xff, 0x40, 0xbf, 0x8f, 0xe3, 0x0, 0xbf,
    0x5d, 0x20, 0x0, 0xae, 0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x3, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0xfa, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0x90, 0x0, 0x0, 0x1e, 0xff, 0xff, 0xf8, 0x0,
    0x1, 0xdf, 0xff, 0xff, 0xff, 0x70, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x1, 0x34, 0x44, 0x44, 0x44, 0x30,
    0xd, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xf5,

    /* U+F053 "" */
    0x0, 0x0, 0x3, 0x10, 0x0, 0x5, 0xfb, 0x0,
    0x5, 0xff, 0x40, 0x5, 0xff, 0x40, 0x5, 0xff,
    0x50, 0x3, 0xff, 0x50, 0x0, 0xb, 0xfc, 0x10,
    0x0, 0xb, 0xfc, 0x10, 0x0, 0xc, 0xfc, 0x10,
    0x0, 0xc, 0xfb, 0x0, 0x0, 0xa, 0x50,

    /* U+F054 "" */
    0x3, 0x10, 0x0, 0x3, 0xfc, 0x10, 0x0, 0xb,
    0xfc, 0x10, 0x0, 0xb, 0xfc, 0x10, 0x0, 0xb,
    0xfc, 0x10, 0x0, 0xd, 0xfb, 0x0, 0x5, 0xff,
    0x50, 0x5, 0xff, 0x50, 0x5, 0xff, 0x50, 0x3,
    0xff, 0x50, 0x0, 0xa, 0x50, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x69, 0x10, 0x0, 0x0, 0x0, 0xd,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xef, 0x60, 0x0,
    0x0, 0x0, 0xe, 0xf6, 0x0, 0x0, 0x58, 0x88,
    0xff, 0xb8, 0x88, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x9b, 0xbb, 0xff, 0xdb, 0xbb, 0x30, 0x0,
    0xe, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xef, 0x60,
    0x0, 0x0, 0x0, 0xe, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x9d, 0x20, 0x0, 0x0,

    /* U+F068 "" */
    0x46, 0x66, 0x66, 0x66, 0x66, 0x1f, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xad, 0xdd, 0xdd, 0xdd, 0xdd,
    0x40,

    /* U+F06E "" */
    0x0, 0x3, 0xad, 0xff, 0xc7, 0x0, 0x0, 0x0,
    0x9f, 0xe6, 0x24, 0xaf, 0xe3, 0x0, 0xb, 0xff,
    0x20, 0x77, 0x9, 0xff, 0x40, 0x7f, 0xf9, 0x0,
    0xcf, 0xa1, 0xff, 0xe1, 0xef, 0xf6, 0x7f, 0xff,
    0xf0, 0xef, 0xf7, 0x8f, 0xf9, 0x3f, 0xff, 0xc1,
    0xff, 0xe1, 0xb, 0xff, 0x26, 0xca, 0x19, 0xff,
    0x40, 0x0, 0x9f, 0xe6, 0x24, 0xaf, 0xe3, 0x0,
    0x0, 0x3, 0x9d, 0xff, 0xc7, 0x0, 0x0,

    /* U+F070 "" */
    0x32, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0xf8, 0x4a, 0xef, 0xeb, 0x50, 0x0, 0x0,
    0x0, 0x9f, 0xfd, 0x52, 0x5d, 0xfc, 0x10, 0x0,
    0x0, 0x5, 0xfe, 0x4a, 0x70, 0xcf, 0xe1, 0x0,
    0xb, 0x80, 0x2d, 0xff, 0xf7, 0x4f, 0xfb, 0x0,
    0x2f, 0xfb, 0x0, 0xaf, 0xfb, 0x2f, 0xff, 0x30,
    0xb, 0xff, 0x50, 0x7, 0xfe, 0x7f, 0xfb, 0x0,
    0x1, 0xdf, 0xc0, 0x0, 0x3e, 0xff, 0xe1, 0x0,
    0x0, 0x1b, 0xfc, 0x42, 0x1, 0xbf, 0xa0, 0x0,
    0x0, 0x0, 0x5b, 0xef, 0xb0, 0x8, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x40,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x3, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xfd, 0xef, 0xa0, 0x0, 0x0, 0x0, 0xb,
    0xfb, 0x3, 0xff, 0x30, 0x0, 0x0, 0x4, 0xff,
    0xc0, 0x4f, 0xfc, 0x0, 0x0, 0x0, 0xdf, 0xfd,
    0x5, 0xff, 0xf6, 0x0, 0x0, 0x7f, 0xff, 0xf8,
    0xcf, 0xff, 0xe1, 0x0, 0x1f, 0xff, 0xfc, 0x4,
    0xff, 0xff, 0x90, 0xa, 0xff, 0xff, 0xd2, 0x7f,
    0xff, 0xff, 0x20, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x4, 0x78, 0x88, 0x88, 0x88, 0x88,
    0x87, 0x0,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x6, 0x10, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xc1, 0xff, 0xf8, 0x0, 0x2e,
    0xff, 0xfc, 0xcd, 0xff, 0x62, 0xef, 0xdf, 0xf9,
    0x0, 0x2c, 0x4e, 0xf9, 0xf, 0x90, 0x0, 0x2,
    0xef, 0x90, 0x7, 0x0, 0x0, 0x2e, 0xf8, 0x88,
    0xf, 0xa0, 0xcd, 0xff, 0x80, 0xdf, 0xdf, 0xf9,
    0xff, 0xf8, 0x0, 0x1e, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0x10,

    /* U+F077 "" */
    0x0, 0x0, 0x27, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xf9, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xf9, 0x0,
    0x0, 0x2e, 0xf9, 0x2e, 0xf9, 0x0, 0x2e, 0xf9,
    0x0, 0x2e, 0xf9, 0xb, 0xf9, 0x0, 0x0, 0x2e,
    0xf4, 0x27, 0x0, 0x0, 0x0, 0x27, 0x0,

    /* U+F078 "" */
    0x27, 0x0, 0x0, 0x0, 0x27, 0xb, 0xf9, 0x0,
    0x0, 0x2e, 0xf4, 0x2e, 0xf9, 0x0, 0x2e, 0xf9,
    0x0, 0x2e, 0xf9, 0x2e, 0xf9, 0x0, 0x0, 0x2e,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x2e, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x26, 0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xc0, 0x7, 0x77, 0x77, 0x72, 0x0,
    0x3, 0xff, 0xfc, 0x2e, 0xff, 0xff, 0xf9, 0x0,
    0xf, 0xcf, 0xcf, 0xa0, 0x0, 0x0, 0xe9, 0x0,
    0x4, 0x1e, 0x93, 0x20, 0x0, 0x0, 0xe9, 0x0,
    0x0, 0xe, 0x90, 0x0, 0x0, 0x0, 0xe9, 0x0,
    0x0, 0xe, 0x90, 0x0, 0x0, 0xb5, 0xe9, 0x97,
    0x0, 0xe, 0xc7, 0x77, 0x73, 0xbf, 0xff, 0xf6,
    0x0, 0xd, 0xff, 0xff, 0xfd, 0xb, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa6, 0x0,

    /* U+F07B "" */
    0xbf, 0xff, 0xf6, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0x98, 0x88, 0x74, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F093 "" */
    0x0, 0x0, 0x2, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xe3, 0x0, 0x0, 0x0, 0x3, 0xef, 0xfe,
    0x30, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xe3, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x8f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0xf8, 0x0, 0x0,
    0x23, 0x32, 0x8f, 0xf8, 0x23, 0x32, 0xff, 0xfe,
    0x39, 0x93, 0xef, 0xff, 0xff, 0xff, 0xc9, 0x9c,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5c, 0x8f,
    0x9a, 0xaa, 0xaa, 0xaa, 0xaa, 0xa8,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0x62, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xff, 0xe0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xfd, 0x0, 0x0, 0x1,
    0x0, 0x9, 0xff, 0x40, 0x1, 0x8e, 0xe1, 0x1a,
    0xff, 0x70, 0x0, 0xef, 0xff, 0xde, 0xff, 0x90,
    0x0, 0xc, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x8f, 0xff, 0xe9, 0x10, 0x0, 0x0, 0x2, 0x76,
    0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x7, 0x93, 0x0, 0x0, 0x22, 0xa, 0xff, 0xf2,
    0x0, 0x8f, 0xf5, 0xf9, 0x1f, 0x70, 0x8f, 0xf9,
    0xc, 0xfc, 0xf8, 0x8f, 0xf9, 0x0, 0x1a, 0xef,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0xef, 0xfc, 0x0,
    0x0, 0x7, 0xbf, 0xff, 0xf6, 0x0, 0xa, 0xff,
    0xfa, 0xbf, 0xf6, 0x0, 0xf9, 0x1f, 0x70, 0xbf,
    0xf6, 0xc, 0xfc, 0xf4, 0x0, 0xbf, 0xf4, 0x1a,
    0xc6, 0x0, 0x0, 0x56, 0x0,

    /* U+F0C5 "" */
    0x0, 0x3, 0x44, 0x41, 0x20, 0x0, 0x0, 0xff,
    0xff, 0x5e, 0x40, 0x24, 0x1f, 0xff, 0xf5, 0xee,
    0x2f, 0xf4, 0xff, 0xff, 0xc8, 0x82, 0xff, 0x4f,
    0xff, 0xff, 0xff, 0x5f, 0xf4, 0xff, 0xff, 0xff,
    0xf5, 0xff, 0x4f, 0xff, 0xff, 0xff, 0x5f, 0xf4,
    0xff, 0xff, 0xff, 0xf5, 0xff, 0x4f, 0xff, 0xff,
    0xff, 0x5f, 0xf4, 0xff, 0xff, 0xff, 0xf4, 0xff,
    0x93, 0x44, 0x44, 0x43, 0xf, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x68, 0x88, 0x88, 0x71, 0x0, 0x0,

    /* U+F0C7 "" */
    0x48, 0x88, 0x88, 0x87, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0xf8, 0x0, 0x0, 0xb, 0xfb,
    0xf, 0x80, 0x0, 0x0, 0xbf, 0xf3, 0xfb, 0x77,
    0x77, 0x7d, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0xff, 0xff, 0x42, 0xdf, 0xff, 0x4f, 0xff,
    0xc0, 0x8, 0xff, 0xf4, 0xff, 0xfe, 0x0, 0xaf,
    0xff, 0x4f, 0xff, 0xfc, 0xaf, 0xff, 0xf4, 0xaf,
    0xff, 0xff, 0xff, 0xfd, 0x10,

    /* U+F0E7 "" */
    0x1, 0xbb, 0xba, 0x10, 0x0, 0x5f, 0xff, 0xf1,
    0x0, 0x7, 0xff, 0xfb, 0x0, 0x0, 0x9f, 0xff,
    0x60, 0x0, 0xb, 0xff, 0xff, 0xff, 0x60, 0xef,
    0xff, 0xff, 0xf1, 0xe, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0xc, 0xfe, 0x0, 0x0, 0x0, 0xff, 0x50,
    0x0, 0x0, 0x3f, 0xc0, 0x0, 0x0, 0x7, 0xf3,
    0x0, 0x0, 0x0, 0xa9, 0x0, 0x0, 0x0, 0x2,
    0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x2a, 0x50, 0x0, 0x0, 0xe, 0xff, 0x8f,
    0xff, 0x20, 0x0, 0xff, 0xf8, 0xff, 0xf4, 0x0,
    0xf, 0xff, 0xeb, 0xbb, 0x30, 0x0, 0xff, 0xf4,
    0x99, 0x92, 0x60, 0xf, 0xff, 0x5f, 0xff, 0x4f,
    0xa0, 0xff, 0xf5, 0xff, 0xf5, 0x56, 0x1f, 0xff,
    0x5f, 0xff, 0xff, 0xf4, 0xff, 0xf5, 0xff, 0xff,
    0xff, 0x4e, 0xff, 0x5f, 0xff, 0xff, 0xf4, 0x0,
    0x5, 0xff, 0xff, 0xff, 0x40, 0x0, 0x5f, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x44, 0x44, 0x44, 0x0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x15, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xf1, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xf9, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xf7, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xf2, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0x50, 0x6f, 0xff, 0xff, 0xff,
    0xfd, 0xe, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x24,
    0x44, 0x44, 0x44, 0x43, 0x0, 0x0, 0x2f, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x46, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xfc,
    0x8e, 0x8e, 0x8e, 0x88, 0xe8, 0xf7, 0xf8, 0xc,
    0xc, 0xb, 0x0, 0xb0, 0xf8, 0xff, 0xec, 0xfc,
    0xec, 0xee, 0xcf, 0xf8, 0xff, 0xa0, 0xc0, 0xa0,
    0x77, 0x2f, 0xf8, 0xff, 0xec, 0xfc, 0xec, 0xee,
    0xcf, 0xf8, 0xf8, 0xc, 0x0, 0x0, 0x0, 0xb0,
    0xf8, 0xfc, 0x8e, 0x88, 0x88, 0x88, 0xe8, 0xf7,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x18, 0xef, 0xe0, 0x0, 0x0,
    0x0, 0x29, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x3a,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x4c, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0x0, 0x1, 0x34, 0x44, 0xdf, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x26,
    0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x9b, 0xbb, 0xb2, 0x70, 0xf, 0xff, 0xff, 0x4f,
    0x90, 0xff, 0xff, 0xf4, 0xff, 0x9f, 0xff, 0xff,
    0x54, 0x44, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x34, 0x44,
    0x44, 0x44, 0x30,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x9b, 0xcb, 0x95, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0x80, 0x3, 0xef,
    0xfa, 0x53, 0x23, 0x5a, 0xff, 0xe3, 0xdf, 0xa1,
    0x0, 0x0, 0x0, 0x1, 0xaf, 0xd2, 0x60, 0x5,
    0xbe, 0xfe, 0xb5, 0x0, 0x52, 0x0, 0x1c, 0xff,
    0xfe, 0xff, 0xfc, 0x10, 0x0, 0x2, 0xec, 0x40,
    0x0, 0x4c, 0xe2, 0x0, 0x0, 0x1, 0x0, 0x1,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xd6, 0x0,
    0x0, 0x0,

    /* U+F240 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x44, 0x44, 0x44, 0x44, 0x4f, 0xdf, 0x8c,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0xcf, 0xf8, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0x8, 0xff, 0x89, 0xcc,
    0xcc, 0xcc, 0xcc, 0xc3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F241 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x44, 0x44, 0x43, 0x0, 0x4f, 0xdf, 0x8c,
    0xff, 0xff, 0xff, 0xc0, 0x2, 0xcf, 0xf8, 0xcf,
    0xff, 0xff, 0xfc, 0x0, 0x8, 0xff, 0x89, 0xcc,
    0xcc, 0xcc, 0x90, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F242 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x44, 0x42, 0x0, 0x0, 0x4f, 0xdf, 0x8c,
    0xff, 0xff, 0x80, 0x0, 0x2, 0xcf, 0xf8, 0xcf,
    0xff, 0xf8, 0x0, 0x0, 0x8, 0xff, 0x89, 0xcc,
    0xcc, 0x60, 0x0, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F243 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x34, 0x41, 0x0, 0x0, 0x0, 0x4f, 0xdf, 0x8c,
    0xff, 0x40, 0x0, 0x0, 0x2, 0xcf, 0xf8, 0xcf,
    0xf4, 0x0, 0x0, 0x0, 0x8, 0xff, 0x89, 0xcc,
    0x30, 0x0, 0x0, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F244 "" */
    0x37, 0x77, 0x77, 0x77, 0x77, 0x77, 0x75, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xdf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xcf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xfb, 0x77, 0x77,
    0x77, 0x77, 0x77, 0x9f, 0x9c, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x25, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xcb, 0xfe, 0x0, 0x0, 0x0,
    0x1, 0x0, 0xd, 0x10, 0x42, 0x0, 0x0, 0x0,
    0x9f, 0xd1, 0x68, 0x0, 0x0, 0x0, 0x68, 0x0,
    0xff, 0xfe, 0xee, 0xed, 0xdd, 0xdd, 0xef, 0xc0,
    0x9f, 0xd1, 0x0, 0xb3, 0x0, 0x0, 0x68, 0x0,
    0x1, 0x0, 0x0, 0x3b, 0x5, 0x74, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xbe, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2d, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x34, 0x20, 0x0, 0x0, 0x6e, 0xfe,
    0xfd, 0x20, 0x4, 0xff, 0xf3, 0xff, 0xd0, 0xc,
    0xff, 0xf0, 0x4f, 0xf5, 0xf, 0xd5, 0xf2, 0x95,
    0xf8, 0x2f, 0xf7, 0x41, 0x3c, 0xfa, 0x3f, 0xff,
    0x60, 0xaf, 0xfb, 0x3f, 0xfe, 0x20, 0x4f, 0xfb,
    0x2f, 0xe2, 0x92, 0x75, 0xfa, 0xf, 0xeb, 0xf1,
    0x49, 0xf8, 0x9, 0xff, 0xf0, 0x9f, 0xf2, 0x1,
    0xdf, 0xf9, 0xff, 0x90, 0x0, 0x6, 0xab, 0x95,
    0x0,

    /* U+F2ED "" */
    0x0, 0x4, 0x88, 0x70, 0x0, 0xb, 0xcc, 0xff,
    0xff, 0xdc, 0xc5, 0xbc, 0xcc, 0xcc, 0xcc, 0xcc,
    0x52, 0x88, 0x88, 0x88, 0x88, 0x60, 0x4f, 0xff,
    0xff, 0xff, 0xfc, 0x4, 0xfa, 0xae, 0x6f, 0x5f,
    0xc0, 0x4f, 0xaa, 0xe6, 0xf4, 0xfc, 0x4, 0xfa,
    0xae, 0x6f, 0x4f, 0xc0, 0x4f, 0xaa, 0xe6, 0xf4,
    0xfc, 0x4, 0xfa, 0xae, 0x6f, 0x4f, 0xc0, 0x4f,
    0xaa, 0xe6, 0xf5, 0xfc, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x6, 0x88, 0x88, 0x88, 0x72, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x71, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xef, 0xd1, 0x0, 0x0, 0x0,
    0x1, 0x5f, 0xff, 0xc0, 0x0, 0x0, 0x2, 0xea,
    0x5f, 0xfd, 0x0, 0x0, 0x2, 0xef, 0xfa, 0x5d,
    0x20, 0x0, 0x2, 0xef, 0xff, 0xf8, 0x0, 0x0,
    0x2, 0xef, 0xff, 0xfe, 0x20, 0x0, 0x2, 0xef,
    0xff, 0xfe, 0x20, 0x0, 0x2, 0xef, 0xff, 0xfe,
    0x20, 0x0, 0x0, 0xbf, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0xd, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x6, 0x64,
    0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x5, 0xef, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5,
    0xff, 0xff, 0x91, 0xdd, 0x19, 0xff, 0xf5, 0xff,
    0xff, 0xfd, 0x11, 0x11, 0xdf, 0xff, 0xef, 0xff,
    0xff, 0xfb, 0x0, 0xbf, 0xff, 0xf5, 0xff, 0xff,
    0xfd, 0x11, 0x11, 0xdf, 0xff, 0x5, 0xff, 0xff,
    0x91, 0xdd, 0x19, 0xff, 0xf0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x4, 0xef, 0xff,
    0xff, 0xff, 0xff, 0x80,

    /* U+F7C2 "" */
    0x0, 0x17, 0x88, 0x87, 0x20, 0x2d, 0xff, 0xff,
    0xfd, 0x2e, 0xa0, 0xb3, 0x78, 0xfe, 0xfa, 0xb,
    0x37, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0xff, 0xff, 0xff, 0xfc, 0x4, 0x44,
    0x44, 0x44, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xf0, 0x0, 0x69, 0x0,
    0x0, 0x0, 0xdf, 0x0, 0x7f, 0xc0, 0x0, 0x0,
    0xd, 0xf0, 0x8f, 0xff, 0xdd, 0xdd, 0xdd, 0xff,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xb,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+FF08 "（" */
    0x0, 0x0, 0x0, 0x0, 0x40, 0x0, 0x70, 0x0,
    0x72, 0x0, 0xa, 0x0, 0x0, 0xa0, 0x0, 0xa,
    0x0, 0x0, 0x90, 0x0, 0x2, 0x60, 0x0, 0x3,
    0x50, 0x0, 0x0, 0x0,

    /* U+FF09 "）" */
    0x0, 0x0, 0x1, 0x50, 0x0, 0x1, 0x80, 0x0,
    0x4, 0x60, 0x0, 0xb, 0x0, 0x0, 0xb0, 0x0,
    0xb, 0x0, 0x1, 0xa0, 0x0, 0x92, 0x0, 0x62,
    0x0, 0x10, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 96, .box_w = 2, .box_h = 9, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 9, .adv_w = 96, .box_w = 6, .box_h = 4, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 21, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 48, .adv_w = 96, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 84, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 111, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 138, .adv_w = 96, .box_w = 3, .box_h = 4, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 144, .adv_w = 96, .box_w = 4, .box_h = 13, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 170, .adv_w = 96, .box_w = 4, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 196, .adv_w = 96, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 217, .adv_w = 96, .box_w = 6, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 238, .adv_w = 96, .box_w = 6, .box_h = 1, .ofs_x = 0, .ofs_y = 4},
    {.bitmap_index = 241, .adv_w = 96, .box_w = 3, .box_h = 2, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 244, .adv_w = 96, .box_w = 6, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 280, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 307, .adv_w = 96, .box_w = 4, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 325, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 352, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 379, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 406, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 433, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 460, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 487, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 514, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 541, .adv_w = 96, .box_w = 2, .box_h = 6, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 547, .adv_w = 96, .box_w = 2, .box_h = 8, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 555, .adv_w = 96, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 585, .adv_w = 96, .box_w = 6, .box_h = 4, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 597, .adv_w = 96, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 627, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 654, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 681, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 708, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 735, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 762, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 789, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 816, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 843, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 870, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 897, .adv_w = 96, .box_w = 4, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 915, .adv_w = 96, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 948, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 975, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1002, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1029, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1056, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1083, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1110, .adv_w = 96, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1140, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1167, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1194, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1221, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1248, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1275, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1302, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1329, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1356, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1383, .adv_w = 96, .box_w = 4, .box_h = 12, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 1407, .adv_w = 96, .box_w = 6, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1440, .adv_w = 96, .box_w = 4, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1464, .adv_w = 96, .box_w = 4, .box_h = 2, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 1468, .adv_w = 96, .box_w = 6, .box_h = 1, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1471, .adv_w = 96, .box_w = 4, .box_h = 2, .ofs_x = 0, .ofs_y = 9},
    {.bitmap_index = 1475, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1493, .adv_w = 96, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1523, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1541, .adv_w = 96, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1571, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1589, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1616, .adv_w = 96, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1640, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1667, .adv_w = 96, .box_w = 4, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1685, .adv_w = 96, .box_w = 5, .box_h = 11, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1713, .adv_w = 96, .box_w = 6, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1740, .adv_w = 96, .box_w = 4, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1758, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1776, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1794, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1812, .adv_w = 96, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1836, .adv_w = 96, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 1860, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1878, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1896, .adv_w = 96, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1920, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1938, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1956, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1974, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1992, .adv_w = 96, .box_w = 6, .box_h = 8, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2016, .adv_w = 96, .box_w = 6, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2034, .adv_w = 96, .box_w = 4, .box_h = 12, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 2058, .adv_w = 96, .box_w = 2, .box_h = 13, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 2071, .adv_w = 96, .box_w = 4, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2095, .adv_w = 96, .box_w = 6, .box_h = 3, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 2104, .adv_w = 192, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2154, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2226, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2298, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2364, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2436, .adv_w = 192, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2502, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2574, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2646, .adv_w = 192, .box_w = 8, .box_h = 11, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 2690, .adv_w = 192, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 2751, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2823, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2895, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 2967, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3039, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3111, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3183, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3249, .adv_w = 192, .box_w = 11, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3315, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3387, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3459, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3531, .adv_w = 192, .box_w = 12, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3603, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3681, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3735, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 3801, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3855, .adv_w = 132, .box_w = 9, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3896, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3974, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4052, .adv_w = 216, .box_w = 14, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4129, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4207, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4270, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4348, .adv_w = 96, .box_w = 6, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4378, .adv_w = 144, .box_w = 9, .box_h = 10, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4423, .adv_w = 216, .box_w = 14, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4514, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4568, .adv_w = 168, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 4616, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4688, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4749, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4810, .adv_w = 168, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 4858, .adv_w = 168, .box_w = 12, .box_h = 11, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 4924, .adv_w = 120, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 4963, .adv_w = 120, .box_w = 7, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5002, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5063, .adv_w = 168, .box_w = 11, .box_h = 3, .ofs_x = 0, .ofs_y = 3},
    {.bitmap_index = 5080, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5143, .adv_w = 240, .box_w = 16, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5247, .adv_w = 216, .box_w = 15, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5345, .adv_w = 192, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5411, .adv_w = 168, .box_w = 11, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 5450, .adv_w = 168, .box_w = 11, .box_h = 7, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 5489, .adv_w = 240, .box_w = 16, .box_h = 10, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5569, .adv_w = 192, .box_w = 12, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5623, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5701, .adv_w = 192, .box_w = 13, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 5786, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5847, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5919, .adv_w = 168, .box_w = 11, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 5980, .adv_w = 120, .box_w = 9, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6039, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6111, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6183, .adv_w = 216, .box_w = 14, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6246, .adv_w = 192, .box_w = 14, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6337, .adv_w = 144, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6396, .adv_w = 240, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6486, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6554, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6622, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6690, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6758, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6826, .adv_w = 240, .box_w = 16, .box_h = 11, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6914, .adv_w = 168, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6979, .adv_w = 168, .box_w = 11, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7051, .adv_w = 192, .box_w = 13, .box_h = 13, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 7136, .adv_w = 240, .box_w = 15, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7204, .adv_w = 144, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7263, .adv_w = 193, .box_w = 13, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7322, .adv_w = 192, .box_w = 5, .box_h = 11, .ofs_x = 6, .ofs_y = -1},
    {.bitmap_index = 7350, .adv_w = 192, .box_w = 5, .box_h = 11, .ofs_x = 1, .ofs_y = -1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_2[] = {
    0x0, 0x2d19, 0x3214, 0x3254, 0x3826, 0x3cdb, 0x3d92, 0x3da3,
    0x44e2, 0x451c, 0x461c, 0x47bd, 0x4b11, 0x4c48, 0x4d26, 0x5977,
    0x687c, 0x6c25, 0x6fce, 0x70cc, 0x77cb, 0x79d5, 0xcefe, 0xcf05,
    0xcf08, 0xcf09, 0xcf0a, 0xcf0e, 0xcf10, 0xcf12, 0xcf16, 0xcf19,
    0xcf1e, 0xcf23, 0xcf24, 0xcf25, 0xcf3b, 0xcf45, 0xcf48, 0xcf49,
    0xcf4a, 0xcf4e, 0xcf4f, 0xcf50, 0xcf51, 0xcf64, 0xcf65, 0xcf6b,
    0xcf6d, 0xcf6e, 0xcf71, 0xcf74, 0xcf75, 0xcf76, 0xcf78, 0xcf90,
    0xcf92, 0xcfc1, 0xcfc2, 0xcfc4, 0xcfe4, 0xcfe7, 0xcff0, 0xd019,
    0xd021, 0xd058, 0xd0e8, 0xd13d, 0xd13e, 0xd13f, 0xd140, 0xd141,
    0xd184, 0xd190, 0xd1ea, 0xd201, 0xd457, 0xd6bf, 0xd79f, 0xde05,
    0xde06
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 33, .range_length = 11, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 45, .range_length = 82, .glyph_id_start = 12,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 8451, .range_length = 56839, .glyph_id_start = 94,
        .unicode_list = unicode_list_2, .glyph_id_ofs_list = NULL, .list_length = 81, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 3,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t lv_font_simsun_12 = {
#else
lv_font_t lv_font_simsun_12 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 13,          /*The maximum line height required by the font*/
    .base_line = 2,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -2,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};



#endif /*#if LV_FONT_SIMSUN_12*/

