#ifndef __w25q65_H_
#define __w25q65_H_

#include "stm32f10x.h"
#include "SPI.h"
#define sFLASH_CMD_WRITE          0x02  /*!< Write to Memory instruction */
#define sFLASH_CMD_WRSR           0x01  /*!< Write Status Register instruction */
#define sFLASH_CMD_WREN           0x06  /*!< Write enable instruction */
#define sFLASH_CMD_READ           0x03  /*!< Read from Memory instruction */
#define sFLASH_CMD_RDSR           0x05  /*!< Read Status Register instruction  */
#define sFLASH_CMD_RDID           0x9F  /*!< Read identification */
#define sFLASH_CMD_SE             0x20  /*!< Sector Erase instruction */
#define sFLASH_CMD_BE             0xC7  /*!< Bulk Erase instruction */

#define sFLASH_WIP_FLAG           0x01  /*!< Write In Progress (WIP) flag */

#define sFLASH_DUMMY_BYTE         0xA5
#define sFLASH_SPI_PAGESIZE       0x100

#define  sFLASH_SendByte      SPI_Send_Rec_Byte

#define sFLASH_CS_LOW()       GPIO_ResetBits(GPIOB,GPIO_Pin_12)
#define sFLASH_CS_HIGH()      GPIO_SetBits(GPIOB,GPIO_Pin_12)   
extern uint8_t buff_0x90[2];
extern uint8_t buff_0x9f[3];
void W25Q64_Read_ID_0x90(void);
void W25Q64_Read_ID_0x9f(void);
void sFLASH_WriteEnable(void);
void sFLASH_WaitForWriteEnd(void);
void sFLASH_WritePage(uint8_t* pBuffer, uint32_t WriteAddr, uint16_t NumByteToWrite);
void sFLASH_EraseSector(uint32_t SectorAddr);
void sFLASH_ReadBuffer(uint8_t* pBuffer, uint32_t ReadAddr, uint16_t NumByteToRead);
void sFLASH_WriteBuffer(uint8_t* pBuffer, uint32_t WriteAddr, uint16_t NumByteToWrite);

#endif
