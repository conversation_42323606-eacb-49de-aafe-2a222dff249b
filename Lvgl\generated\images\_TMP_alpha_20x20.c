/*
 * Copyright 2025 NXP
 * SPDX-License-Identifier: MIT
 */

#include "lvgl.h"

#include "lvgl.h"

#include "lvgl.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMG__TMP_ALPHA_20X20
#define LV_ATTRIBUTE_IMG__TMP_ALPHA_20X20
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_IMG__TMP_ALPHA_20X20 uint8_t _TMP_alpha_20x20_map[] = {
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP == 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit*/
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x21, 0x62, 0xd0, 0x39, 0xb8, 0xd1, 0x41, 0xba, 0x2c, 0x29, 0x65, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x65, 0x10, 0x30, 0xf1, 0x41, 0xc4, 0x8f, 0x39, 0xa8, 0x8f, 0x39, 0xa9, 0x12, 0x4a, 0xc7, 0xa7, 0x18, 0x32, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xea, 0x20, 0x53, 0x13, 0x4a, 0xd8, 0xaf, 0x39, 0xa3, 0x6e, 0x31, 0x6a, 0x13, 0x4a, 0xc9, 0x0a, 0x21, 0x59, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x21, 0x54, 0x33, 0x4a, 0xdb, 0x12, 0x42, 0xbe, 0x8f, 0x31, 0x84, 0x13, 0x4a, 0xc9, 0x0a, 0x21, 0x5e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x21, 0x52, 0x33, 0x52, 0xe3, 0x13, 0x72, 0xe0, 0x12, 0x72, 0xe1, 0x33, 0x52, 0xe6, 0x0a, 0x21, 0x5e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0a, 0x21, 0x52, 0x33, 0x6a, 0xeb, 0x33, 0xba, 0xff, 0x33, 0xba, 0xff, 0x33, 0x6a, 0xee, 0x0a, 0x19, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xea, 0x20, 0x53, 0x93, 0x62, 0xeb, 0xf2, 0xab, 0xff, 0xf2, 0xab, 0xff, 0xb3, 0x6a, 0xed, 0xea, 0x20, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xea, 0x20, 0x53, 0x94, 0x5a, 0xeb, 0xb5, 0x83, 0xff, 0xb5, 0x83, 0xff, 0x94, 0x5a, 0xed, 0xea, 0x20, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xca, 0x20, 0x50, 0x36, 0x53, 0xeb, 0xdd, 0x5d, 0xff, 0xde, 0x5d, 0xff, 0x56, 0x53, 0xec, 0xca, 0x20, 0x58, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc9, 0x20, 0x4f, 0x56, 0x53, 0xea, 0x3f, 0x5e, 0xff, 0x5f, 0x5e, 0xff, 0x77, 0x53, 0xec, 0xa9, 0x20, 0x57, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa9, 0x20, 0x4f, 0x56, 0x53, 0xea, 0x3f, 0x5e, 0xff, 0x3f, 0x5e, 0xff, 0x76, 0x53, 0xec, 0xa9, 0x20, 0x56, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0xcb, 0x28, 0x59, 0x56, 0x53, 0xed, 0x3f, 0x5e, 0xff, 0x3f, 0x5e, 0xff, 0x76, 0x53, 0xef, 0xcb, 0x28, 0x61, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc8, 0x18, 0x48, 0x32, 0x42, 0xcc, 0xba, 0x54, 0xfd, 0x5f, 0x5e, 0xff, 0x5f, 0x5e, 0xff, 0xba, 0x54, 0xfd, 0x32, 0x42, 0xce, 0xc8, 0x18, 0x4b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x44, 0x08, 0x00, 0xf1, 0x41, 0xb9, 0x9a, 0x54, 0xff, 0xda, 0x54, 0xff, 0x7c, 0x5d, 0xff, 0x7f, 0x5e, 0xff, 0x5f, 0x5e, 0xff, 0xfb, 0x5c, 0xff, 0x11, 0x42, 0xbc, 0x24, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x67, 0x18, 0x54, 0x56, 0x4b, 0xea, 0xfb, 0x5c, 0xff, 0x38, 0x54, 0xff, 0x3e, 0x5e, 0xff, 0x7f, 0x5e, 0xff, 0x7f, 0x5e, 0xff, 0x3e, 0x5e, 0xff, 0x56, 0x53, 0xec, 0xc9, 0x20, 0x58, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xca, 0x28, 0x6e, 0xd7, 0x53, 0xf6, 0x79, 0x54, 0xff, 0x5c, 0x5d, 0xff, 0x9f, 0x5e, 0xff, 0x7f, 0x5e, 0xff, 0xdd, 0x5d, 0xff, 0x1b, 0x5d, 0xff, 0xd8, 0x53, 0xf6, 0x0c, 0x31, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x88, 0x18, 0x57, 0x56, 0x4b, 0xec, 0xba, 0x54, 0xff, 0x9a, 0x54, 0xff, 0x5f, 0x5e, 0xff, 0x5f, 0x5e, 0xff, 0x9a, 0x54, 0xff, 0xba, 0x54, 0xff, 0x76, 0x53, 0xef, 0xa9, 0x20, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x23, 0x08, 0x06, 0xf1, 0x41, 0xbe, 0x9a, 0x54, 0xff, 0x59, 0x54, 0xff, 0x39, 0x54, 0xff, 0x39, 0x54, 0xff, 0x59, 0x54, 0xff, 0x9a, 0x54, 0xff, 0x11, 0x42, 0xc4, 0x23, 0x08, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xc9, 0x20, 0x54, 0x73, 0x4a, 0xd2, 0x39, 0x54, 0xfc, 0xba, 0x54, 0xff, 0x9a, 0x54, 0xff, 0x39, 0x54, 0xfd, 0x73, 0x4a, 0xd4, 0xc9, 0x20, 0x5a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0xa7, 0x18, 0x3f, 0x8f, 0x39, 0x9d, 0x11, 0x42, 0xc7, 0x11, 0x42, 0xc6, 0x8f, 0x39, 0x9b, 0x87, 0x18, 0x43, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP != 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit  BUT the 2  color bytes are swapped*/
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x21, 0x0a, 0x62, 0x39, 0xd0, 0xb8, 0x41, 0xd1, 0xba, 0x29, 0x2c, 0x65, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x65, 0x30, 0x41, 0xf1, 0xc4, 0x39, 0x8f, 0xa8, 0x39, 0x8f, 0xa9, 0x4a, 0x12, 0xc7, 0x18, 0xa7, 0x32, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xea, 0x53, 0x4a, 0x13, 0xd8, 0x39, 0xaf, 0xa3, 0x31, 0x6e, 0x6a, 0x4a, 0x13, 0xc9, 0x21, 0x0a, 0x59, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x21, 0x0a, 0x54, 0x4a, 0x33, 0xdb, 0x42, 0x12, 0xbe, 0x31, 0x8f, 0x84, 0x4a, 0x13, 0xc9, 0x21, 0x0a, 0x5e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x21, 0x0a, 0x52, 0x52, 0x33, 0xe3, 0x72, 0x13, 0xe0, 0x72, 0x12, 0xe1, 0x52, 0x33, 0xe6, 0x21, 0x0a, 0x5e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x21, 0x0a, 0x52, 0x6a, 0x33, 0xeb, 0xba, 0x33, 0xff, 0xba, 0x33, 0xff, 0x6a, 0x33, 0xee, 0x19, 0x0a, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xea, 0x53, 0x62, 0x93, 0xeb, 0xab, 0xf2, 0xff, 0xab, 0xf2, 0xff, 0x6a, 0xb3, 0xed, 0x20, 0xea, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xea, 0x53, 0x5a, 0x94, 0xeb, 0x83, 0xb5, 0xff, 0x83, 0xb5, 0xff, 0x5a, 0x94, 0xed, 0x20, 0xea, 0x5b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xca, 0x50, 0x53, 0x36, 0xeb, 0x5d, 0xdd, 0xff, 0x5d, 0xde, 0xff, 0x53, 0x56, 0xec, 0x20, 0xca, 0x58, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xc9, 0x4f, 0x53, 0x56, 0xea, 0x5e, 0x3f, 0xff, 0x5e, 0x5f, 0xff, 0x53, 0x77, 0xec, 0x20, 0xa9, 0x57, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xa9, 0x4f, 0x53, 0x56, 0xea, 0x5e, 0x3f, 0xff, 0x5e, 0x3f, 0xff, 0x53, 0x76, 0xec, 0x20, 0xa9, 0x56, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x28, 0xcb, 0x59, 0x53, 0x56, 0xed, 0x5e, 0x3f, 0xff, 0x5e, 0x3f, 0xff, 0x53, 0x76, 0xef, 0x28, 0xcb, 0x61, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0xc8, 0x48, 0x42, 0x32, 0xcc, 0x54, 0xba, 0xfd, 0x5e, 0x5f, 0xff, 0x5e, 0x5f, 0xff, 0x54, 0xba, 0xfd, 0x42, 0x32, 0xce, 0x18, 0xc8, 0x4b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x44, 0x00, 0x41, 0xf1, 0xb9, 0x54, 0x9a, 0xff, 0x54, 0xda, 0xff, 0x5d, 0x7c, 0xff, 0x5e, 0x7f, 0xff, 0x5e, 0x5f, 0xff, 0x5c, 0xfb, 0xff, 0x42, 0x11, 0xbc, 0x08, 0x24, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x67, 0x54, 0x4b, 0x56, 0xea, 0x5c, 0xfb, 0xff, 0x54, 0x38, 0xff, 0x5e, 0x3e, 0xff, 0x5e, 0x7f, 0xff, 0x5e, 0x7f, 0xff, 0x5e, 0x3e, 0xff, 0x53, 0x56, 0xec, 0x20, 0xc9, 0x58, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x28, 0xca, 0x6e, 0x53, 0xd7, 0xf6, 0x54, 0x79, 0xff, 0x5d, 0x5c, 0xff, 0x5e, 0x9f, 0xff, 0x5e, 0x7f, 0xff, 0x5d, 0xdd, 0xff, 0x5d, 0x1b, 0xff, 0x53, 0xd8, 0xf6, 0x31, 0x0c, 0x70, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x88, 0x57, 0x4b, 0x56, 0xec, 0x54, 0xba, 0xff, 0x54, 0x9a, 0xff, 0x5e, 0x5f, 0xff, 0x5e, 0x5f, 0xff, 0x54, 0x9a, 0xff, 0x54, 0xba, 0xff, 0x53, 0x76, 0xef, 0x20, 0xa9, 0x60, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x23, 0x06, 0x41, 0xf1, 0xbe, 0x54, 0x9a, 0xff, 0x54, 0x59, 0xff, 0x54, 0x39, 0xff, 0x54, 0x39, 0xff, 0x54, 0x59, 0xff, 0x54, 0x9a, 0xff, 0x42, 0x11, 0xc4, 0x08, 0x23, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xc9, 0x54, 0x4a, 0x73, 0xd2, 0x54, 0x39, 0xfc, 0x54, 0xba, 0xff, 0x54, 0x9a, 0xff, 0x54, 0x39, 0xfd, 0x4a, 0x73, 0xd4, 0x20, 0xc9, 0x5a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x18, 0xa7, 0x3f, 0x39, 0x8f, 0x9d, 0x42, 0x11, 0xc7, 0x42, 0x11, 0xc6, 0x39, 0x8f, 0x9b, 0x18, 0x87, 0x43, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
#endif
};

const lv_img_dsc_t _TMP_alpha_20x20 = {
  .header.always_zero = 0,
  .header.w = 20,
  .header.h = 20,
  .data_size = 400 * LV_COLOR_SIZE / 8,
  .header.cf = LV_IMG_CF_TRUE_COLOR_ALPHA,
  .data = _TMP_alpha_20x20_map,
};
