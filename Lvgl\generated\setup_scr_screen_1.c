/*
 * Copyright 2025 NXP
 * SPDX-License-Identifier: MIT
 */

#include "lvgl.h"
#include <stdio.h>
#include "gui_guider.h"
#include "events_init.h"
//#include "custom.h"


void setup_scr_screen_1(lv_ui *ui){

	//Write codes screen_1
	ui->screen_1 = lv_obj_create(NULL, NULL);

	//Write style LV_OBJ_PART_MAIN for screen_1
	static lv_style_t style_screen_1_main;
	lv_style_reset(&style_screen_1_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_main
	lv_style_set_bg_color(&style_screen_1_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_opa(&style_screen_1_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1, LV_OBJ_PART_MAIN, &style_screen_1_main);

	//Write codes screen_1_img_1
	ui->screen_1_img_1 = lv_img_create(ui->screen_1, NULL);

	//Write style LV_IMG_PART_MAIN for screen_1_img_1
	static lv_style_t style_screen_1_img_1_main;
	lv_style_reset(&style_screen_1_img_1_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_img_1_main
	lv_style_set_image_recolor(&style_screen_1_img_1_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_image_recolor_opa(&style_screen_1_img_1_main, LV_STATE_DEFAULT, 0);
	lv_style_set_image_opa(&style_screen_1_img_1_main, LV_STATE_DEFAULT, 255);
	lv_obj_add_style(ui->screen_1_img_1, LV_IMG_PART_MAIN, &style_screen_1_img_1_main);
	lv_obj_set_pos(ui->screen_1_img_1, 0, 0);
	lv_obj_set_size(ui->screen_1_img_1, 320, 240);
	lv_obj_set_click(ui->screen_1_img_1, true);
	lv_img_set_src(ui->screen_1_img_1,&_9_alpha_320x240);
	lv_img_set_pivot(ui->screen_1_img_1, 0,0);
	lv_img_set_angle(ui->screen_1_img_1, 0);

	//Write codes screen_1_cont_1
	ui->screen_1_cont_1 = lv_cont_create(ui->screen_1, NULL);

	//Write style LV_CONT_PART_MAIN for screen_1_cont_1
	static lv_style_t style_screen_1_cont_1_main;
	lv_style_reset(&style_screen_1_cont_1_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_cont_1_main
	lv_style_set_radius(&style_screen_1_cont_1_main, LV_STATE_DEFAULT, 20);
	lv_style_set_bg_color(&style_screen_1_cont_1_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_color(&style_screen_1_cont_1_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_dir(&style_screen_1_cont_1_main, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_style_set_bg_opa(&style_screen_1_cont_1_main, LV_STATE_DEFAULT, 0);
	lv_style_set_border_color(&style_screen_1_cont_1_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xf0, 0xf0));
	lv_style_set_border_width(&style_screen_1_cont_1_main, LV_STATE_DEFAULT, 2);
	lv_style_set_border_opa(&style_screen_1_cont_1_main, LV_STATE_DEFAULT, 255);
	lv_style_set_pad_left(&style_screen_1_cont_1_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_right(&style_screen_1_cont_1_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_top(&style_screen_1_cont_1_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_bottom(&style_screen_1_cont_1_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1_cont_1, LV_CONT_PART_MAIN, &style_screen_1_cont_1_main);
	lv_obj_set_pos(ui->screen_1_cont_1, 0, 0);
	lv_obj_set_size(ui->screen_1_cont_1, 84, 240);
	lv_obj_set_click(ui->screen_1_cont_1, false);

	//Write codes screen_1_label_7
	ui->screen_1_label_7 = lv_label_create(ui->screen_1_cont_1, NULL);
	lv_label_set_text(ui->screen_1_label_7, "19");
	lv_label_set_long_mode(ui->screen_1_label_7, LV_LABEL_LONG_BREAK);
	lv_label_set_align(ui->screen_1_label_7, LV_LABEL_ALIGN_CENTER);

	//Write style LV_LABEL_PART_MAIN for screen_1_label_7
	static lv_style_t style_screen_1_label_7_main;
	lv_style_reset(&style_screen_1_label_7_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_label_7_main
	lv_style_set_radius(&style_screen_1_label_7_main, LV_STATE_DEFAULT, 0);
	lv_style_set_bg_color(&style_screen_1_label_7_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_color(&style_screen_1_label_7_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_dir(&style_screen_1_label_7_main, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_style_set_bg_opa(&style_screen_1_label_7_main, LV_STATE_DEFAULT, 0);
	lv_style_set_text_color(&style_screen_1_label_7_main, LV_STATE_DEFAULT, lv_color_make(0x00, 0x00, 0x00));
	lv_style_set_text_font(&style_screen_1_label_7_main, LV_STATE_DEFAULT, &lv_font_Slideyouran_Regular_20);
	lv_style_set_text_letter_space(&style_screen_1_label_7_main, LV_STATE_DEFAULT, 2);
	lv_style_set_pad_left(&style_screen_1_label_7_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_right(&style_screen_1_label_7_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_top(&style_screen_1_label_7_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_bottom(&style_screen_1_label_7_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1_label_7, LV_LABEL_PART_MAIN, &style_screen_1_label_7_main);
	lv_obj_set_pos(ui->screen_1_label_7, 7, 191);
	lv_obj_set_size(ui->screen_1_label_7, 23, 0);

	//Write codes screen_1_label_6
	ui->screen_1_label_6 = lv_label_create(ui->screen_1_cont_1, NULL);
	lv_label_set_text(ui->screen_1_label_6, "℃ ");
	lv_label_set_long_mode(ui->screen_1_label_6, LV_LABEL_LONG_BREAK);
	lv_label_set_align(ui->screen_1_label_6, LV_LABEL_ALIGN_CENTER);

	//Write style LV_LABEL_PART_MAIN for screen_1_label_6
	static lv_style_t style_screen_1_label_6_main;
	lv_style_reset(&style_screen_1_label_6_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_label_6_main
	lv_style_set_radius(&style_screen_1_label_6_main, LV_STATE_DEFAULT, 0);
	lv_style_set_bg_color(&style_screen_1_label_6_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_color(&style_screen_1_label_6_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_dir(&style_screen_1_label_6_main, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_style_set_bg_opa(&style_screen_1_label_6_main, LV_STATE_DEFAULT, 0);
	lv_style_set_text_color(&style_screen_1_label_6_main, LV_STATE_DEFAULT, lv_color_make(0x00, 0x00, 0x00));
	lv_style_set_text_font(&style_screen_1_label_6_main, LV_STATE_DEFAULT, &lv_font_Slideyouran_Regular_20);
	lv_style_set_text_letter_space(&style_screen_1_label_6_main, LV_STATE_DEFAULT, 2);
	lv_style_set_pad_left(&style_screen_1_label_6_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_right(&style_screen_1_label_6_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_top(&style_screen_1_label_6_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_bottom(&style_screen_1_label_6_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1_label_6, LV_LABEL_PART_MAIN, &style_screen_1_label_6_main);
	lv_obj_set_pos(ui->screen_1_label_6, 30, 166);
	lv_obj_set_size(ui->screen_1_label_6, 25, 0);

	//Write codes screen_1_label_4
	ui->screen_1_label_4 = lv_label_create(ui->screen_1_cont_1, NULL);
	lv_label_set_text(ui->screen_1_label_4, "温度（高/底） ");
	lv_label_set_long_mode(ui->screen_1_label_4, LV_LABEL_LONG_BREAK);
	lv_label_set_align(ui->screen_1_label_4, LV_LABEL_ALIGN_CENTER);

	//Write style LV_LABEL_PART_MAIN for screen_1_label_4
	static lv_style_t style_screen_1_label_4_main;
	lv_style_reset(&style_screen_1_label_4_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_label_4_main
	lv_style_set_radius(&style_screen_1_label_4_main, LV_STATE_DEFAULT, 0);
	lv_style_set_bg_color(&style_screen_1_label_4_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_color(&style_screen_1_label_4_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_dir(&style_screen_1_label_4_main, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_style_set_bg_opa(&style_screen_1_label_4_main, LV_STATE_DEFAULT, 0);
	lv_style_set_text_color(&style_screen_1_label_4_main, LV_STATE_DEFAULT, lv_color_make(0x00, 0x00, 0x00));
	lv_style_set_text_font(&style_screen_1_label_4_main, LV_STATE_DEFAULT, &lv_font_Slideyouran_Regular_20);
	lv_style_set_text_letter_space(&style_screen_1_label_4_main, LV_STATE_DEFAULT, 2);
	lv_style_set_pad_left(&style_screen_1_label_4_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_right(&style_screen_1_label_4_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_top(&style_screen_1_label_4_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_bottom(&style_screen_1_label_4_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1_label_4, LV_LABEL_PART_MAIN, &style_screen_1_label_4_main);
	lv_obj_set_pos(ui->screen_1_label_4, 2, 117);
	lv_obj_set_size(ui->screen_1_label_4, 80, 0);

	//Write codes screen_1_img_3
	ui->screen_1_img_3 = lv_img_create(ui->screen_1_cont_1, NULL);

	//Write style LV_IMG_PART_MAIN for screen_1_img_3
	static lv_style_t style_screen_1_img_3_main;
	lv_style_reset(&style_screen_1_img_3_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_img_3_main
	lv_style_set_image_recolor(&style_screen_1_img_3_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_image_recolor_opa(&style_screen_1_img_3_main, LV_STATE_DEFAULT, 0);
	lv_style_set_image_opa(&style_screen_1_img_3_main, LV_STATE_DEFAULT, 255);
	lv_obj_add_style(ui->screen_1_img_3, LV_IMG_PART_MAIN, &style_screen_1_img_3_main);
	lv_obj_set_pos(ui->screen_1_img_3, 37, 40);
	lv_obj_set_size(ui->screen_1_img_3, 36, 38);
	lv_obj_set_click(ui->screen_1_img_3, true);
	lv_img_set_src(ui->screen_1_img_3,&_sunshine_alpha_36x38);
	lv_img_set_pivot(ui->screen_1_img_3, 0,0);
	lv_img_set_angle(ui->screen_1_img_3, 0);

	//Write codes screen_1_label_2
	ui->screen_1_label_2 = lv_label_create(ui->screen_1_cont_1, NULL);
	lv_label_set_text(ui->screen_1_label_2, "天气");
	lv_label_set_long_mode(ui->screen_1_label_2, LV_LABEL_LONG_BREAK);
	lv_label_set_align(ui->screen_1_label_2, LV_LABEL_ALIGN_CENTER);

	//Write style LV_LABEL_PART_MAIN for screen_1_label_2
	static lv_style_t style_screen_1_label_2_main;
	lv_style_reset(&style_screen_1_label_2_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_label_2_main
	lv_style_set_radius(&style_screen_1_label_2_main, LV_STATE_DEFAULT, 0);
	lv_style_set_bg_color(&style_screen_1_label_2_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_color(&style_screen_1_label_2_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_dir(&style_screen_1_label_2_main, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_style_set_bg_opa(&style_screen_1_label_2_main, LV_STATE_DEFAULT, 0);
	lv_style_set_text_color(&style_screen_1_label_2_main, LV_STATE_DEFAULT, lv_color_make(0x00, 0x00, 0x00));
	lv_style_set_text_font(&style_screen_1_label_2_main, LV_STATE_DEFAULT, &lv_font_Slideyouran_Regular_20);
	lv_style_set_text_letter_space(&style_screen_1_label_2_main, LV_STATE_DEFAULT, 2);
	lv_style_set_pad_left(&style_screen_1_label_2_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_right(&style_screen_1_label_2_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_top(&style_screen_1_label_2_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_bottom(&style_screen_1_label_2_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1_label_2, LV_LABEL_PART_MAIN, &style_screen_1_label_2_main);
	lv_obj_set_pos(ui->screen_1_label_2, 9, 35);
	lv_obj_set_size(ui->screen_1_label_2, 20, 0);

	//Write codes screen_1_img_2
	ui->screen_1_img_2 = lv_img_create(ui->screen_1_cont_1, NULL);

	//Write style LV_IMG_PART_MAIN for screen_1_img_2
	static lv_style_t style_screen_1_img_2_main;
	lv_style_reset(&style_screen_1_img_2_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_img_2_main
	lv_style_set_image_recolor(&style_screen_1_img_2_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_image_recolor_opa(&style_screen_1_img_2_main, LV_STATE_DEFAULT, 0);
	lv_style_set_image_opa(&style_screen_1_img_2_main, LV_STATE_DEFAULT, 255);
	lv_obj_add_style(ui->screen_1_img_2, LV_IMG_PART_MAIN, &style_screen_1_img_2_main);
	lv_obj_set_pos(ui->screen_1_img_2, 0, 0);
	lv_obj_set_size(ui->screen_1_img_2, 39, 30);
	lv_obj_set_click(ui->screen_1_img_2, true);
	lv_img_set_src(ui->screen_1_img_2,&_didian_alpha_39x30);
	lv_img_set_pivot(ui->screen_1_img_2, 0,0);
	lv_img_set_angle(ui->screen_1_img_2, 0);
	lv_cont_set_layout(ui->screen_1_cont_1, LV_LAYOUT_OFF);
	lv_cont_set_fit(ui->screen_1_cont_1, LV_FIT_NONE);

	//Write codes screen_1_label_1
	ui->screen_1_label_1 = lv_label_create(ui->screen_1, NULL);
	lv_label_set_text(ui->screen_1_label_1, "郑州");
	lv_label_set_long_mode(ui->screen_1_label_1, LV_LABEL_LONG_BREAK);
	lv_label_set_align(ui->screen_1_label_1, LV_LABEL_ALIGN_CENTER);

	//Write style LV_LABEL_PART_MAIN for screen_1_label_1
	static lv_style_t style_screen_1_label_1_main;
	lv_style_reset(&style_screen_1_label_1_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_label_1_main
	lv_style_set_radius(&style_screen_1_label_1_main, LV_STATE_DEFAULT, 0);
	lv_style_set_bg_color(&style_screen_1_label_1_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_color(&style_screen_1_label_1_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_dir(&style_screen_1_label_1_main, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_style_set_bg_opa(&style_screen_1_label_1_main, LV_STATE_DEFAULT, 0);
	lv_style_set_text_color(&style_screen_1_label_1_main, LV_STATE_DEFAULT, lv_color_make(0x00, 0x00, 0x00));
	lv_style_set_text_font(&style_screen_1_label_1_main, LV_STATE_DEFAULT, &lv_font_Slideyouran_Regular_20);
	lv_style_set_text_letter_space(&style_screen_1_label_1_main, LV_STATE_DEFAULT, 2);
	lv_style_set_pad_left(&style_screen_1_label_1_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_right(&style_screen_1_label_1_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_top(&style_screen_1_label_1_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_bottom(&style_screen_1_label_1_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1_label_1, LV_LABEL_PART_MAIN, &style_screen_1_label_1_main);
	lv_obj_set_pos(ui->screen_1_label_1, 37, 3);
	lv_obj_set_size(ui->screen_1_label_1, 47, 0);

	//Write codes screen_1_label_3
	ui->screen_1_label_3 = lv_label_create(ui->screen_1, NULL);
	lv_label_set_text(ui->screen_1_label_3, "东西南北风  ");
	lv_label_set_long_mode(ui->screen_1_label_3, LV_LABEL_LONG_BREAK);
	lv_label_set_align(ui->screen_1_label_3, LV_LABEL_ALIGN_CENTER);

	//Write style LV_LABEL_PART_MAIN for screen_1_label_3
	static lv_style_t style_screen_1_label_3_main;
	lv_style_reset(&style_screen_1_label_3_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_label_3_main
	lv_style_set_radius(&style_screen_1_label_3_main, LV_STATE_DEFAULT, 0);
	lv_style_set_bg_color(&style_screen_1_label_3_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_color(&style_screen_1_label_3_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_dir(&style_screen_1_label_3_main, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_style_set_bg_opa(&style_screen_1_label_3_main, LV_STATE_DEFAULT, 0);
	lv_style_set_text_color(&style_screen_1_label_3_main, LV_STATE_DEFAULT, lv_color_make(0x00, 0x00, 0x00));
	lv_style_set_text_font(&style_screen_1_label_3_main, LV_STATE_DEFAULT, &lv_font_Slideyouran_Regular_20);
	lv_style_set_text_letter_space(&style_screen_1_label_3_main, LV_STATE_DEFAULT, 2);
	lv_style_set_pad_left(&style_screen_1_label_3_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_right(&style_screen_1_label_3_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_top(&style_screen_1_label_3_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_bottom(&style_screen_1_label_3_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1_label_3, LV_LABEL_PART_MAIN, &style_screen_1_label_3_main);
	lv_obj_set_pos(ui->screen_1_label_3, 5, 78);
	lv_obj_set_size(ui->screen_1_label_3, 77, 0);

	//Write codes screen_1_label_5
	ui->screen_1_label_5 = lv_label_create(ui->screen_1, NULL);
	lv_label_set_text(ui->screen_1_label_5, "25 ");
	lv_label_set_long_mode(ui->screen_1_label_5, LV_LABEL_LONG_BREAK);
	lv_label_set_align(ui->screen_1_label_5, LV_LABEL_ALIGN_CENTER);

	//Write style LV_LABEL_PART_MAIN for screen_1_label_5
	static lv_style_t style_screen_1_label_5_main;
	lv_style_reset(&style_screen_1_label_5_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_label_5_main
	lv_style_set_radius(&style_screen_1_label_5_main, LV_STATE_DEFAULT, 0);
	lv_style_set_bg_color(&style_screen_1_label_5_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_color(&style_screen_1_label_5_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_dir(&style_screen_1_label_5_main, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_style_set_bg_opa(&style_screen_1_label_5_main, LV_STATE_DEFAULT, 0);
	lv_style_set_text_color(&style_screen_1_label_5_main, LV_STATE_DEFAULT, lv_color_make(0x00, 0x00, 0x00));
	lv_style_set_text_font(&style_screen_1_label_5_main, LV_STATE_DEFAULT, &lv_font_Slideyouran_Regular_20);
	lv_style_set_text_letter_space(&style_screen_1_label_5_main, LV_STATE_DEFAULT, 2);
	lv_style_set_pad_left(&style_screen_1_label_5_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_right(&style_screen_1_label_5_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_top(&style_screen_1_label_5_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_bottom(&style_screen_1_label_5_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1_label_5, LV_LABEL_PART_MAIN, &style_screen_1_label_5_main);
	lv_obj_set_pos(ui->screen_1_label_5, 3, 165);
	lv_obj_set_size(ui->screen_1_label_5, 28, 0);

	//Write codes screen_1_label_8
	ui->screen_1_label_8 = lv_label_create(ui->screen_1, NULL);
	lv_label_set_text(ui->screen_1_label_8, "℃  ");
	lv_label_set_long_mode(ui->screen_1_label_8, LV_LABEL_LONG_BREAK);
	lv_label_set_align(ui->screen_1_label_8, LV_LABEL_ALIGN_CENTER);

	//Write style LV_LABEL_PART_MAIN for screen_1_label_8
	static lv_style_t style_screen_1_label_8_main;
	lv_style_reset(&style_screen_1_label_8_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_label_8_main
	lv_style_set_radius(&style_screen_1_label_8_main, LV_STATE_DEFAULT, 0);
	lv_style_set_bg_color(&style_screen_1_label_8_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_color(&style_screen_1_label_8_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_dir(&style_screen_1_label_8_main, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_style_set_bg_opa(&style_screen_1_label_8_main, LV_STATE_DEFAULT, 0);
	lv_style_set_text_color(&style_screen_1_label_8_main, LV_STATE_DEFAULT, lv_color_make(0x00, 0x00, 0x00));
	lv_style_set_text_font(&style_screen_1_label_8_main, LV_STATE_DEFAULT, &lv_font_Slideyouran_Regular_20);
	lv_style_set_text_letter_space(&style_screen_1_label_8_main, LV_STATE_DEFAULT, 2);
	lv_style_set_pad_left(&style_screen_1_label_8_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_right(&style_screen_1_label_8_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_top(&style_screen_1_label_8_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_bottom(&style_screen_1_label_8_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1_label_8, LV_LABEL_PART_MAIN, &style_screen_1_label_8_main);
	lv_obj_set_pos(ui->screen_1_label_8, 28, 192);
	lv_obj_set_size(ui->screen_1_label_8, 28, 0);

	//Write codes screen_1_cont_2
	ui->screen_1_cont_2 = lv_cont_create(ui->screen_1, NULL);

	//Write style LV_CONT_PART_MAIN for screen_1_cont_2
	static lv_style_t style_screen_1_cont_2_main;
	lv_style_reset(&style_screen_1_cont_2_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_cont_2_main
	lv_style_set_radius(&style_screen_1_cont_2_main, LV_STATE_DEFAULT, 20);
	lv_style_set_bg_color(&style_screen_1_cont_2_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_color(&style_screen_1_cont_2_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_dir(&style_screen_1_cont_2_main, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_style_set_bg_opa(&style_screen_1_cont_2_main, LV_STATE_DEFAULT, 0);
	lv_style_set_border_color(&style_screen_1_cont_2_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xe5, 0xe5));
	lv_style_set_border_width(&style_screen_1_cont_2_main, LV_STATE_DEFAULT, 2);
	lv_style_set_border_opa(&style_screen_1_cont_2_main, LV_STATE_DEFAULT, 255);
	lv_style_set_pad_left(&style_screen_1_cont_2_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_right(&style_screen_1_cont_2_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_top(&style_screen_1_cont_2_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_bottom(&style_screen_1_cont_2_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1_cont_2, LV_CONT_PART_MAIN, &style_screen_1_cont_2_main);
	lv_obj_set_pos(ui->screen_1_cont_2, 100, 0);
	lv_obj_set_size(ui->screen_1_cont_2, 200, 100);
	lv_obj_set_click(ui->screen_1_cont_2, false);

	//Write codes screen_1_img_6
	ui->screen_1_img_6 = lv_img_create(ui->screen_1_cont_2, NULL);

	//Write style LV_IMG_PART_MAIN for screen_1_img_6
	static lv_style_t style_screen_1_img_6_main;
	lv_style_reset(&style_screen_1_img_6_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_img_6_main
	lv_style_set_image_recolor(&style_screen_1_img_6_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_image_recolor_opa(&style_screen_1_img_6_main, LV_STATE_DEFAULT, 0);
	lv_style_set_image_opa(&style_screen_1_img_6_main, LV_STATE_DEFAULT, 255);
	lv_obj_add_style(ui->screen_1_img_6, LV_IMG_PART_MAIN, &style_screen_1_img_6_main);
	lv_obj_set_pos(ui->screen_1_img_6, 117, 35);
	lv_obj_set_size(ui->screen_1_img_6, 55, 55);
	lv_obj_set_click(ui->screen_1_img_6, true);
	lv_img_set_src(ui->screen_1_img_6,&_wifi_alpha_55x55);
	lv_img_set_pivot(ui->screen_1_img_6, 0,0);
	lv_img_set_angle(ui->screen_1_img_6, 0);

	//Write codes screen_1_img_5
	ui->screen_1_img_5 = lv_img_create(ui->screen_1_cont_2, NULL);

	//Write style LV_IMG_PART_MAIN for screen_1_img_5
	static lv_style_t style_screen_1_img_5_main;
	lv_style_reset(&style_screen_1_img_5_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_img_5_main
	lv_style_set_image_recolor(&style_screen_1_img_5_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_image_recolor_opa(&style_screen_1_img_5_main, LV_STATE_DEFAULT, 0);
	lv_style_set_image_opa(&style_screen_1_img_5_main, LV_STATE_DEFAULT, 255);
	lv_obj_add_style(ui->screen_1_img_5, LV_IMG_PART_MAIN, &style_screen_1_img_5_main);
	lv_obj_set_pos(ui->screen_1_img_5, 117, 35);
	lv_obj_set_size(ui->screen_1_img_5, 55, 55);
	lv_obj_set_click(ui->screen_1_img_5, true);
	lv_img_set_src(ui->screen_1_img_5,&_wifi_you_alpha_55x55);
	lv_img_set_pivot(ui->screen_1_img_5, 0,0);
	lv_img_set_angle(ui->screen_1_img_5, 0);

	//Write codes screen_1_img_4
	ui->screen_1_img_4 = lv_img_create(ui->screen_1_cont_2, NULL);

	//Write style LV_IMG_PART_MAIN for screen_1_img_4
	static lv_style_t style_screen_1_img_4_main;
	lv_style_reset(&style_screen_1_img_4_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_img_4_main
	lv_style_set_image_recolor(&style_screen_1_img_4_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_image_recolor_opa(&style_screen_1_img_4_main, LV_STATE_DEFAULT, 0);
	lv_style_set_image_opa(&style_screen_1_img_4_main, LV_STATE_DEFAULT, 255);
	lv_obj_add_style(ui->screen_1_img_4, LV_IMG_PART_MAIN, &style_screen_1_img_4_main);
	lv_obj_set_pos(ui->screen_1_img_4, 143, 0);
	lv_obj_set_size(ui->screen_1_img_4, 39, 27);
	lv_obj_set_click(ui->screen_1_img_4, true);
	lv_img_set_src(ui->screen_1_img_4,&_dianliang_alpha_39x27);
	lv_img_set_pivot(ui->screen_1_img_4, 0,0);
	lv_img_set_angle(ui->screen_1_img_4, 0);

	//Write codes screen_1_label_11
	ui->screen_1_label_11 = lv_label_create(ui->screen_1_cont_2, NULL);
	lv_label_set_text(ui->screen_1_label_11, "15:35");
	lv_label_set_long_mode(ui->screen_1_label_11, LV_LABEL_LONG_BREAK);
	lv_label_set_align(ui->screen_1_label_11, LV_LABEL_ALIGN_CENTER);

	//Write style LV_LABEL_PART_MAIN for screen_1_label_11
	static lv_style_t style_screen_1_label_11_main;
	lv_style_reset(&style_screen_1_label_11_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_label_11_main
	lv_style_set_radius(&style_screen_1_label_11_main, LV_STATE_DEFAULT, 0);
	lv_style_set_bg_color(&style_screen_1_label_11_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_color(&style_screen_1_label_11_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_dir(&style_screen_1_label_11_main, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_style_set_bg_opa(&style_screen_1_label_11_main, LV_STATE_DEFAULT, 0);
	lv_style_set_text_color(&style_screen_1_label_11_main, LV_STATE_DEFAULT, lv_color_make(0x00, 0x00, 0x00));
	lv_style_set_text_font(&style_screen_1_label_11_main, LV_STATE_DEFAULT, &lv_font_Slideyouran_Regular_20);
	lv_style_set_text_letter_space(&style_screen_1_label_11_main, LV_STATE_DEFAULT, 2);
	lv_style_set_pad_left(&style_screen_1_label_11_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_right(&style_screen_1_label_11_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_top(&style_screen_1_label_11_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_bottom(&style_screen_1_label_11_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1_label_11, LV_LABEL_PART_MAIN, &style_screen_1_label_11_main);
	lv_obj_set_pos(ui->screen_1_label_11, 0, 61);
	lv_obj_set_size(ui->screen_1_label_11, 100, 0);

	//Write codes screen_1_label_10
	ui->screen_1_label_10 = lv_label_create(ui->screen_1_cont_2, NULL);
	lv_label_set_text(ui->screen_1_label_10, "星期日 ");
	lv_label_set_long_mode(ui->screen_1_label_10, LV_LABEL_LONG_BREAK);
	lv_label_set_align(ui->screen_1_label_10, LV_LABEL_ALIGN_CENTER);

	//Write style LV_LABEL_PART_MAIN for screen_1_label_10
	static lv_style_t style_screen_1_label_10_main;
	lv_style_reset(&style_screen_1_label_10_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_label_10_main
	lv_style_set_radius(&style_screen_1_label_10_main, LV_STATE_DEFAULT, 0);
	lv_style_set_bg_color(&style_screen_1_label_10_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_color(&style_screen_1_label_10_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_dir(&style_screen_1_label_10_main, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_style_set_bg_opa(&style_screen_1_label_10_main, LV_STATE_DEFAULT, 0);
	lv_style_set_text_color(&style_screen_1_label_10_main, LV_STATE_DEFAULT, lv_color_make(0x00, 0x00, 0x00));
	lv_style_set_text_font(&style_screen_1_label_10_main, LV_STATE_DEFAULT, &lv_font_Slideyouran_Regular_20);
	lv_style_set_text_letter_space(&style_screen_1_label_10_main, LV_STATE_DEFAULT, 2);
	lv_style_set_pad_left(&style_screen_1_label_10_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_right(&style_screen_1_label_10_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_top(&style_screen_1_label_10_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_bottom(&style_screen_1_label_10_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1_label_10, LV_LABEL_PART_MAIN, &style_screen_1_label_10_main);
	lv_obj_set_pos(ui->screen_1_label_10, 0, 34);
	lv_obj_set_size(ui->screen_1_label_10, 100, 0);

	//Write codes screen_1_label_9
	ui->screen_1_label_9 = lv_label_create(ui->screen_1_cont_2, NULL);
	lv_label_set_text(ui->screen_1_label_9, "2025-06-13");
	lv_label_set_long_mode(ui->screen_1_label_9, LV_LABEL_LONG_BREAK);
	lv_label_set_align(ui->screen_1_label_9, LV_LABEL_ALIGN_CENTER);

	//Write style LV_LABEL_PART_MAIN for screen_1_label_9
	static lv_style_t style_screen_1_label_9_main;
	lv_style_reset(&style_screen_1_label_9_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_label_9_main
	lv_style_set_radius(&style_screen_1_label_9_main, LV_STATE_DEFAULT, 0);
	lv_style_set_bg_color(&style_screen_1_label_9_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_color(&style_screen_1_label_9_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_dir(&style_screen_1_label_9_main, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_style_set_bg_opa(&style_screen_1_label_9_main, LV_STATE_DEFAULT, 0);
	lv_style_set_text_color(&style_screen_1_label_9_main, LV_STATE_DEFAULT, lv_color_make(0x00, 0x00, 0x00));
	lv_style_set_text_font(&style_screen_1_label_9_main, LV_STATE_DEFAULT, &lv_font_Slideyouran_Regular_20);
	lv_style_set_text_letter_space(&style_screen_1_label_9_main, LV_STATE_DEFAULT, 2);
	lv_style_set_pad_left(&style_screen_1_label_9_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_right(&style_screen_1_label_9_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_top(&style_screen_1_label_9_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_bottom(&style_screen_1_label_9_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1_label_9, LV_LABEL_PART_MAIN, &style_screen_1_label_9_main);
	lv_obj_set_pos(ui->screen_1_label_9, 0, 0);
	lv_obj_set_size(ui->screen_1_label_9, 138, 0);
	lv_cont_set_layout(ui->screen_1_cont_2, LV_LAYOUT_OFF);
	lv_cont_set_fit(ui->screen_1_cont_2, LV_FIT_NONE);

	//Write codes screen_1_cont_3
	ui->screen_1_cont_3 = lv_cont_create(ui->screen_1, NULL);

	//Write style LV_CONT_PART_MAIN for screen_1_cont_3
	static lv_style_t style_screen_1_cont_3_main;
	lv_style_reset(&style_screen_1_cont_3_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_cont_3_main
	lv_style_set_radius(&style_screen_1_cont_3_main, LV_STATE_DEFAULT, 20);
	lv_style_set_bg_color(&style_screen_1_cont_3_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_color(&style_screen_1_cont_3_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_dir(&style_screen_1_cont_3_main, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_style_set_bg_opa(&style_screen_1_cont_3_main, LV_STATE_DEFAULT, 0);
	lv_style_set_border_color(&style_screen_1_cont_3_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xf5, 0xf5));
	lv_style_set_border_width(&style_screen_1_cont_3_main, LV_STATE_DEFAULT, 2);
	lv_style_set_border_opa(&style_screen_1_cont_3_main, LV_STATE_DEFAULT, 255);
	lv_style_set_pad_left(&style_screen_1_cont_3_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_right(&style_screen_1_cont_3_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_top(&style_screen_1_cont_3_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_bottom(&style_screen_1_cont_3_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1_cont_3, LV_CONT_PART_MAIN, &style_screen_1_cont_3_main);
	lv_obj_set_pos(ui->screen_1_cont_3, 97, 116);
	lv_obj_set_size(ui->screen_1_cont_3, 200, 100);
	lv_obj_set_click(ui->screen_1_cont_3, false);

	//Write codes screen_1_label_19
	ui->screen_1_label_19 = lv_label_create(ui->screen_1_cont_3, NULL);
	lv_label_set_text(ui->screen_1_label_19, "%RH");
	lv_label_set_long_mode(ui->screen_1_label_19, LV_LABEL_LONG_BREAK);
	lv_label_set_align(ui->screen_1_label_19, LV_LABEL_ALIGN_CENTER);

	//Write style LV_LABEL_PART_MAIN for screen_1_label_19
	static lv_style_t style_screen_1_label_19_main;
	lv_style_reset(&style_screen_1_label_19_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_label_19_main
	lv_style_set_radius(&style_screen_1_label_19_main, LV_STATE_DEFAULT, 0);
	lv_style_set_bg_color(&style_screen_1_label_19_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_color(&style_screen_1_label_19_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_dir(&style_screen_1_label_19_main, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_style_set_bg_opa(&style_screen_1_label_19_main, LV_STATE_DEFAULT, 0);
	lv_style_set_text_color(&style_screen_1_label_19_main, LV_STATE_DEFAULT, lv_color_make(0x00, 0x00, 0x00));
	lv_style_set_text_font(&style_screen_1_label_19_main, LV_STATE_DEFAULT, &lv_font_Slideyouran_Regular_15);
	lv_style_set_text_letter_space(&style_screen_1_label_19_main, LV_STATE_DEFAULT, 2);
	lv_style_set_pad_left(&style_screen_1_label_19_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_right(&style_screen_1_label_19_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_top(&style_screen_1_label_19_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_bottom(&style_screen_1_label_19_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1_label_19, LV_LABEL_PART_MAIN, &style_screen_1_label_19_main);
	lv_obj_set_pos(ui->screen_1_label_19, 163, 73);
	lv_obj_set_size(ui->screen_1_label_19, 33, 0);

	//Write codes screen_1_label_18
	ui->screen_1_label_18 = lv_label_create(ui->screen_1_cont_3, NULL);
	lv_label_set_text(ui->screen_1_label_18, "77");
	lv_label_set_long_mode(ui->screen_1_label_18, LV_LABEL_LONG_BREAK);
	lv_label_set_align(ui->screen_1_label_18, LV_LABEL_ALIGN_CENTER);

	//Write style LV_LABEL_PART_MAIN for screen_1_label_18
	static lv_style_t style_screen_1_label_18_main;
	lv_style_reset(&style_screen_1_label_18_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_label_18_main
	lv_style_set_radius(&style_screen_1_label_18_main, LV_STATE_DEFAULT, 0);
	lv_style_set_bg_color(&style_screen_1_label_18_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_color(&style_screen_1_label_18_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_dir(&style_screen_1_label_18_main, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_style_set_bg_opa(&style_screen_1_label_18_main, LV_STATE_DEFAULT, 0);
	lv_style_set_text_color(&style_screen_1_label_18_main, LV_STATE_DEFAULT, lv_color_make(0x00, 0x00, 0x00));
	lv_style_set_text_font(&style_screen_1_label_18_main, LV_STATE_DEFAULT, &lv_font_Slideyouran_Regular_20);
	lv_style_set_text_letter_space(&style_screen_1_label_18_main, LV_STATE_DEFAULT, 2);
	lv_style_set_pad_left(&style_screen_1_label_18_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_right(&style_screen_1_label_18_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_top(&style_screen_1_label_18_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_bottom(&style_screen_1_label_18_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1_label_18, LV_LABEL_PART_MAIN, &style_screen_1_label_18_main);
	lv_obj_set_pos(ui->screen_1_label_18, 133, 67);
	lv_obj_set_size(ui->screen_1_label_18, 33, 0);

	//Write codes screen_1_label_17
	ui->screen_1_label_17 = lv_label_create(ui->screen_1_cont_3, NULL);
	lv_label_set_text(ui->screen_1_label_17, "℃ ");
	lv_label_set_long_mode(ui->screen_1_label_17, LV_LABEL_LONG_BREAK);
	lv_label_set_align(ui->screen_1_label_17, LV_LABEL_ALIGN_CENTER);

	//Write style LV_LABEL_PART_MAIN for screen_1_label_17
	static lv_style_t style_screen_1_label_17_main;
	lv_style_reset(&style_screen_1_label_17_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_label_17_main
	lv_style_set_radius(&style_screen_1_label_17_main, LV_STATE_DEFAULT, 0);
	lv_style_set_bg_color(&style_screen_1_label_17_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_color(&style_screen_1_label_17_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_dir(&style_screen_1_label_17_main, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_style_set_bg_opa(&style_screen_1_label_17_main, LV_STATE_DEFAULT, 0);
	lv_style_set_text_color(&style_screen_1_label_17_main, LV_STATE_DEFAULT, lv_color_make(0x00, 0x00, 0x00));
	lv_style_set_text_font(&style_screen_1_label_17_main, LV_STATE_DEFAULT, &lv_font_Slideyouran_Regular_20);
	lv_style_set_text_letter_space(&style_screen_1_label_17_main, LV_STATE_DEFAULT, 2);
	lv_style_set_pad_left(&style_screen_1_label_17_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_right(&style_screen_1_label_17_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_top(&style_screen_1_label_17_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_bottom(&style_screen_1_label_17_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1_label_17, LV_LABEL_PART_MAIN, &style_screen_1_label_17_main);
	lv_obj_set_pos(ui->screen_1_label_17, 154, 19);
	lv_obj_set_size(ui->screen_1_label_17, 44, 0);

	//Write codes screen_1_label_16
	ui->screen_1_label_16 = lv_label_create(ui->screen_1_cont_3, NULL);
	lv_label_set_text(ui->screen_1_label_16, "24");
	lv_label_set_long_mode(ui->screen_1_label_16, LV_LABEL_LONG_BREAK);
	lv_label_set_align(ui->screen_1_label_16, LV_LABEL_ALIGN_CENTER);

	//Write style LV_LABEL_PART_MAIN for screen_1_label_16
	static lv_style_t style_screen_1_label_16_main;
	lv_style_reset(&style_screen_1_label_16_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_label_16_main
	lv_style_set_radius(&style_screen_1_label_16_main, LV_STATE_DEFAULT, 0);
	lv_style_set_bg_color(&style_screen_1_label_16_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_color(&style_screen_1_label_16_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_dir(&style_screen_1_label_16_main, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_style_set_bg_opa(&style_screen_1_label_16_main, LV_STATE_DEFAULT, 0);
	lv_style_set_text_color(&style_screen_1_label_16_main, LV_STATE_DEFAULT, lv_color_make(0x00, 0x00, 0x00));
	lv_style_set_text_font(&style_screen_1_label_16_main, LV_STATE_DEFAULT, &lv_font_Slideyouran_Regular_20);
	lv_style_set_text_letter_space(&style_screen_1_label_16_main, LV_STATE_DEFAULT, 2);
	lv_style_set_pad_left(&style_screen_1_label_16_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_right(&style_screen_1_label_16_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_top(&style_screen_1_label_16_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_bottom(&style_screen_1_label_16_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1_label_16, LV_LABEL_PART_MAIN, &style_screen_1_label_16_main);
	lv_obj_set_pos(ui->screen_1_label_16, 135, 17);
	lv_obj_set_size(ui->screen_1_label_16, 33, 0);

	//Write codes screen_1_label_15
	ui->screen_1_label_15 = lv_label_create(ui->screen_1_cont_3, NULL);
	lv_label_set_text(ui->screen_1_label_15, "5000");
	lv_label_set_long_mode(ui->screen_1_label_15, LV_LABEL_LONG_BREAK);
	lv_label_set_align(ui->screen_1_label_15, LV_LABEL_ALIGN_CENTER);

	//Write style LV_LABEL_PART_MAIN for screen_1_label_15
	static lv_style_t style_screen_1_label_15_main;
	lv_style_reset(&style_screen_1_label_15_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_label_15_main
	lv_style_set_radius(&style_screen_1_label_15_main, LV_STATE_DEFAULT, 0);
	lv_style_set_bg_color(&style_screen_1_label_15_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_color(&style_screen_1_label_15_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_dir(&style_screen_1_label_15_main, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_style_set_bg_opa(&style_screen_1_label_15_main, LV_STATE_DEFAULT, 0);
	lv_style_set_text_color(&style_screen_1_label_15_main, LV_STATE_DEFAULT, lv_color_make(0x00, 0x00, 0x00));
	lv_style_set_text_font(&style_screen_1_label_15_main, LV_STATE_DEFAULT, &lv_font_Slideyouran_Regular_20);
	lv_style_set_text_letter_space(&style_screen_1_label_15_main, LV_STATE_DEFAULT, 2);
	lv_style_set_pad_left(&style_screen_1_label_15_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_right(&style_screen_1_label_15_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_top(&style_screen_1_label_15_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_bottom(&style_screen_1_label_15_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1_label_15, LV_LABEL_PART_MAIN, &style_screen_1_label_15_main);
	lv_obj_set_pos(ui->screen_1_label_15, 8, 65);
	lv_obj_set_size(ui->screen_1_label_15, 100, 0);

	//Write codes screen_1_label_14
	ui->screen_1_label_14 = lv_label_create(ui->screen_1_cont_3, NULL);
	lv_label_set_text(ui->screen_1_label_14, "flux");
	lv_label_set_long_mode(ui->screen_1_label_14, LV_LABEL_LONG_BREAK);
	lv_label_set_align(ui->screen_1_label_14, LV_LABEL_ALIGN_CENTER);

	//Write style LV_LABEL_PART_MAIN for screen_1_label_14
	static lv_style_t style_screen_1_label_14_main;
	lv_style_reset(&style_screen_1_label_14_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_label_14_main
	lv_style_set_radius(&style_screen_1_label_14_main, LV_STATE_DEFAULT, 0);
	lv_style_set_bg_color(&style_screen_1_label_14_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_color(&style_screen_1_label_14_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_dir(&style_screen_1_label_14_main, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_style_set_bg_opa(&style_screen_1_label_14_main, LV_STATE_DEFAULT, 0);
	lv_style_set_text_color(&style_screen_1_label_14_main, LV_STATE_DEFAULT, lv_color_make(0x00, 0x00, 0x00));
	lv_style_set_text_font(&style_screen_1_label_14_main, LV_STATE_DEFAULT, &lv_font_Slideyouran_Regular_20);
	lv_style_set_text_letter_space(&style_screen_1_label_14_main, LV_STATE_DEFAULT, 2);
	lv_style_set_pad_left(&style_screen_1_label_14_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_right(&style_screen_1_label_14_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_top(&style_screen_1_label_14_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_bottom(&style_screen_1_label_14_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1_label_14, LV_LABEL_PART_MAIN, &style_screen_1_label_14_main);
	lv_obj_set_pos(ui->screen_1_label_14, 77, 66);
	lv_obj_set_size(ui->screen_1_label_14, 44, 0);

	//Write codes screen_1_label_13
	ui->screen_1_label_13 = lv_label_create(ui->screen_1_cont_3, NULL);
	lv_label_set_text(ui->screen_1_label_13, "PPM");
	lv_label_set_long_mode(ui->screen_1_label_13, LV_LABEL_LONG_BREAK);
	lv_label_set_align(ui->screen_1_label_13, LV_LABEL_ALIGN_CENTER);

	//Write style LV_LABEL_PART_MAIN for screen_1_label_13
	static lv_style_t style_screen_1_label_13_main;
	lv_style_reset(&style_screen_1_label_13_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_label_13_main
	lv_style_set_radius(&style_screen_1_label_13_main, LV_STATE_DEFAULT, 0);
	lv_style_set_bg_color(&style_screen_1_label_13_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_color(&style_screen_1_label_13_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_dir(&style_screen_1_label_13_main, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_style_set_bg_opa(&style_screen_1_label_13_main, LV_STATE_DEFAULT, 0);
	lv_style_set_text_color(&style_screen_1_label_13_main, LV_STATE_DEFAULT, lv_color_make(0x00, 0x00, 0x00));
	lv_style_set_text_font(&style_screen_1_label_13_main, LV_STATE_DEFAULT, &lv_font_Slideyouran_Regular_20);
	lv_style_set_text_letter_space(&style_screen_1_label_13_main, LV_STATE_DEFAULT, 2);
	lv_style_set_pad_left(&style_screen_1_label_13_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_right(&style_screen_1_label_13_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_top(&style_screen_1_label_13_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_bottom(&style_screen_1_label_13_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1_label_13, LV_LABEL_PART_MAIN, &style_screen_1_label_13_main);
	lv_obj_set_pos(ui->screen_1_label_13, 74, 17);
	lv_obj_set_size(ui->screen_1_label_13, 39, 0);

	//Write codes screen_1_label_12
	ui->screen_1_label_12 = lv_label_create(ui->screen_1_cont_3, NULL);
	lv_label_set_text(ui->screen_1_label_12, "389");
	lv_label_set_long_mode(ui->screen_1_label_12, LV_LABEL_LONG_BREAK);
	lv_label_set_align(ui->screen_1_label_12, LV_LABEL_ALIGN_CENTER);

	//Write style LV_LABEL_PART_MAIN for screen_1_label_12
	static lv_style_t style_screen_1_label_12_main;
	lv_style_reset(&style_screen_1_label_12_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_label_12_main
	lv_style_set_radius(&style_screen_1_label_12_main, LV_STATE_DEFAULT, 0);
	lv_style_set_bg_color(&style_screen_1_label_12_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_color(&style_screen_1_label_12_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_bg_grad_dir(&style_screen_1_label_12_main, LV_STATE_DEFAULT, LV_GRAD_DIR_VER);
	lv_style_set_bg_opa(&style_screen_1_label_12_main, LV_STATE_DEFAULT, 0);
	lv_style_set_text_color(&style_screen_1_label_12_main, LV_STATE_DEFAULT, lv_color_make(0x00, 0x00, 0x00));
	lv_style_set_text_font(&style_screen_1_label_12_main, LV_STATE_DEFAULT, &lv_font_Slideyouran_Regular_20);
	lv_style_set_text_letter_space(&style_screen_1_label_12_main, LV_STATE_DEFAULT, 2);
	lv_style_set_pad_left(&style_screen_1_label_12_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_right(&style_screen_1_label_12_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_top(&style_screen_1_label_12_main, LV_STATE_DEFAULT, 0);
	lv_style_set_pad_bottom(&style_screen_1_label_12_main, LV_STATE_DEFAULT, 0);
	lv_obj_add_style(ui->screen_1_label_12, LV_LABEL_PART_MAIN, &style_screen_1_label_12_main);
	lv_obj_set_pos(ui->screen_1_label_12, 37, 13);
	lv_obj_set_size(ui->screen_1_label_12, 44, 0);

	//Write codes screen_1_img_10
	ui->screen_1_img_10 = lv_img_create(ui->screen_1_cont_3, NULL);

	//Write style LV_IMG_PART_MAIN for screen_1_img_10
	static lv_style_t style_screen_1_img_10_main;
	lv_style_reset(&style_screen_1_img_10_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_img_10_main
	lv_style_set_image_recolor(&style_screen_1_img_10_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_image_recolor_opa(&style_screen_1_img_10_main, LV_STATE_DEFAULT, 0);
	lv_style_set_image_opa(&style_screen_1_img_10_main, LV_STATE_DEFAULT, 255);
	lv_obj_add_style(ui->screen_1_img_10, LV_IMG_PART_MAIN, &style_screen_1_img_10_main);
	lv_obj_set_pos(ui->screen_1_img_10, 121, 70);
	lv_obj_set_size(ui->screen_1_img_10, 20, 20);
	lv_obj_set_click(ui->screen_1_img_10, true);
	lv_img_set_src(ui->screen_1_img_10,&_HMP_alpha_20x20);
	lv_img_set_pivot(ui->screen_1_img_10, 0,0);
	lv_img_set_angle(ui->screen_1_img_10, 0);

	//Write codes screen_1_img_9
	ui->screen_1_img_9 = lv_img_create(ui->screen_1_cont_3, NULL);

	//Write style LV_IMG_PART_MAIN for screen_1_img_9
	static lv_style_t style_screen_1_img_9_main;
	lv_style_reset(&style_screen_1_img_9_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_img_9_main
	lv_style_set_image_recolor(&style_screen_1_img_9_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_image_recolor_opa(&style_screen_1_img_9_main, LV_STATE_DEFAULT, 0);
	lv_style_set_image_opa(&style_screen_1_img_9_main, LV_STATE_DEFAULT, 255);
	lv_obj_add_style(ui->screen_1_img_9, LV_IMG_PART_MAIN, &style_screen_1_img_9_main);
	lv_obj_set_pos(ui->screen_1_img_9, 0, 0);
	lv_obj_set_size(ui->screen_1_img_9, 40, 40);
	lv_obj_set_click(ui->screen_1_img_9, true);
	lv_img_set_src(ui->screen_1_img_9,&_CO2_alpha_40x40);
	lv_img_set_pivot(ui->screen_1_img_9, 0,0);
	lv_img_set_angle(ui->screen_1_img_9, 0);

	//Write codes screen_1_img_8
	ui->screen_1_img_8 = lv_img_create(ui->screen_1_cont_3, NULL);

	//Write style LV_IMG_PART_MAIN for screen_1_img_8
	static lv_style_t style_screen_1_img_8_main;
	lv_style_reset(&style_screen_1_img_8_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_img_8_main
	lv_style_set_image_recolor(&style_screen_1_img_8_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_image_recolor_opa(&style_screen_1_img_8_main, LV_STATE_DEFAULT, 0);
	lv_style_set_image_opa(&style_screen_1_img_8_main, LV_STATE_DEFAULT, 255);
	lv_obj_add_style(ui->screen_1_img_8, LV_IMG_PART_MAIN, &style_screen_1_img_8_main);
	lv_obj_set_pos(ui->screen_1_img_8, 0, 54);
	lv_obj_set_size(ui->screen_1_img_8, 40, 40);
	lv_obj_set_click(ui->screen_1_img_8, true);
	lv_img_set_src(ui->screen_1_img_8,&_SUN_alpha_40x40);
	lv_img_set_pivot(ui->screen_1_img_8, 0,0);
	lv_img_set_angle(ui->screen_1_img_8, 0);

	//Write codes screen_1_img_7
	ui->screen_1_img_7 = lv_img_create(ui->screen_1_cont_3, NULL);

	//Write style LV_IMG_PART_MAIN for screen_1_img_7
	static lv_style_t style_screen_1_img_7_main;
	lv_style_reset(&style_screen_1_img_7_main);

	//Write style state: LV_STATE_DEFAULT for style_screen_1_img_7_main
	lv_style_set_image_recolor(&style_screen_1_img_7_main, LV_STATE_DEFAULT, lv_color_make(0xff, 0xff, 0xff));
	lv_style_set_image_recolor_opa(&style_screen_1_img_7_main, LV_STATE_DEFAULT, 0);
	lv_style_set_image_opa(&style_screen_1_img_7_main, LV_STATE_DEFAULT, 255);
	lv_obj_add_style(ui->screen_1_img_7, LV_IMG_PART_MAIN, &style_screen_1_img_7_main);
	lv_obj_set_pos(ui->screen_1_img_7, 120, 19);
	lv_obj_set_size(ui->screen_1_img_7, 20, 20);
	lv_obj_set_click(ui->screen_1_img_7, true);
	lv_img_set_src(ui->screen_1_img_7,&_TMP_alpha_20x20);
	lv_img_set_pivot(ui->screen_1_img_7, 0,0);
	lv_img_set_angle(ui->screen_1_img_7, 0);
	lv_cont_set_layout(ui->screen_1_cont_3, LV_LAYOUT_OFF);
	lv_cont_set_fit(ui->screen_1_cont_3, LV_FIT_NONE);
}