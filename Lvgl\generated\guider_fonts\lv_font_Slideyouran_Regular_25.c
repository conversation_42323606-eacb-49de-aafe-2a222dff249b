/*******************************************************************************
 * Size: 25 px
 * Bpp: 4
 * Opts: 
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_SLIDEYOURAN_REGULAR_25
#define LV_FONT_SLIDEYOURAN_REGULAR_25 1
#endif

#if LV_FONT_SLIDEYOURAN_REGULAR_25

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0021 "!" */
    0x0, 0x0, 0x0, 0xe7, 0x0, 0xe, 0xf8, 0x0,
    0xef, 0xd0, 0xf, 0xfd, 0x0, 0xff, 0xd0, 0xf,
    0xfd, 0x0, 0xdf, 0xc0, 0xc, 0xfb, 0x0, 0x9f,
    0xb0, 0x7, 0xf7, 0x0, 0x4f, 0x10, 0x0, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xa7, 0x0,
    0x2f, 0xfe, 0x0, 0x8a, 0x60, 0x0, 0x0, 0x0,

    /* U+0022 "\"" */
    0x0, 0x0, 0x0, 0x5, 0x0, 0x99, 0xe, 0x0,
    0xcd, 0x1f, 0x30, 0xde, 0x3f, 0x30, 0xce, 0x4f,
    0x10, 0x4c, 0xc, 0x0, 0x2, 0x1, 0x0, 0x0,

    /* U+002D "-" */
    0x2b, 0xde, 0xef, 0xff, 0xff, 0x31, 0xff, 0xff,
    0xde, 0xff, 0xe6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+002E "." */
    0x0, 0x10, 0x7f, 0xf7, 0xbf, 0xfa, 0x3b, 0xd1,

    /* U+0030 "0" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xcb,
    0xda, 0x20, 0x0, 0xa5, 0x0, 0xf, 0xa0, 0x2,
    0xc0, 0x0, 0xc, 0xf0, 0x6, 0x90, 0x0, 0xb,
    0xf0, 0x9, 0x70, 0x0, 0xc, 0xf0, 0xb, 0x60,
    0x0, 0xc, 0xf0, 0xd, 0x50, 0x0, 0xf, 0xf0,
    0xf, 0x60, 0x0, 0x5f, 0xc0, 0x1f, 0x60, 0x0,
    0xcf, 0x60, 0xb, 0xc4, 0x17, 0xfc, 0x0, 0x0,
    0x9f, 0xea, 0x10, 0x0,

    /* U+0031 "1" */
    0x0, 0x0, 0x34, 0x0, 0x5, 0x9f, 0xe0, 0x0,
    0x1, 0xfa, 0x0, 0x0, 0xf, 0x80, 0x0, 0x2,
    0xf5, 0x0, 0x0, 0x3f, 0x40, 0x0, 0x4, 0xf3,
    0x0, 0x0, 0x7f, 0x30, 0x0, 0xb, 0xf1, 0x0,
    0x0, 0xdf, 0x0, 0x0, 0xf, 0xf0, 0x3, 0xbc,
    0xff, 0xe3, 0x0, 0x0, 0x0, 0x0,

    /* U+0032 "2" */
    0x0, 0x68, 0x89, 0x20, 0x8, 0x20, 0x5, 0xc0,
    0x0, 0x0, 0x6, 0xf1, 0x0, 0x0, 0xd, 0xe0,
    0x0, 0x0, 0x9f, 0x50, 0x0, 0x4, 0xf7, 0x0,
    0x0, 0x1e, 0x80, 0x0, 0x0, 0xca, 0x0, 0x0,
    0x9, 0xa0, 0x0, 0x0, 0x2f, 0x10, 0x0, 0x0,
    0x9f, 0xef, 0xfe, 0x40, 0x26, 0x20, 0x0, 0x0,

    /* U+0033 "3" */
    0x0, 0x5, 0x61, 0x5, 0xb8, 0xcc, 0x8, 0x0,
    0x5d, 0x0, 0x0, 0x7a, 0x0, 0x3, 0xe3, 0x1,
    0x9f, 0x60, 0x7, 0x89, 0xea, 0x0, 0x0, 0x5d,
    0x0, 0x0, 0x5c, 0x0, 0x0, 0xa9, 0x60, 0x1a,
    0xd2, 0x5d, 0xe7, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x2f, 0x10,
    0x0, 0x0, 0xc, 0xb0, 0x0, 0x0, 0x6, 0xea,
    0x10, 0x0, 0x0, 0xe5, 0x81, 0x0, 0x0, 0x89,
    0x9, 0x0, 0x0, 0x1d, 0x0, 0x90, 0x0, 0x6,
    0x63, 0x5c, 0xab, 0xb0, 0x7f, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x9, 0x0, 0x0, 0x0, 0x1, 0x70,
    0x0, 0x0, 0x0, 0x36, 0x0, 0x0, 0x0, 0x3,
    0x30, 0x0, 0x0,

    /* U+0035 "5" */
    0x4, 0x31, 0x11, 0x10, 0x7, 0xff, 0xff, 0xf1,
    0x8, 0x80, 0x13, 0x20, 0x9, 0x70, 0x0, 0x0,
    0x9, 0x50, 0x0, 0x0, 0xb, 0xac, 0xed, 0x30,
    0x6, 0x83, 0x28, 0xb0, 0x0, 0x0, 0x3, 0xd0,
    0x0, 0x0, 0x4, 0xd0, 0x0, 0x0, 0x9, 0x90,
    0x32, 0x0, 0x2e, 0x20, 0x2c, 0x68, 0xd5, 0x0,
    0x7, 0xfc, 0x40, 0x0, 0x0, 0x10, 0x0, 0x0,

    /* U+0036 "6" */
    0x0, 0x0, 0x9d, 0x70, 0x0, 0x1c, 0xe8, 0x50,
    0x0, 0xbd, 0x10, 0x0, 0x4, 0xf1, 0x0, 0x0,
    0x9, 0xc6, 0x9c, 0x50, 0xc, 0x60, 0x4, 0xf2,
    0xe, 0x40, 0x0, 0xf4, 0xf, 0x30, 0x1, 0xf3,
    0x2f, 0x40, 0x6, 0xf0, 0x1f, 0x50, 0x2e, 0x70,
    0x4, 0xdc, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0037 "7" */
    0x2, 0x0, 0x12, 0x22, 0x1f, 0xff, 0xff, 0xfe,
    0xa, 0xdd, 0xb9, 0xc8, 0x0, 0x0, 0x0, 0xd0,
    0x0, 0x0, 0x7, 0x60, 0x0, 0x0, 0x1c, 0x0,
    0x0, 0x0, 0x85, 0x0, 0x0, 0x0, 0xc0, 0x0,
    0x0, 0x6, 0x70, 0x0, 0x0, 0xc, 0x10, 0x0,
    0x0, 0x2c, 0x0, 0x0, 0x0, 0x47, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+0038 "8" */
    0x0, 0x3, 0x50, 0x0, 0x6, 0x80, 0x97, 0x2,
    0xc0, 0x1, 0xe0, 0x67, 0x0, 0x2d, 0x2, 0xb0,
    0x8, 0x10, 0x8, 0xc9, 0x10, 0x0, 0x28, 0xab,
    0x10, 0x46, 0x0, 0xba, 0x9, 0x0, 0xb, 0xb4,
    0x90, 0x0, 0xf8, 0x2d, 0x0, 0x8f, 0x40, 0x7d,
    0xcf, 0x80,

    /* U+0039 "9" */
    0x0, 0x47, 0x20, 0x8, 0x22, 0xf2, 0x26, 0x0,
    0xe7, 0x74, 0x0, 0xe7, 0x95, 0x0, 0xf5, 0x96,
    0x1, 0xf4, 0x4c, 0x89, 0xf4, 0x2, 0x32, 0xf2,
    0x0, 0x4, 0xf0, 0x0, 0x4, 0xd0, 0x60, 0x8,
    0x80, 0x58, 0xa9, 0x0, 0x0, 0x0, 0x0,

    /* U+003F "?" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfa, 0x10,
    0x8, 0xc5, 0x9f, 0xb0, 0x38, 0x0, 0x2f, 0xf0,
    0x10, 0x0, 0x2f, 0xf0, 0x0, 0x0, 0x5f, 0xe0,
    0x0, 0x0, 0xcf, 0x70, 0x0, 0x6, 0xf8, 0x0,
    0x0, 0x2f, 0x90, 0x0, 0x0, 0x8f, 0x20, 0x0,
    0x0, 0xdf, 0x0, 0x0, 0x0, 0xfe, 0x0, 0x0,
    0x0, 0xfe, 0x0, 0x0, 0x0, 0xfe, 0x0, 0x0,
    0x0, 0x53, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xd8, 0x0, 0x0,
    0x1, 0x82, 0x0, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0xb4, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xc0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x80,
    0x0, 0x0, 0x4, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x9d, 0xbf, 0xf4, 0x0, 0x0, 0xe, 0x78, 0xff,
    0x80, 0x0, 0x4, 0xf2, 0x6f, 0xfa, 0x0, 0x0,
    0xac, 0x3, 0xff, 0xe0, 0x0, 0xf, 0x80, 0xf,
    0xff, 0x10, 0x6, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0xca, 0x32, 0x2c, 0xff, 0x70, 0x1f, 0x50, 0x0,
    0x9f, 0xf8, 0x8, 0xf0, 0x0, 0x9, 0xff, 0x90,
    0xcb, 0x0, 0x0, 0x9f, 0xf8, 0xf, 0x80, 0x0,
    0x2, 0xef, 0x0, 0x93, 0x0, 0x0, 0x0, 0x10,

    /* U+0042 "B" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xaf,
    0xff, 0xfc, 0x20, 0x3, 0xf8, 0x10, 0x3, 0xfe,
    0x0, 0x5f, 0x60, 0x0, 0x4f, 0xf1, 0x8, 0xf5,
    0x0, 0x8, 0xfe, 0x0, 0x9f, 0x40, 0x2, 0xff,
    0x90, 0xa, 0xf3, 0x1, 0xdf, 0xe2, 0x0, 0xbf,
    0xef, 0xff, 0xf4, 0x0, 0xc, 0xfc, 0xdd, 0xdf,
    0xb0, 0x0, 0xdf, 0x0, 0x0, 0xdf, 0x40, 0xe,
    0xf0, 0x0, 0xc, 0xf7, 0x1, 0xfe, 0x0, 0x0,
    0xef, 0x90, 0x5f, 0xb0, 0x0, 0x6f, 0xf8, 0x6,
    0xf7, 0x4, 0xbf, 0xff, 0x30, 0x6f, 0xaf, 0xff,
    0xe9, 0x20, 0x0, 0x10, 0x24, 0x10, 0x0, 0x0,

    /* U+0043 "C" */
    0x0, 0x0, 0x0, 0x5d, 0x0, 0x0, 0x0, 0x18,
    0xfd, 0x90, 0x0, 0x5, 0xff, 0xff, 0xf7, 0x0,
    0x7f, 0x92, 0x9, 0xc5, 0x3, 0xf5, 0x0, 0x0,
    0x0, 0xa, 0xd0, 0x0, 0x0, 0x0, 0xe, 0xc0,
    0x0, 0x0, 0x0, 0xf, 0xb0, 0x0, 0x0, 0x0,
    0xf, 0xb0, 0x0, 0x0, 0x0, 0xe, 0xd0, 0x0,
    0x0, 0x0, 0xd, 0xf0, 0x0, 0x0, 0x0, 0xa,
    0xf5, 0x0, 0x1, 0x0, 0x3, 0xff, 0xbb, 0xd9,
    0x0, 0x0, 0x6e, 0xfc, 0x60, 0x0,

    /* U+0044 "D" */
    0x0, 0x18, 0xdf, 0xd8, 0x0, 0x5, 0xa7, 0x78,
    0xdf, 0xc0, 0x2, 0x90, 0x0, 0xc, 0xf9, 0x6,
    0xf0, 0x0, 0x9, 0xfd, 0x8, 0xf0, 0x0, 0xa,
    0xff, 0x8, 0xf0, 0x0, 0xd, 0xfe, 0x9, 0xf0,
    0x0, 0x1f, 0xfc, 0xa, 0xf0, 0x0, 0x6f, 0xfa,
    0xa, 0xe0, 0x0, 0xaf, 0xf6, 0xc, 0xe0, 0x1,
    0xff, 0xf1, 0xe, 0xb0, 0xa, 0xff, 0xc0, 0xf,
    0x90, 0x4f, 0xff, 0x80, 0x2f, 0xb9, 0xff, 0xff,
    0x20, 0x1f, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x18,
    0x72, 0x10, 0x0,

    /* U+0045 "E" */
    0x0, 0x0, 0x9, 0xfe, 0x30, 0x4, 0x77, 0xcf,
    0xff, 0xd0, 0x7, 0xff, 0xff, 0xff, 0x50, 0xa,
    0xf1, 0x0, 0x0, 0x0, 0xb, 0xf1, 0x0, 0x0,
    0x0, 0xe, 0xf0, 0x0, 0x0, 0x0, 0xf, 0xf0,
    0x0, 0x0, 0x0, 0xf, 0xf2, 0x45, 0x10, 0x0,
    0x2f, 0xff, 0x98, 0x73, 0x0, 0x3f, 0xd2, 0x0,
    0x0, 0x0, 0x4f, 0xc0, 0x0, 0x0, 0x0, 0x4f,
    0xb0, 0x0, 0x0, 0x0, 0x7f, 0xa0, 0x0, 0x0,
    0x0, 0x7f, 0xd9, 0x99, 0x81, 0x0, 0x9, 0xdf,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0046 "F" */
    0x1, 0x0, 0x0, 0x0, 0x0, 0xed, 0xff, 0xff,
    0xf3, 0xe, 0xea, 0xcc, 0x94, 0x2, 0xfc, 0x0,
    0x0, 0x0, 0x4f, 0xd0, 0x0, 0x0, 0x5, 0xfe,
    0x66, 0x53, 0x0, 0x7f, 0xff, 0xff, 0xd0, 0x7,
    0xfb, 0x32, 0x20, 0x0, 0x8f, 0xa0, 0x0, 0x0,
    0x9, 0xf9, 0x0, 0x0, 0x0, 0xaf, 0x80, 0x0,
    0x0, 0x9, 0xf7, 0x0, 0x0, 0x0, 0xaf, 0x60,
    0x0, 0x0, 0xc, 0xf5, 0x0, 0x0, 0x0, 0xaf,
    0x10, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x1, 0x66, 0x0, 0x0, 0x0, 0x7,
    0xfc, 0xcb, 0x20, 0x0, 0xa, 0xf5, 0x0, 0x8d,
    0x0, 0x8, 0xf4, 0x0, 0x0, 0xc0, 0x1, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x8f, 0x10, 0x0, 0x0,
    0x0, 0xe, 0xc0, 0x0, 0x0, 0x0, 0x3, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0x60, 0x7, 0xef,
    0xf7, 0x6, 0xf5, 0x0, 0x0, 0x9f, 0x90, 0x6f,
    0x50, 0x0, 0xb, 0xf8, 0x4, 0xf6, 0x0, 0x0,
    0xdf, 0x70, 0xf, 0xc0, 0x0, 0xe, 0xf5, 0x0,
    0x6f, 0xc6, 0x59, 0xff, 0x20, 0x0, 0x29, 0xef,
    0xff, 0x90, 0x0,

    /* U+0048 "H" */
    0x0, 0x10, 0x0, 0x0, 0x11, 0x0, 0xd, 0x40,
    0x0, 0xc, 0xc0, 0x0, 0xf9, 0x0, 0x0, 0xdf,
    0x20, 0x2f, 0xd0, 0x0, 0xf, 0xf2, 0x6, 0xfd,
    0x0, 0x0, 0xff, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xd0, 0xb, 0xff, 0xff, 0xff, 0xfb, 0x0, 0xef,
    0xd8, 0x75, 0x7f, 0x90, 0xf, 0xf8, 0x0, 0x5,
    0xf8, 0x1, 0xff, 0x70, 0x0, 0x5f, 0x80, 0x1f,
    0xf5, 0x0, 0x7, 0xf7, 0x0, 0xff, 0x40, 0x0,
    0x9f, 0x60, 0xf, 0xf1, 0x0, 0xb, 0xf3, 0x0,
    0xd2, 0x0, 0x0, 0x9d, 0x0, 0x3, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+0049 "I" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xff, 0xff,
    0xe0, 0x0, 0x8a, 0xff, 0x84, 0x0, 0x0, 0xf,
    0xf0, 0x0, 0x0, 0x1, 0xfe, 0x0, 0x0, 0x0,
    0x2f, 0xc0, 0x0, 0x0, 0x3, 0xfb, 0x0, 0x0,
    0x0, 0x4f, 0xa0, 0x0, 0x0, 0x5, 0xf9, 0x0,
    0x0, 0x0, 0x6f, 0x70, 0x0, 0x0, 0x9, 0xf3,
    0x0, 0x0, 0x0, 0xaf, 0x0, 0x0, 0x0, 0xb,
    0xb0, 0x0, 0x0, 0x68, 0xee, 0xb3, 0x0, 0x4f,
    0xff, 0xfe, 0x20, 0x0, 0x0, 0x1, 0x0, 0x0,

    /* U+004A "J" */
    0x0, 0x0, 0x2, 0x80, 0x0, 0x0, 0x8, 0xf1,
    0x0, 0x0, 0x9, 0xf1, 0x0, 0x0, 0xe, 0xf1,
    0x0, 0x0, 0x2f, 0xf0, 0x0, 0x0, 0x4f, 0xe0,
    0x0, 0x0, 0x5f, 0xe0, 0x0, 0x0, 0x8f, 0xd0,
    0x0, 0x0, 0x9f, 0xb0, 0x0, 0x0, 0xbf, 0x80,
    0x0, 0x2, 0xff, 0x50, 0x5b, 0x5c, 0xfb, 0x0,
    0xb, 0xfd, 0x60, 0x0,

    /* U+004B "K" */
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0xdc, 0x0,
    0x0, 0x7b, 0x0, 0xfc, 0x0, 0x7, 0xf7, 0x0,
    0xfa, 0x0, 0x3f, 0xa0, 0x2, 0xf9, 0x0, 0xdc,
    0x0, 0x4, 0xf9, 0xa, 0xe1, 0x0, 0x5, 0xf7,
    0x5f, 0xd6, 0x0, 0x7, 0xf7, 0xe7, 0x8b, 0x0,
    0x9, 0xff, 0xa0, 0x5f, 0x20, 0xb, 0xfa, 0x0,
    0x1f, 0x70, 0xc, 0xf3, 0x0, 0xe, 0xb0, 0xe,
    0xf1, 0x0, 0xb, 0xf0, 0xf, 0xd0, 0x0, 0x6,
    0xf4, 0xd, 0xa0, 0x0, 0x0, 0xf8, 0x1, 0x10,
    0x0, 0x0, 0x42,

    /* U+004C "L" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0x60, 0x0,
    0x0, 0x2, 0xf8, 0x0, 0x0, 0x0, 0x4f, 0x80,
    0x0, 0x0, 0x7, 0xf6, 0x0, 0x0, 0x0, 0x8f,
    0x50, 0x0, 0x0, 0xa, 0xf3, 0x0, 0x0, 0x0,
    0xbf, 0x10, 0x0, 0x0, 0xd, 0xf0, 0x0, 0x0,
    0x0, 0xee, 0x0, 0x0, 0x0, 0xf, 0xd0, 0x0,
    0x0, 0x1, 0xfc, 0x0, 0x0, 0x0, 0xf, 0xa0,
    0x0, 0x0, 0x0, 0xdc, 0x8a, 0xcc, 0xc3, 0x2,
    0xef, 0xff, 0xfd, 0x10,

    /* U+004D "M" */
    0x2, 0x30, 0x0, 0x0, 0x17, 0x0, 0xcf, 0x20,
    0x0, 0x2f, 0xf3, 0xe, 0xfb, 0x0, 0xb, 0xff,
    0x30, 0xfd, 0xf4, 0x3, 0xfc, 0xf3, 0x2f, 0x4f,
    0xe1, 0xc6, 0xaf, 0x23, 0xd0, 0x9f, 0xed, 0xb,
    0xf1, 0x4d, 0x0, 0xaf, 0x80, 0xcf, 0x5, 0xc0,
    0x0, 0x41, 0xd, 0xf0, 0x5b, 0x0, 0x0, 0x0,
    0xee, 0x7, 0xa0, 0x0, 0x0, 0xf, 0xd0, 0x98,
    0x0, 0x0, 0x0, 0xfb, 0xb, 0x70, 0x0, 0x0,
    0xf, 0xa0, 0xd5, 0x0, 0x0, 0x0, 0xf9, 0x8,
    0x40, 0x0, 0x0, 0x6, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+004E "N" */
    0x0, 0x10, 0x0, 0x0, 0x10, 0x4f, 0x73, 0x0,
    0xcc, 0x7, 0xff, 0xe0, 0xf, 0xe0, 0xaf, 0xff,
    0x22, 0xfe, 0xc, 0xfb, 0xf5, 0x3f, 0xe0, 0xed,
    0x7f, 0x95, 0xfe, 0xf, 0xc4, 0xfe, 0x7f, 0xc0,
    0xeb, 0xf, 0xfc, 0xfb, 0xf, 0xa0, 0xcf, 0xff,
    0x92, 0xf8, 0x8, 0xff, 0xf7, 0x4f, 0x60, 0x2f,
    0xff, 0x66, 0xf3, 0x0, 0x6f, 0xf5, 0x7f, 0x20,
    0x2, 0xff, 0x48, 0xf1, 0x0, 0x2, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+004F "O" */
    0x0, 0x0, 0x7e, 0xfd, 0x30, 0x0, 0xb, 0x9b,
    0x8c, 0xe1, 0x0, 0x87, 0x0, 0x2, 0xf9, 0x2,
    0xf1, 0x0, 0x0, 0xed, 0x9, 0xb0, 0x0, 0x0,
    0xee, 0xf, 0x80, 0x0, 0x0, 0xef, 0x4f, 0x60,
    0x0, 0x1, 0xfe, 0x7f, 0x50, 0x0, 0x4, 0xfc,
    0x8f, 0x40, 0x0, 0x8, 0xf9, 0x8f, 0x50, 0x0,
    0xd, 0xf6, 0x6f, 0x70, 0x0, 0x6f, 0xf2, 0x1f,
    0xd1, 0x6, 0xff, 0xb0, 0x6, 0xfe, 0xbd, 0xfd,
    0x10, 0x0, 0x3a, 0xa2, 0x41, 0x0,

    /* U+0050 "P" */
    0x0, 0x27, 0xcf, 0xfd, 0x70, 0x0, 0x7, 0xf9,
    0x68, 0xef, 0x90, 0x0, 0xad, 0x0, 0x7, 0xff,
    0x0, 0xd, 0xd0, 0x0, 0xbf, 0xf1, 0x0, 0xfd,
    0x0, 0x3f, 0xff, 0x0, 0xf, 0xc0, 0x4e, 0xff,
    0x80, 0x1, 0xfe, 0xaf, 0xff, 0xe1, 0x0, 0x3f,
    0xff, 0xff, 0xe3, 0x0, 0x5, 0xf8, 0x77, 0x50,
    0x0, 0x0, 0x8f, 0x60, 0x0, 0x0, 0x0, 0xa,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xbf, 0x30, 0x0,
    0x0, 0x0, 0xd, 0xf1, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x0, 0x0, 0x0, 0x0, 0x0, 0x40, 0x0,
    0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x0, 0xcf, 0xa0, 0x0, 0x3, 0xb6, 0x3f,
    0x80, 0x2, 0xf3, 0x0, 0xaf, 0x0, 0xc9, 0x0,
    0x5, 0xf5, 0x3f, 0x60, 0x0, 0x1f, 0x98, 0xf0,
    0x0, 0x0, 0xfc, 0xbc, 0x0, 0x0, 0xf, 0xda,
    0xa0, 0x0, 0x1, 0xfc, 0x9a, 0x0, 0x0, 0x3f,
    0xa8, 0xc0, 0x0, 0x8, 0xf5, 0x6f, 0x10, 0x2,
    0xfe, 0x3, 0xf9, 0x46, 0xdf, 0x50, 0xc, 0xff,
    0xfe, 0x30, 0x0, 0x5, 0xae, 0xb0, 0x0, 0x0,
    0x0, 0x9f, 0x10, 0x0, 0x0, 0x2, 0xed, 0x81,
    0x0, 0x0, 0x0, 0x66, 0x0,

    /* U+0052 "R" */
    0x0, 0x0, 0x6d, 0xfd, 0x80, 0x0, 0x8f, 0xc4,
    0x6, 0xf5, 0x0, 0xe7, 0x0, 0x9, 0xf8, 0x0,
    0xe7, 0x0, 0xf, 0xf5, 0x2, 0xf6, 0x0, 0x8f,
    0xe0, 0x2, 0xf5, 0x19, 0xff, 0x70, 0x4, 0xfe,
    0xff, 0xf7, 0x0, 0x6, 0xf7, 0x76, 0xe1, 0x0,
    0x7, 0xf1, 0x0, 0xe7, 0x0, 0x9, 0xf0, 0x0,
    0xba, 0x0, 0xa, 0xf0, 0x0, 0x8d, 0x0, 0xb,
    0xd0, 0x0, 0x2f, 0x20, 0xd, 0xc0, 0x0, 0xd,
    0x90, 0x8, 0x80, 0x0, 0x9, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x30,

    /* U+0053 "S" */
    0x0, 0x2, 0x9d, 0xc3, 0x0, 0x6f, 0xb8, 0xca,
    0x5, 0xf3, 0x0, 0x3b, 0xd, 0x70, 0x0, 0x0,
    0xf, 0x40, 0x0, 0x0, 0xd, 0x70, 0x0, 0x0,
    0x1, 0xbc, 0x83, 0x0, 0x0, 0x3, 0x8f, 0xb0,
    0x0, 0x0, 0x8, 0xf8, 0x0, 0x0, 0x6, 0xfb,
    0x0, 0x0, 0x9, 0xf9, 0x0, 0x0, 0xe, 0xf5,
    0x72, 0x14, 0xcf, 0xe0, 0xaf, 0xff, 0xfe, 0x40,
    0x0, 0x0, 0x0, 0x0,

    /* U+0054 "T" */
    0x0, 0x0, 0x7, 0xff, 0xfb, 0x32, 0x96, 0x79,
    0xff, 0xff, 0xfd, 0xc, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x11, 0x0, 0xbb, 0x0, 0x0, 0x0, 0x0,
    0xc, 0x80, 0x0, 0x0, 0x0, 0x0, 0xe7, 0x0,
    0x0, 0x0, 0x0, 0xf, 0x50, 0x0, 0x0, 0x0,
    0x1, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x2f, 0x40,
    0x0, 0x0, 0x0, 0x4, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0x0, 0x0, 0x0, 0x0, 0x7, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x8d, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+0055 "U" */
    0x1, 0x0, 0x0, 0x0, 0x30, 0x2b, 0x0, 0x0,
    0x1, 0xf2, 0x3b, 0x0, 0x0, 0x1, 0xf1, 0x59,
    0x0, 0x0, 0x2, 0xf0, 0x68, 0x0, 0x0, 0x4,
    0xe0, 0x88, 0x0, 0x0, 0x5, 0xd0, 0x98, 0x0,
    0x0, 0x7, 0xc0, 0xb8, 0x0, 0x0, 0x9, 0xc0,
    0xd7, 0x0, 0x0, 0xb, 0xb0, 0xf6, 0x0, 0x0,
    0xf, 0xa0, 0xf5, 0x0, 0x0, 0x4f, 0x70, 0xe7,
    0x0, 0x0, 0xbf, 0x20, 0xdb, 0x23, 0x6b, 0xfb,
    0x0, 0x3c, 0xff, 0xd5, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+0056 "V" */
    0x0, 0x0, 0x0, 0x0, 0x65, 0x80, 0x0, 0x0,
    0x0, 0xe9, 0xa3, 0x0, 0x0, 0x2, 0xf5, 0x69,
    0x0, 0x0, 0x8, 0xe0, 0x3e, 0x0, 0x0, 0xe,
    0x70, 0xf, 0x20, 0x0, 0x5f, 0x0, 0xe, 0x40,
    0x0, 0xc8, 0x0, 0xb, 0x70, 0x4, 0xf1, 0x0,
    0x8, 0xb0, 0xb, 0xa0, 0x0, 0x5, 0xe0, 0x3f,
    0x30, 0x0, 0x1, 0xf1, 0xab, 0x0, 0x0, 0x0,
    0xcb, 0xf4, 0x0, 0x0, 0x0, 0x7f, 0xd0, 0x0,
    0x0, 0x0, 0xb, 0x40, 0x0, 0x0,

    /* U+0057 "W" */
    0x0, 0x0, 0x1, 0x40, 0x0, 0x83, 0x92, 0x0,
    0xa, 0xe0, 0x0, 0xf5, 0x75, 0x0, 0x1f, 0xf0,
    0x4, 0xf2, 0x67, 0x0, 0x6f, 0xf1, 0x9, 0xe0,
    0x58, 0x0, 0xcf, 0xf3, 0xc, 0xa0, 0x3a, 0x2,
    0xf8, 0xf5, 0xf, 0x60, 0x1d, 0x7, 0xd4, 0xf7,
    0x4f, 0x20, 0xf, 0xc, 0x82, 0xfa, 0x9e, 0x0,
    0xe, 0x2f, 0x30, 0xfc, 0xe9, 0x0, 0xd, 0x9d,
    0x0, 0xdf, 0xf4, 0x0, 0xb, 0xf7, 0x0, 0xaf,
    0xf0, 0x0, 0xa, 0xa1, 0x0, 0x5f, 0xb0, 0x0,
    0x2, 0x20, 0x0, 0x3, 0x30, 0x0,

    /* U+0058 "X" */
    0x0, 0x0, 0x0, 0x0, 0x20, 0x1d, 0x10, 0x0,
    0x7, 0xf0, 0x1f, 0xa0, 0x0, 0x1f, 0xe0, 0xb,
    0xf2, 0x0, 0xbf, 0x70, 0x3, 0xf9, 0x7, 0xfe,
    0x0, 0x0, 0xdf, 0x3f, 0xf6, 0x0, 0x0, 0x7f,
    0xef, 0xb0, 0x0, 0x0, 0x1f, 0xff, 0x20, 0x0,
    0x0, 0x1f, 0xf9, 0x0, 0x0, 0x0, 0xaf, 0xfe,
    0x10, 0x0, 0x3, 0xff, 0xef, 0x80, 0x0, 0xd,
    0xf8, 0x5f, 0xe0, 0x0, 0x7f, 0xd0, 0xe, 0xf3,
    0x0, 0xac, 0x30, 0x7, 0xf6, 0x0, 0x21, 0x0,
    0x0, 0x62, 0x0,

    /* U+0059 "Y" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0x0, 0x0,
    0x1, 0xc4, 0xe, 0x60, 0x0, 0x1d, 0xe0, 0x7,
    0xf9, 0x0, 0xcf, 0x40, 0x0, 0xbf, 0xaa, 0xf7,
    0x0, 0x0, 0x9, 0xff, 0x80, 0x0, 0x0, 0x1,
    0xfb, 0x0, 0x0, 0x0, 0x3, 0xf9, 0x0, 0x0,
    0x0, 0x5, 0xf6, 0x0, 0x0, 0x0, 0x7, 0xf4,
    0x0, 0x0, 0x0, 0x8, 0xf3, 0x0, 0x0, 0x0,
    0x9, 0xf1, 0x0, 0x0, 0x0, 0xa, 0xe0, 0x0,
    0x0, 0x0, 0xb, 0xb0, 0x0, 0x0, 0x0, 0x2,
    0x60, 0x0, 0x0,

    /* U+005A "Z" */
    0x0, 0xbf, 0xff, 0xff, 0xe7, 0x0, 0x9d, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x1, 0x1c, 0xe0, 0x0,
    0x0, 0x0, 0x3f, 0x60, 0x0, 0x0, 0x0, 0xcb,
    0x0, 0x0, 0x0, 0x7, 0xe1, 0x0, 0x0, 0x0,
    0x2f, 0x50, 0x0, 0x0, 0x0, 0xcc, 0x0, 0x0,
    0x0, 0x7, 0xf2, 0x0, 0x0, 0x0, 0x2f, 0x80,
    0x0, 0x0, 0x0, 0xae, 0x0, 0x0, 0x0, 0x5,
    0xf6, 0x0, 0x0, 0x0, 0x1f, 0xfd, 0xdd, 0xdc,
    0x40, 0x1d, 0xff, 0xff, 0xff, 0x40, 0x1, 0x31,
    0x0, 0x0, 0x0,

    /* U+0061 "a" */
    0x0, 0x6f, 0xfd, 0x60, 0x6, 0xe5, 0x2, 0xf1,
    0x2, 0x0, 0x0, 0xe3, 0x0, 0x4, 0xaa, 0xf3,
    0x0, 0x97, 0x5, 0xf0, 0x5, 0x90, 0x4, 0xd0,
    0xd, 0x10, 0x8, 0xc0, 0xe, 0x0, 0xa, 0xa0,
    0xd, 0x31, 0x1d, 0xa0, 0x3, 0xef, 0xaf, 0x80,
    0x0, 0x0, 0xf, 0x60, 0x0, 0x0, 0x5, 0x10,

    /* U+0062 "b" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb0, 0x0, 0x0,
    0x0, 0xc0, 0x0, 0x0, 0x0, 0xd0, 0x0, 0x0,
    0x2, 0xb0, 0x0, 0x0, 0x3, 0xa1, 0x53, 0x0,
    0x6, 0xdc, 0x9c, 0x90, 0x9, 0x90, 0x1, 0xf0,
    0xc, 0x50, 0x2, 0xf0, 0xf, 0x40, 0x6, 0xe0,
    0x3f, 0x20, 0x2f, 0xa0, 0x6f, 0xab, 0xfd, 0x10,
    0x6f, 0xfd, 0x80, 0x0, 0x12, 0x0, 0x0, 0x0,

    /* U+0063 "c" */
    0x0, 0x9, 0xfd, 0x70, 0x0, 0xac, 0x39, 0xe4,
    0x6, 0xe1, 0x0, 0x0, 0xe, 0x60, 0x0, 0x0,
    0x3f, 0x20, 0x0, 0x0, 0x5f, 0x0, 0x0, 0x0,
    0x7f, 0x20, 0x0, 0x0, 0x4f, 0x70, 0x0, 0x0,
    0xa, 0xfb, 0x79, 0x50, 0x0, 0x9f, 0xe7, 0x0,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x2, 0x60, 0x0, 0x0, 0x0,
    0x7, 0xf1, 0x0, 0x0, 0x0, 0xa, 0xf0, 0x0,
    0x0, 0x0, 0xe, 0xe0, 0x0, 0x0, 0x0, 0xf,
    0xc0, 0x0, 0x0, 0x0, 0x2f, 0xa0, 0x0, 0x0,
    0x0, 0x3f, 0x80, 0x0, 0x0, 0x0, 0x5f, 0x70,
    0x0, 0x8, 0xed, 0x9f, 0x60, 0x0, 0xb7, 0x4d,
    0xff, 0x50, 0x5, 0xe0, 0x0, 0x6f, 0x50, 0xb,
    0xa0, 0x0, 0x7f, 0x50, 0x1f, 0x70, 0x0, 0x8f,
    0x50, 0x3f, 0x60, 0x0, 0x9f, 0x30, 0x1f, 0x80,
    0x2, 0xcf, 0x10, 0x6, 0xfd, 0xcf, 0xa7, 0x0,
    0x0, 0x37, 0x72, 0x0, 0x0,

    /* U+0065 "e" */
    0x0, 0x6, 0xdf, 0xd5, 0x0, 0x0, 0x7c, 0x20,
    0x4f, 0x80, 0x4, 0xc0, 0x0, 0x7, 0xf2, 0xc,
    0x51, 0x46, 0x8b, 0xf6, 0xf, 0x64, 0x22, 0x22,
    0x10, 0x3f, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0x40, 0x0, 0x0, 0x0,
    0x8, 0xe6, 0x21, 0x23, 0x0, 0x0, 0x5b, 0xef,
    0xf8, 0x0,

    /* U+0066 "f" */
    0x0, 0x0, 0x0, 0x4, 0x72, 0x0, 0x0, 0x0,
    0x9, 0x96, 0x60, 0x0, 0x0, 0x5, 0x40, 0x0,
    0x0, 0x0, 0x0, 0xa1, 0x1, 0x0, 0x0, 0x0,
    0xd, 0x3e, 0xff, 0x30, 0x6b, 0xcd, 0xfe, 0xc5,
    0x52, 0x0, 0x0, 0x2f, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x6c, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0xa9, 0x0, 0x0, 0x0, 0x0, 0xb, 0x90,
    0x0, 0x0, 0x0, 0x0, 0xd8, 0x0, 0x0, 0x0,
    0x0, 0xe, 0x70, 0x0, 0x0, 0x0, 0x0, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0xd, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x19, 0xef, 0xff, 0xcf, 0xb0, 0x0, 0xc, 0xff,
    0xde, 0xfe, 0x61, 0x0, 0x0, 0xef, 0x43, 0xef,
    0xe0, 0x0, 0x0, 0xe, 0xfb, 0xef, 0xf7, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xd5, 0x0, 0x0, 0x2,
    0xfe, 0x27, 0x50, 0x0, 0x0, 0x0, 0x3f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xea, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xdf, 0xff, 0xe8, 0x0,
    0x0, 0x0, 0x3c, 0xf3, 0x29, 0xfb, 0x0, 0x0,
    0x2e, 0x91, 0x0, 0xb, 0xf4, 0x0, 0x1f, 0x70,
    0x0, 0x0, 0xaf, 0x40, 0x6, 0xf2, 0x0, 0x0,
    0x1f, 0xd0, 0x0, 0x2f, 0xa1, 0x0, 0x3d, 0x90,
    0x0, 0x0, 0x2b, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x22, 0x10, 0x0, 0x0, 0x0,

    /* U+0068 "h" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe1, 0x0, 0x0,
    0x1, 0xf0, 0x0, 0x0, 0x3, 0xf0, 0x0, 0x0,
    0x5, 0xd0, 0x0, 0x0, 0x8, 0xa0, 0x0, 0x0,
    0xa, 0x97, 0xdd, 0x50, 0xc, 0xd7, 0x1a, 0xf0,
    0xd, 0x60, 0xa, 0xf0, 0xf, 0x50, 0xb, 0xf0,
    0x1f, 0x40, 0xb, 0xf0, 0x1f, 0x40, 0xb, 0xf0,
    0x3f, 0x30, 0xb, 0xf0, 0x19, 0x0, 0x1, 0x10,

    /* U+0069 "i" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xf2, 0x0,
    0x0, 0x4, 0x0, 0x6c, 0xce, 0xf1, 0x0, 0x0,
    0x4f, 0x10, 0x0, 0x6, 0xf0, 0x0, 0x0, 0x8e,
    0x0, 0x0, 0xb, 0xd0, 0x0, 0x0, 0xed, 0x0,
    0x0, 0xf, 0xd0, 0x0, 0x1, 0xfa, 0x0, 0x0,
    0xb, 0x50, 0x0, 0x0, 0x0, 0x0,

    /* U+006A "j" */
    0x0, 0x0, 0x49, 0x20, 0x0, 0xa, 0xe2, 0x0,
    0x0, 0x0, 0x0, 0x9b, 0xca, 0xc0, 0x0, 0x0,
    0x3b, 0x0, 0x0, 0x6, 0x90, 0x0, 0x0, 0x87,
    0x0, 0x0, 0xa, 0x50, 0x0, 0x0, 0xd4, 0x0,
    0x0, 0xf, 0x20, 0x0, 0x18, 0xf0, 0x0, 0x7f,
    0xfb, 0x0, 0x7, 0xe7, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+006B "k" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x70, 0x0, 0x0,
    0x0, 0x90, 0x0, 0x0, 0x0, 0xa0, 0x0, 0x0,
    0x0, 0xa0, 0x0, 0x0, 0x0, 0xb0, 0x0, 0x0,
    0x1, 0xa0, 0x5, 0xf1, 0x2, 0x90, 0x4f, 0x50,
    0x5, 0x74, 0xf6, 0x0, 0x8, 0x7f, 0xa0, 0x0,
    0xb, 0xec, 0x68, 0x0, 0xd, 0x30, 0x2e, 0x0,
    0xf, 0x0, 0xe, 0x50, 0x3f, 0x0, 0x8, 0xb0,
    0x1b, 0x0, 0x0, 0x60, 0x0, 0x0, 0x0, 0x0,

    /* U+006C "l" */
    0x6a, 0xaa, 0x50, 0x8, 0xff, 0xdf, 0x10, 0x0,
    0x1, 0xe0, 0x0, 0x0, 0x2c, 0x0, 0x0, 0x3,
    0xa0, 0x0, 0x0, 0x58, 0x0, 0x0, 0x7, 0x60,
    0x0, 0x0, 0x94, 0x0, 0x0, 0xa, 0x20, 0x0,
    0x0, 0xc1, 0x0, 0x0, 0xd, 0x0, 0x0, 0x0,
    0xd0, 0x0, 0x0, 0x1c, 0x0, 0x0, 0x1, 0xc0,
    0x0, 0x0, 0xe, 0x10, 0x0, 0x0, 0x7c, 0x10,
    0x0, 0x0, 0x8e, 0x50, 0x0, 0x0, 0x0,

    /* U+006D "m" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x84, 0xad,
    0x11, 0x9d, 0x20, 0x1e, 0x60, 0x79, 0x80, 0x7b,
    0x2, 0xb0, 0x5, 0xd0, 0x3, 0xf0, 0x2b, 0x0,
    0x69, 0x0, 0x3f, 0x3, 0xc0, 0x7, 0x70, 0x3,
    0xf0, 0x5c, 0x0, 0x95, 0x0, 0x4f, 0x6, 0xb0,
    0xb, 0x30, 0x5, 0xe0, 0x89, 0x0, 0xc1, 0x0,
    0x6c, 0xa, 0x90, 0xe, 0x0, 0x7, 0xa0, 0xb8,
    0x0, 0xa0, 0x0, 0x65, 0x4, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+006E "n" */
    0x1, 0x0, 0x0, 0x0, 0xb, 0x6, 0xad, 0x70,
    0x2d, 0xa4, 0x0, 0xe4, 0x3d, 0x10, 0x0, 0x89,
    0x5b, 0x0, 0x0, 0x8b, 0x79, 0x0, 0x0, 0x9a,
    0x88, 0x0, 0x0, 0xb8, 0x96, 0x0, 0x0, 0xb7,
    0xa5, 0x0, 0x0, 0xd6, 0xc4, 0x0, 0x0, 0xe4,
    0x20, 0x0, 0x0, 0xc1,

    /* U+006F "o" */
    0x0, 0x9, 0xfe, 0xa2, 0x0, 0x1, 0xdb, 0x22,
    0xcf, 0x30, 0x9, 0xc0, 0x0, 0x1f, 0xd0, 0xf,
    0x60, 0x0, 0xc, 0xf1, 0x2f, 0x30, 0x0, 0xa,
    0xf1, 0x2f, 0x30, 0x0, 0xc, 0xe0, 0xe, 0x60,
    0x0, 0xe, 0xa0, 0x7, 0xe8, 0x56, 0xcd, 0x20,
    0x0, 0x5c, 0xdc, 0x80, 0x0,

    /* U+0070 "p" */
    0x0, 0x30, 0x5a, 0xc7, 0x0, 0x0, 0xf9, 0x40,
    0x1c, 0xb0, 0x2, 0xd0, 0x0, 0x5, 0xf3, 0x4,
    0xa0, 0x0, 0x7, 0xf3, 0x6, 0x90, 0x0, 0xb,
    0xf1, 0x8, 0x80, 0x0, 0x4f, 0xa0, 0xa, 0x90,
    0x4, 0xed, 0x10, 0xc, 0xfc, 0xdf, 0x91, 0x0,
    0xf, 0x65, 0x52, 0x0, 0x0, 0xf, 0x30, 0x0,
    0x0, 0x0, 0x1f, 0x30, 0x0, 0x0, 0x0, 0x2f,
    0x20, 0x0, 0x0, 0x0, 0x3f, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0xbf, 0xa1, 0x2,
    0xd4, 0x7, 0xa0, 0xb1, 0x0, 0xb, 0x47, 0x0,
    0x0, 0xb9, 0x30, 0x0, 0xa, 0xc2, 0x0, 0x2,
    0x8a, 0x90, 0x0, 0x45, 0x18, 0xc9, 0x68, 0x10,
    0x0, 0x0, 0x90, 0x0, 0x0, 0x8, 0x0, 0x0,
    0x0, 0x80, 0x0, 0x0, 0x8, 0x0, 0x0, 0x1,
    0x70, 0x0, 0x0, 0x11, 0x0,

    /* U+0072 "r" */
    0x6, 0x0, 0x0, 0x0, 0xb0, 0x4c, 0xf6, 0x1c,
    0x6b, 0x30, 0x2, 0xe9, 0x0, 0x0, 0x3c, 0x0,
    0x0, 0x5, 0xb0, 0x0, 0x0, 0x79, 0x0, 0x0,
    0x9, 0x70, 0x0, 0x0, 0xa5, 0x0, 0x0, 0x5,
    0x30, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x1, 0x67, 0x20, 0x0, 0x7d, 0x74, 0x50,
    0x6, 0xc0, 0x0, 0x0, 0xb, 0x50, 0x0, 0x0,
    0xa, 0x60, 0x0, 0x0, 0x1, 0xbf, 0xfe, 0x70,
    0x0, 0x0, 0x6, 0xf4, 0x0, 0x0, 0x3, 0xf6,
    0x0, 0x0, 0x7, 0xf4, 0x60, 0x0, 0x3e, 0xe0,
    0xdf, 0xdd, 0xff, 0x50, 0x2b, 0xfa, 0x71, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+0074 "t" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x19, 0x0, 0x0, 0x0, 0x0, 0x0, 0x38, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x69, 0x44, 0x44, 0x41,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x4, 0x44,
    0xd6, 0x33, 0x33, 0x10, 0x0, 0x0, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xe6, 0x0, 0x21, 0x0, 0x0, 0x0, 0x1a, 0xef,
    0xc2, 0x0,

    /* U+0075 "u" */
    0x0, 0x0, 0x0, 0x0, 0x7, 0x10, 0x0, 0x7,
    0xe, 0x10, 0x0, 0x7, 0x1f, 0x10, 0x0, 0x26,
    0x3f, 0x0, 0x0, 0x44, 0x5e, 0x0, 0x0, 0x73,
    0x6f, 0x0, 0x0, 0x92, 0x4f, 0x20, 0x39, 0xf1,
    0x6, 0xde, 0xa3, 0xd0, 0x0, 0x0, 0x0, 0x70,

    /* U+0076 "v" */
    0x0, 0x0, 0x0, 0x0, 0x6, 0x70, 0x0, 0x0,
    0x67, 0x5e, 0x0, 0x0, 0xe, 0x11, 0xf3, 0x0,
    0x7, 0xb0, 0xc, 0x70, 0x0, 0xe4, 0x0, 0x8b,
    0x0, 0x7c, 0x0, 0x5, 0xf0, 0x1e, 0x30, 0x0,
    0x3f, 0x38, 0xa0, 0x0, 0x1, 0xf8, 0xe1, 0x0,
    0x0, 0xe, 0xf5, 0x0, 0x0, 0x0, 0x9c, 0x0,
    0x0, 0x0, 0x0, 0x10, 0x0, 0x0,

    /* U+0077 "w" */
    0xd6, 0x0, 0x4d, 0x10, 0x9, 0x6c, 0xd0, 0xc,
    0xf5, 0x3, 0xf2, 0xae, 0x9, 0x6c, 0xa0, 0xca,
    0x8, 0xf7, 0xd0, 0x4f, 0x9f, 0x20, 0x6f, 0xf3,
    0x0, 0xaf, 0x70, 0x2, 0xf6, 0x0, 0x0, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0078 "x" */
    0x0, 0x0, 0x0, 0x0, 0x50, 0x3, 0x30, 0x0,
    0x6, 0x30, 0x5, 0xd1, 0x0, 0x35, 0x0, 0x0,
    0x99, 0x1, 0x80, 0x0, 0x0, 0x1f, 0x28, 0x0,
    0x0, 0x0, 0x8, 0xe2, 0x0, 0x0, 0x0, 0x5,
    0xf7, 0x0, 0x0, 0x0, 0x27, 0x4f, 0x40, 0x0,
    0x0, 0x90, 0x9, 0xf2, 0x0, 0x8, 0x10, 0x0,
    0xed, 0x0, 0x43, 0x0, 0x0, 0x3e, 0x50, 0x10,
    0x0, 0x0, 0x0, 0x0,

    /* U+0079 "y" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0x20, 0x0, 0x16,
    0x0, 0x0, 0xd4, 0x0, 0x0, 0x97, 0x0, 0xab,
    0x0, 0x0, 0x1, 0xe3, 0x5e, 0x10, 0x0, 0x0,
    0x4, 0xbe, 0x40, 0x0, 0x0, 0x0, 0xb, 0x90,
    0x0, 0x0, 0x0, 0x6, 0xd0, 0x0, 0x0, 0x0,
    0x3, 0xf3, 0x0, 0x0, 0x0, 0x1, 0xc8, 0x0,
    0x0, 0x0, 0x1, 0xcd, 0x0, 0x0, 0x0, 0x38,
    0xdf, 0x30, 0x0, 0x0, 0x7, 0xeb, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+007A "z" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xff, 0xf9, 0x4, 0xba, 0x76, 0x68, 0xe2, 0x0,
    0x0, 0x0, 0x2c, 0x20, 0x0, 0x0, 0x2, 0xb1,
    0x0, 0x0, 0x0, 0x1b, 0x10, 0x0, 0x0, 0x1,
    0xb1, 0x0, 0x0, 0x0, 0xb, 0x20, 0x0, 0x0,
    0x0, 0xb3, 0x0, 0x0, 0x0, 0xb, 0x40, 0x12,
    0x22, 0x10, 0x3f, 0xdf, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x6a, 0xc4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x38, 0xcf, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xae,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x7b, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x49, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x94, 0x3f, 0xfe, 0x0, 0x0, 0x0,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xb7, 0x20, 0x0,
    0x3f, 0xfe, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xe9, 0x50, 0x0, 0x0, 0x0, 0x3f, 0xfe, 0x0,
    0x0, 0x0, 0xe, 0xff, 0x93, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0xe,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xfe, 0x0, 0x0, 0x0, 0xe, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0,
    0x0, 0xe, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0xe, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfe,
    0x0, 0x0, 0x0, 0xe, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x23, 0x4f, 0xfe, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x30, 0x0, 0x0, 0x1, 0x9f, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0xe, 0xff, 0x30,
    0x0, 0x0, 0x1e, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x1, 0xe, 0xff, 0x30, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x5c, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff,
    0xfc, 0x9, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xf4, 0x1f, 0xff,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x5c,
    0xef, 0xd9, 0x20, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5c, 0xff,
    0xfc, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0x76, 0x0, 0x2c, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0x40, 0x6, 0x7f, 0xb3, 0x37, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x33,
    0xbf, 0xff, 0xff, 0xff, 0xfb, 0x99, 0x99, 0x99,
    0x99, 0x9a, 0xff, 0xff, 0xff, 0xff, 0xc6, 0x69,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xf, 0xfb,
    0x66, 0xcf, 0xf9, 0x0, 0x3f, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0x60, 0x9, 0xff, 0x90,
    0x3, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xf6, 0x0, 0x9f, 0xfd, 0x88, 0xbf, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xc8, 0x8d, 0xff,
    0xff, 0xff, 0xff, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xfa, 0x11, 0x5f, 0xfa,
    0x88, 0x88, 0x88, 0x88, 0x89, 0xff, 0x81, 0x1a,
    0xff, 0x90, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0x9f, 0xf9, 0x0, 0x4f,
    0xfe, 0xcc, 0xcc, 0xcc, 0xcc, 0xcd, 0xff, 0x60,
    0x9, 0xff, 0xfd, 0xde, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xdd, 0xff, 0xff, 0xcc,
    0xef, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xec, 0xcf, 0xff, 0x90, 0x4, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf6, 0x0, 0x9f, 0xf9,
    0x0, 0x3f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x60, 0x9, 0xff, 0xb2, 0x26, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf8, 0x22, 0xbf,
    0xff, 0xff, 0xff, 0xf8, 0x55, 0x55, 0x55, 0x55,
    0x56, 0xff, 0xff, 0xff, 0xff, 0xd7, 0x7a, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x77,
    0xdf, 0xa8, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x8, 0xa0,

    /* U+F00B "" */
    0x14, 0x44, 0x44, 0x20, 0x2, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x1e, 0xff, 0xff, 0xff,
    0x21, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xf4, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x42, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x42, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xaf, 0xff, 0xff, 0xd1, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x14, 0x44, 0x44, 0x20,
    0x2, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x1e, 0xff, 0xff, 0xff, 0x21, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xf4, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x42, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x42, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xaf,
    0xff, 0xff, 0xd1, 0xc, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9e, 0xee, 0xee, 0xc1, 0xb, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0x9f, 0xff, 0xff, 0xff,
    0x42, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x42, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0x21, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x25, 0x55, 0x55, 0x30, 0x2,
    0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x20,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x44, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x6f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x6f, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0x50, 0x0,
    0xf, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xb, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xf7, 0x0, 0xb, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xf7, 0xb, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8e, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x7, 0xea, 0x0, 0x0, 0x0, 0x0, 0x8, 0xe9,
    0x0, 0x7f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xa0, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xf2, 0xbf, 0xff, 0xff, 0xb0, 0x0,
    0x8f, 0xff, 0xff, 0xd0, 0xc, 0xff, 0xff, 0xfb,
    0x8, 0xff, 0xff, 0xfd, 0x10, 0x0, 0xcf, 0xff,
    0xff, 0xdf, 0xff, 0xff, 0xd1, 0x0, 0x0, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x8, 0xff, 0xff, 0xfd,
    0x2c, 0xff, 0xff, 0xfb, 0x0, 0x8f, 0xff, 0xff,
    0xd1, 0x0, 0xcf, 0xff, 0xff, 0xb0, 0xff, 0xff,
    0xfd, 0x10, 0x0, 0xc, 0xff, 0xff, 0xf2, 0xaf,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xd0,
    0xb, 0xfd, 0x10, 0x0, 0x0, 0x0, 0xb, 0xfd,
    0x10, 0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x20, 0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5d, 0xeb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0x40, 0x0, 0xbf, 0xff,
    0x20, 0x1, 0x50, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0x40, 0xb, 0xff, 0xf2, 0x0, 0xdf, 0xb0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xfd, 0x0, 0xbf,
    0xff, 0x20, 0x6f, 0xff, 0xd1, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xd0, 0xb, 0xff, 0xf2, 0x7, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0xef, 0xff, 0xe2, 0x0,
    0xbf, 0xff, 0x20, 0xa, 0xff, 0xff, 0x50, 0x0,
    0x7f, 0xff, 0xf2, 0x0, 0xb, 0xff, 0xf2, 0x0,
    0xa, 0xff, 0xfe, 0x0, 0xe, 0xff, 0xf6, 0x0,
    0x0, 0xbf, 0xff, 0x20, 0x0, 0x1e, 0xff, 0xf5,
    0x3, 0xff, 0xfe, 0x0, 0x0, 0xb, 0xff, 0xf2,
    0x0, 0x0, 0x7f, 0xff, 0xa0, 0x7f, 0xff, 0x80,
    0x0, 0x0, 0xbf, 0xff, 0x20, 0x0, 0x1, 0xff,
    0xfe, 0x9, 0xff, 0xf5, 0x0, 0x0, 0xb, 0xff,
    0xf2, 0x0, 0x0, 0xe, 0xff, 0xf0, 0xaf, 0xff,
    0x40, 0x0, 0x0, 0xbf, 0xff, 0x20, 0x0, 0x0,
    0xdf, 0xff, 0x19, 0xff, 0xf4, 0x0, 0x0, 0x8,
    0xff, 0xe1, 0x0, 0x0, 0xe, 0xff, 0xf0, 0x8f,
    0xff, 0x70, 0x0, 0x0, 0x2, 0x31, 0x0, 0x0,
    0x0, 0xff, 0xff, 0x5, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xc0,
    0x1f, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xf7, 0x0, 0xbf, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0x20, 0x3, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0x90, 0x0, 0x9, 0xff,
    0xff, 0xb2, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xe1, 0x0, 0x0, 0xc, 0xff, 0xff, 0xfa, 0x53,
    0x24, 0x8d, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x26, 0xab, 0xba, 0x84, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x46, 0x76, 0x41,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0x81, 0x4, 0xcf, 0xff,
    0xff, 0xff, 0xc4, 0x1, 0x96, 0x0, 0x0, 0x3,
    0xff, 0xea, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xf4, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0xbd, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0xdf, 0xff, 0xff, 0xff, 0xe4, 0x0, 0x4,
    0xef, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x7f, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff,
    0x70, 0x0, 0x0, 0xcf, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xc0, 0x0, 0x0, 0xd,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x3e, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xfe, 0x30, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xc1, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xff, 0x90,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x79, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x90, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x6, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xf7, 0x0, 0x0, 0x9, 0xd4, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x4, 0xda, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xcf, 0xff, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x8a, 0xba, 0x83, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x80, 0x0,
    0xf, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xb1, 0x1, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2d, 0xff, 0xff, 0xff, 0xd2, 0x1f, 0xff, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4e, 0xff,
    0xfb, 0xaf, 0xff, 0xf6, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf8, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xf5, 0x4, 0x50, 0x4e,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xbf, 0xff, 0xe3, 0x7, 0xff, 0x90, 0x2d, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xff,
    0xc1, 0xa, 0xff, 0xff, 0xb1, 0x1b, 0xff, 0xff,
    0x30, 0x0, 0x0, 0x4, 0xff, 0xff, 0xa0, 0x1c,
    0xff, 0xff, 0xff, 0xd2, 0x9, 0xff, 0xff, 0x50,
    0x0, 0x7, 0xff, 0xff, 0x70, 0x3e, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x6, 0xff, 0xff, 0x90, 0x9,
    0xff, 0xff, 0x50, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x3, 0xef, 0xff, 0xb0, 0xcf, 0xfe,
    0x30, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x2, 0xdf, 0xfd, 0x1, 0xec, 0x10, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x10, 0xbf, 0x20, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x10, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0xff, 0x98, 0x88,
    0xef, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x9, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x9, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x1b, 0xcc, 0xcc, 0xc6, 0x0, 0x0, 0x4c,
    0xcc, 0xcc, 0xc3, 0x0, 0x0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0xbb, 0xbb, 0xb2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x55, 0x55, 0xcf, 0xff, 0xff,
    0xc5, 0x55, 0x52, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x88, 0x88, 0x88,
    0x85, 0x5, 0xff, 0xf5, 0x5, 0x88, 0x88, 0x88,
    0x84, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x5, 0xe5,
    0x6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x9d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x3f, 0x42, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x77, 0xf8, 0x6f, 0xff, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x10,

    /* U+F01C "" */
    0x0, 0x0, 0x0, 0xae, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xb2,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x29, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xc0, 0x0,
    0x0, 0x5, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x70, 0x0, 0x1,
    0xef, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0x20, 0x0, 0xaf, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xef, 0xfc, 0x0, 0x5f, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xf7, 0xd, 0xff, 0xf6, 0x66, 0x66, 0x50,
    0x0, 0x0, 0x0, 0x4, 0x66, 0x66, 0x6e, 0xff,
    0xf0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x44, 0x44, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x2f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x2f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1c, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x2c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd3, 0x0,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x46, 0x76, 0x30,
    0x0, 0x0, 0x4, 0xde, 0xc0, 0x0, 0x0, 0x0,
    0x5c, 0xff, 0xff, 0xff, 0xfa, 0x30, 0x0, 0x6f,
    0xff, 0x0, 0x0, 0x3, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb2, 0x6, 0xff, 0xf0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x5f, 0xff, 0x0, 0x6, 0xff, 0xff, 0xfa, 0x51,
    0x2, 0x5b, 0xff, 0xff, 0xfb, 0xff, 0xf0, 0x4,
    0xff, 0xff, 0xc2, 0x0, 0x0, 0x0, 0x3, 0xcf,
    0xff, 0xff, 0xff, 0x0, 0xef, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xf0,
    0x6f, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x4, 0x54,
    0x43, 0xcf, 0xff, 0xff, 0xd, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x5f, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xe0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x11, 0x11, 0x11, 0x11, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xff, 0x4f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xf2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x1, 0xef, 0xfd, 0xf, 0xff, 0xff, 0xc5,
    0x56, 0x76, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0x70, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xe0, 0xf, 0xff, 0xff,
    0xff, 0x91, 0x0, 0x0, 0x0, 0x1, 0xaf, 0xff,
    0xf5, 0x0, 0xff, 0xfd, 0xff, 0xff, 0xe8, 0x20,
    0x0, 0x27, 0xef, 0xff, 0xf8, 0x0, 0xf, 0xff,
    0x59, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0xff, 0xf5, 0x4, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0xf,
    0xff, 0x60, 0x0, 0x6d, 0xff, 0xff, 0xff, 0xff,
    0x81, 0x0, 0x0, 0x0, 0xef, 0xf5, 0x0, 0x0,
    0x2, 0x69, 0xa9, 0x74, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5a, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0x80, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xf8, 0x0, 0x11, 0x11, 0x6f, 0xff, 0xff,
    0x8b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x8e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x14, 0x55, 0x55, 0xaf, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9e, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x59, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0x80, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x11,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x1f, 0xf4, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x1, 0xef, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x1, 0xef, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x9, 0xfb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0xcf,
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xbf, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x2f, 0xf9, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x66, 0x0, 0x25, 0x55, 0x55,
    0xbf, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xa1, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0x70, 0x0, 0x0, 0x20, 0x0, 0xaf, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xf8, 0x0,
    0x0, 0x5f, 0xc1, 0x0, 0xcf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0x80, 0x0, 0x5,
    0xff, 0xe1, 0x2, 0xff, 0xa0, 0x0, 0x1, 0x11,
    0x16, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x6, 0xff,
    0xc0, 0x7, 0xff, 0x20, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x2, 0x20, 0x5, 0xff, 0x60,
    0x1f, 0xf7, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x1, 0xff, 0x50, 0xb, 0xfd, 0x0, 0xaf,
    0xc0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x1e, 0xff, 0x20, 0x4f, 0xf2, 0x7, 0xff, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x1e,
    0xf9, 0x0, 0xff, 0x50, 0x4f, 0xf0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x9f, 0xb0,
    0xe, 0xf6, 0x3, 0xff, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0xc, 0xfa, 0x0, 0xff,
    0x60, 0x4f, 0xf1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xb, 0xff, 0x40, 0x3f, 0xf3, 0x6,
    0xff, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x2, 0xff, 0x90, 0x9, 0xff, 0x0, 0xaf, 0xc0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x5,
    0x50, 0x3, 0xff, 0x80, 0xe, 0xf8, 0x1, 0x45,
    0x55, 0x5a, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x2,
    0xef, 0xe1, 0x5, 0xff, 0x30, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x80, 0x0, 0x3, 0xff, 0xf3,
    0x0, 0xdf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf8, 0x0, 0x0, 0x7f, 0xe3, 0x0, 0x9f,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0x70, 0x0, 0x0, 0x61, 0x0, 0x6f, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xe3, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xfb, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x14, 0x0,
    0x0, 0x0, 0x0,

    /* U+F03E "" */
    0x8, 0xbc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xb8, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd3,
    0x3, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x2, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0x62, 0xef, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x8f, 0xff, 0xff, 0xff, 0x60,
    0x2, 0xef, 0xff, 0xff, 0xff, 0xff, 0xc9, 0xcf,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x2, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff,
    0x32, 0xef, 0xff, 0x60, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xff, 0xff, 0x30, 0x2, 0xef, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff, 0xff,
    0x30, 0x0, 0x2, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0x32, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x2c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x20,

    /* U+F048 "" */
    0x1, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x3e, 0xf4,
    0xef, 0xf9, 0x0, 0x0, 0x0, 0x3, 0xff, 0xfa,
    0xef, 0xf9, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xfb,
    0xef, 0xf9, 0x0, 0x0, 0x5, 0xff, 0xff, 0xfb,
    0xef, 0xf9, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xfb,
    0xef, 0xf9, 0x0, 0x8, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xf9, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xf9, 0xa, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xfa, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xf9, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xf9, 0x6, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xf9, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xf9, 0x0, 0x4, 0xff, 0xff, 0xff, 0xfb,
    0xef, 0xf9, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xfb,
    0xef, 0xf9, 0x0, 0x0, 0x2, 0xef, 0xff, 0xfb,
    0xef, 0xf9, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xfb,
    0xef, 0xf9, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xf9,
    0xac, 0xc6, 0x0, 0x0, 0x0, 0x0, 0x9, 0xb2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2b, 0xd8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xe5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xc2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x30, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x10, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd4, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa1,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x70, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe5, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x40, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xd3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xe6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xf9, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4e, 0xfc, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F04C "" */
    0x0, 0x22, 0x22, 0x20, 0x0, 0x0, 0x0, 0x12,
    0x22, 0x21, 0x0, 0x3e, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xe3, 0xdf, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x7f, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xfe, 0xaf,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x2f, 0xff, 0xff,
    0xff, 0xf9, 0x8, 0xbb, 0xbb, 0xba, 0x30, 0x0,
    0x3, 0xab, 0xbb, 0xbb, 0x70,

    /* U+F04D "" */
    0x0, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x0, 0x3e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd2, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x19, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0x80,

    /* U+F051 "" */
    0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x11,
    0x0, 0x9f, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xf8, 0xf, 0xff, 0xc1, 0x0, 0x0, 0x0, 0xe,
    0xff, 0x90, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0xef, 0xf9, 0xf, 0xff, 0xff, 0xe2, 0x0, 0x0,
    0xe, 0xff, 0x90, 0xff, 0xff, 0xff, 0xe3, 0x0,
    0x0, 0xef, 0xf9, 0xf, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0xe, 0xff, 0x90, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0xef, 0xf9, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0xe, 0xff, 0x90, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xef, 0xf9, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x3e, 0xff, 0x90,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x20, 0xef, 0xf9,
    0xf, 0xff, 0xff, 0xff, 0xfd, 0x10, 0xe, 0xff,
    0x90, 0xff, 0xff, 0xff, 0xfc, 0x10, 0x0, 0xef,
    0xf9, 0xf, 0xff, 0xff, 0xfb, 0x0, 0x0, 0xe,
    0xff, 0x90, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0xef, 0xf9, 0xf, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x90, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf9, 0x5, 0xc6, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xcc, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe1, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x1d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x2, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xde, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xed, 0x40, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x55,
    0x55, 0x55, 0x54, 0x0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xdd, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xff, 0xe1, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x1d,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x1, 0xdf, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x1, 0xdf, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0x80, 0x0, 0x0, 0x2,
    0xef, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x1e, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x12,
    0x0,

    /* U+F054 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x1, 0xdf, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xfc, 0x10, 0x0, 0x0, 0xa, 0xff, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xfc, 0x10, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xfc, 0x10, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xfc,
    0x10, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xfc, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x22, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x28, 0x88, 0x88, 0x88, 0xbf,
    0xff, 0xfa, 0x88, 0x88, 0x88, 0x71, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x1, 0x22, 0x22, 0x22, 0x7f, 0xff,
    0xf6, 0x22, 0x22, 0x22, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xbb,
    0x70, 0x0, 0x0, 0x0, 0x0,

    /* U+F068 "" */
    0x4a, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xa2, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe5,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0x69, 0xbd, 0xdc,
    0xa6, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5c, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xcf, 0xff, 0xff, 0xa8, 0x8a, 0xef, 0xff, 0xfd,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x6e, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xf4, 0x0, 0x0, 0xef,
    0xd5, 0x0, 0x3f, 0xff, 0xff, 0xa0, 0x0, 0x5,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0xb, 0xff, 0xf8,
    0x0, 0x9f, 0xff, 0xff, 0x70, 0x1, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x0, 0xef, 0xff, 0xf3, 0x3,
    0xff, 0xff, 0xff, 0x20, 0xaf, 0xff, 0xff, 0xf2,
    0x3, 0x34, 0xcf, 0xff, 0xff, 0x90, 0xf, 0xff,
    0xff, 0xfc, 0xf, 0xff, 0xff, 0xff, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0xef, 0xff, 0xff,
    0xf1, 0xbf, 0xff, 0xff, 0xf1, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0xf, 0xff, 0xff, 0xfd, 0x2,
    0xff, 0xff, 0xff, 0x40, 0x3f, 0xff, 0xff, 0xff,
    0xf5, 0x3, 0xff, 0xff, 0xff, 0x40, 0x7, 0xff,
    0xff, 0xfa, 0x0, 0x9f, 0xff, 0xff, 0xfb, 0x0,
    0x8f, 0xff, 0xff, 0x90, 0x0, 0x9, 0xff, 0xff,
    0xf3, 0x0, 0x7f, 0xff, 0xf9, 0x0, 0x2f, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xe2,
    0x0, 0x4, 0x41, 0x0, 0x1d, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xe5, 0x0,
    0x0, 0x0, 0x4d, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xef, 0xff, 0xfd, 0x86, 0x68,
    0xcf, 0xff, 0xfe, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe8, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x8c, 0xef, 0xfe, 0xc9, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F070 "" */
    0x2, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0x70, 0x0, 0x2,
    0x6a, 0xcd, 0xcb, 0x85, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xa2, 0x8e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xef, 0xff, 0xff,
    0xff, 0xfe, 0xa8, 0x9c, 0xff, 0xff, 0xf9, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xbf, 0xff,
    0xff, 0xd4, 0x0, 0x0, 0x1, 0xaf, 0xff, 0xfe,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4e, 0xff, 0xfb, 0x16, 0xff, 0xa1, 0x0, 0x9f,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x8, 0x10,
    0x0, 0x2c, 0xff, 0xfd, 0x7f, 0xff, 0xe1, 0x0,
    0xef, 0xff, 0xff, 0x20, 0x0, 0x0, 0x8, 0xfe,
    0x30, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x9, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x2, 0xff,
    0xff, 0x60, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0x10, 0x5f, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xa0, 0x0, 0x3, 0xdf, 0xff, 0xff,
    0xf3, 0x4, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x4,
    0xff, 0xff, 0xff, 0x90, 0x0, 0x1, 0xbf, 0xff,
    0xff, 0x10, 0x6f, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xf8, 0x9, 0xff, 0xff, 0xfd, 0x0, 0x0,
    0x0, 0x1e, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x4e, 0xff, 0xfc, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xa1,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1a, 0xff, 0xff,
    0xfa, 0x75, 0x61, 0x0, 0x2, 0xdf, 0xff, 0xe3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xbf,
    0xff, 0xff, 0xff, 0xd3, 0x0, 0x0, 0xaf, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x27, 0xad, 0xef, 0xfd, 0xa0, 0x0, 0x0, 0x6f,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1b, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xde,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xff, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xfb, 0xaa,
    0xaf, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xf0, 0x0, 0xc,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xf0, 0x0, 0xd, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xff, 0xf0, 0x0, 0xe, 0xff, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xf1, 0x0, 0xf, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0xf, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xf3, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x2f, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0xfd, 0xbb,
    0xcf, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x54, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x1e, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf1, 0x0, 0xe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x32, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf1, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x4, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x1, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x10, 0x0,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xfe, 0x20, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xfe, 0x20, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0xff, 0xfe, 0x2f, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x1c, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcc,
    0xdd, 0xde, 0xff, 0xff, 0x80, 0xc, 0xff, 0xff,
    0xee, 0xff, 0xff, 0xd1, 0x0, 0x0, 0xa, 0xff,
    0xf3, 0xb, 0xff, 0xff, 0x90, 0x4f, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0xb, 0xf4, 0xb, 0xff, 0xff,
    0x90, 0x3, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xa, 0xff, 0xff, 0xa0, 0x0, 0x1d, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xb0, 0x10, 0x0, 0xa,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xb0, 0x3f, 0x60, 0x3, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xc0, 0x3f, 0xff, 0x60,
    0x4f, 0xff, 0xb0, 0x9, 0xbb, 0xbb, 0xff, 0xff,
    0xc0, 0xb, 0xff, 0xff, 0xbc, 0xff, 0xff, 0xb0,
    0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x1d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xaf, 0xff, 0xff, 0xff,
    0xd1, 0x0, 0x0, 0x1e, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xff, 0xff, 0xff, 0x42, 0x33, 0x33,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x13, 0x36, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x20, 0x0, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0xb9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xdf, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0x9c, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xf9, 0x0,
    0xcf, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x1d, 0xff,
    0xff, 0x90, 0x0, 0xc, 0xff, 0xff, 0xa0, 0x0,
    0x1, 0xdf, 0xff, 0xf9, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xfa, 0x0, 0x1d, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xa0, 0xaf, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xf6, 0x7f, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xf4, 0x9, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F078 "" */
    0x5, 0xc5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8c, 0x20, 0x5f, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xe2, 0xbf, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xf7, 0x3f, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xd1, 0x3, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xfd, 0x10, 0x0,
    0x3f, 0xff, 0xff, 0x50, 0x0, 0x9, 0xff, 0xff,
    0xd1, 0x0, 0x0, 0x3, 0xff, 0xff, 0xf5, 0x0,
    0x9f, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x3f,
    0xff, 0xff, 0x59, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x7, 0xd4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0x40, 0x0, 0x1, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xf4, 0x0, 0x2f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0x50, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x5, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x8f, 0xff, 0xdf, 0xff, 0xef, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xe0, 0x0, 0x0,
    0xaf, 0xfb, 0x3f, 0xfe, 0x2e, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xe0, 0x0, 0x0,
    0xa, 0xa0, 0x3f, 0xfe, 0x2, 0xb7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x99, 0x3, 0xff, 0xe0, 0x2b, 0x60,
    0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xa4, 0xff, 0xe2, 0xef, 0xf4,
    0x0, 0x0, 0x3f, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xfd, 0xff, 0xfe, 0xff, 0xf3,
    0x0, 0x0, 0x3f, 0xff, 0xee, 0xee, 0xee, 0xee,
    0xec, 0x20, 0xaf, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd0, 0xa, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd0, 0x0, 0x9f, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x1, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x0, 0x0, 0x9, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8d, 0x40, 0x0, 0x0,

    /* U+F07B "" */
    0x8, 0xbc, 0xcc, 0xcc, 0xcc, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xaa, 0xaa, 0xaa, 0xaa,
    0xa9, 0x60, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x2c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x20,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xa1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x8, 0xaa, 0xaa, 0xff, 0xff, 0xff,
    0xca, 0xaa, 0xa3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x4, 0x88, 0x88, 0x88,
    0x60, 0xdf, 0xff, 0xff, 0x60, 0x68, 0x88, 0x88,
    0x84, 0xff, 0xff, 0xff, 0xff, 0x7, 0xef, 0xff,
    0xd2, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x99,
    0x99, 0x9d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x3f, 0x42, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x77, 0xf8, 0x6f, 0xff, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc0, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x10,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xb7,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xc6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x39, 0x40, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x6d,
    0xff, 0xf2, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x1, 0x8e, 0xff, 0xff, 0xfd, 0x10,
    0x2b, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xc9, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xff, 0xfa, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xec, 0x85, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x1, 0x32, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xfe, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x47, 0x61, 0x0, 0x1e,
    0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x1, 0xbf,
    0xff, 0xf6, 0x9, 0xff, 0xfe, 0xff, 0xfe, 0x0,
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xa0, 0xef, 0xf7,
    0x2, 0xff, 0xf4, 0x0, 0x1, 0xdf, 0xff, 0xff,
    0xb0, 0xf, 0xff, 0x20, 0xd, 0xff, 0x50, 0x1,
    0xdf, 0xff, 0xff, 0xb0, 0x0, 0xdf, 0xfa, 0x15,
    0xff, 0xf2, 0x1, 0xdf, 0xff, 0xff, 0xb0, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0x71, 0xdf, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xef, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x8, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x4, 0xbd,
    0xef, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff,
    0xff, 0x93, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0xcf, 0xfd, 0x59, 0xff, 0xf2, 0x3, 0xff, 0xff,
    0xff, 0x40, 0x0, 0xf, 0xff, 0x30, 0xd, 0xff,
    0x50, 0x4, 0xff, 0xff, 0xff, 0x40, 0x0, 0xff,
    0xf4, 0x0, 0xef, 0xf4, 0x0, 0x4, 0xff, 0xff,
    0xff, 0x40, 0xc, 0xff, 0xd6, 0xaf, 0xff, 0x10,
    0x0, 0x5, 0xff, 0xff, 0xff, 0x40, 0x4f, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x5, 0xff, 0xff,
    0xfb, 0x0, 0x7f, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x4, 0xcf, 0xe9, 0x10, 0x0, 0x39, 0xcb,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xf6,
    0xb, 0x70, 0x0, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xf7, 0xc, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xff, 0xff, 0xff, 0xf7, 0xc, 0xff,
    0x70, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xf7, 0xc, 0xff, 0xf7, 0x14, 0x44, 0x20, 0xef,
    0xff, 0xff, 0xff, 0xf7, 0x8, 0xbb, 0xba, 0xdf,
    0xff, 0xb0, 0xef, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xb0, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xed, 0xdd, 0xdc, 0xff, 0xff, 0xb0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xb0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xb0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xb0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xb0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xb0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xb0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xb0, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xb0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xb0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xb0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xb0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xb0, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xf1, 0x3,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x30, 0xff,
    0xff, 0xfc, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x1, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x10, 0x0, 0x0, 0x0,

    /* U+F0C7 "" */
    0x0, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x0, 0x0, 0x0, 0x4e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd2, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x20,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xe2, 0x0, 0xff, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xfe, 0x20, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xe2, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xfb, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff, 0xfe,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xfe, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x44, 0xaf, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x1, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x67, 0xcf,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x19, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0x80,

    /* U+F0E7 "" */
    0x0, 0x2e, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xf2, 0x22, 0x22, 0x10, 0x6, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf7, 0xb, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x11, 0x11, 0x13, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xec,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x0, 0x9d, 0xc6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x14, 0x55, 0x5b, 0xff, 0xff,
    0x85, 0x55, 0x40, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xd2, 0x4f, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xc0, 0x1f, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xfe,
    0xef, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xfa, 0x42, 0x22,
    0x22, 0x21, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0x50, 0xcf, 0xff, 0xff, 0xfb,
    0xc, 0x80, 0x0, 0xff, 0xff, 0xff, 0x41, 0xff,
    0xff, 0xff, 0xfb, 0xc, 0xf8, 0x0, 0xff, 0xff,
    0xff, 0x41, 0xff, 0xff, 0xff, 0xfb, 0xc, 0xff,
    0x80, 0xff, 0xff, 0xff, 0x41, 0xff, 0xff, 0xff,
    0xfb, 0xc, 0xff, 0xf8, 0xff, 0xff, 0xff, 0x41,
    0xff, 0xff, 0xff, 0xfb, 0x7, 0xaa, 0xa9, 0xff,
    0xff, 0xff, 0x41, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0x41, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xee, 0xed, 0xff, 0xff, 0xff,
    0x41, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0x41, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0x41, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0x41, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xef, 0xff, 0xff, 0x41, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x25, 0x55, 0x55, 0x11,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0xdc, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1b, 0xff, 0xa1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2b, 0xff, 0xff, 0xff,
    0xa1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0xd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x5f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x5, 0x55, 0x55, 0x55,
    0x55, 0x55, 0x55, 0x55, 0x55, 0x55, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x2b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc3, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x1f, 0xff, 0x52, 0x2c, 0xf3,
    0x24, 0xfa, 0x22, 0xaf, 0x52, 0x2c, 0xf3, 0x24,
    0xff, 0xf2, 0xff, 0xf2, 0x0, 0xae, 0x0, 0x1f,
    0x80, 0x7, 0xf1, 0x0, 0xae, 0x0, 0xf, 0xff,
    0x2f, 0xff, 0x20, 0xa, 0xe0, 0x1, 0xf8, 0x0,
    0x7f, 0x10, 0xa, 0xe0, 0x0, 0xff, 0xf2, 0xff,
    0xfa, 0x88, 0xef, 0x98, 0xaf, 0xd8, 0x8d, 0xfa,
    0x88, 0xef, 0x98, 0x9f, 0xff, 0x2f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xf9, 0x0,
    0x7f, 0x40, 0x1d, 0xd1, 0x3, 0xfa, 0x0, 0x7f,
    0xff, 0xff, 0x2f, 0xff, 0xff, 0x70, 0x5, 0xf3,
    0x0, 0xcc, 0x0, 0x1f, 0x80, 0x5, 0xff, 0xff,
    0xf2, 0xff, 0xff, 0xf8, 0x0, 0x6f, 0x30, 0xc,
    0xd0, 0x2, 0xf9, 0x0, 0x6f, 0xff, 0xff, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0xff, 0xfb,
    0x99, 0xef, 0xa9, 0x99, 0x99, 0x99, 0x99, 0x99,
    0xef, 0xa9, 0xbf, 0xff, 0x2f, 0xff, 0x20, 0xa,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xe0,
    0x0, 0xff, 0xf2, 0xff, 0xf2, 0x0, 0xae, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xae, 0x0, 0xf,
    0xff, 0x2f, 0xff, 0x52, 0x2c, 0xf2, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x2c, 0xf2, 0x23, 0xff, 0xf2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1c, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x2c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd3, 0x0,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x29, 0xd9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0xaf, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4b, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xdf, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x18, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x3b,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x5, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0,
    0x5d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x23, 0x33, 0x33, 0x33,
    0x3f, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xef, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F15B "" */
    0xaf, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x5c, 0x10,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5,
    0xfd, 0x10, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x5f, 0xfd, 0x10, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x5, 0xff, 0xfd, 0x10, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x5f, 0xff, 0xfd, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x5, 0xff, 0xff,
    0xfb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x14,
    0x44, 0x44, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x51, 0x11, 0x11, 0x10, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x12, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x10,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x56, 0x78,
    0x87, 0x53, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x6b, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x72, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb4, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc2, 0x0, 0x0,
    0x0, 0x5e, 0xff, 0xff, 0xff, 0xfd, 0xa7, 0x66,
    0x67, 0x9c, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xb5, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xaf, 0xff, 0xff, 0xfb, 0x10,
    0xaf, 0xff, 0xff, 0xb2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x9f, 0xff, 0xff, 0xd1,
    0xbf, 0xff, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xff, 0xe1,
    0xc, 0xfc, 0x10, 0x0, 0x0, 0x1, 0x68, 0x9b,
    0xa8, 0x62, 0x0, 0x0, 0x0, 0x9, 0xfe, 0x20,
    0x0, 0x60, 0x0, 0x0, 0x6, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xe8, 0x10, 0x0, 0x0, 0x52, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xfe, 0xa6, 0x43,
    0x45, 0x9d, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xff, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x5d, 0xff, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xc2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9e, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x9c,
    0xb3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x25,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x0, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0, 0x0,
    0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xe0,
    0xff, 0xf2, 0x14, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x40, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x1, 0xef, 0xf3,
    0xff, 0xf2, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xef, 0xf3,
    0xff, 0xf2, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xef, 0xf3,
    0xff, 0xf2, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xe1,
    0xff, 0xfe, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xff, 0xfa, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x32, 0x0, 0x0,

    /* U+F241 "" */
    0x0, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0, 0x0,
    0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xe0,
    0xff, 0xf2, 0x4, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x20, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x1, 0xef, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xe1,
    0xff, 0xfe, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xff, 0xfa, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x32, 0x0, 0x0,

    /* U+F242 "" */
    0x0, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0, 0x0,
    0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xe0,
    0xff, 0xf2, 0x4, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xe1,
    0xff, 0xfe, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xff, 0xfa, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x32, 0x0, 0x0,

    /* U+F243 "" */
    0x0, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0, 0x0,
    0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xe0,
    0xff, 0xf2, 0x14, 0x44, 0x44, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x5f, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x5f, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xf3,
    0xff, 0xf2, 0x5f, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0xff, 0xf2, 0x5f, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0xff, 0xf2, 0x5f, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x4f, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xe1,
    0xff, 0xfe, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xff, 0xfa, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x32, 0x0, 0x0,

    /* U+F244 "" */
    0x0, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x21, 0x0, 0x0,
    0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xe0,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf3,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xe1,
    0xff, 0xfe, 0xee, 0xee, 0xee, 0xee, 0xee, 0xee,
    0xee, 0xee, 0xee, 0xee, 0xee, 0xff, 0xfa, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x33,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x32, 0x0, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3c, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x33,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xfe, 0x99,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf2, 0x0,
    0x8f, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x90, 0x0,
    0x2, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x8a, 0x71, 0x0, 0x0, 0xcf, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x2e, 0xff, 0xfe, 0x20, 0x4, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xec, 0x20, 0x0,
    0xbf, 0xff, 0xff, 0xa1, 0x2d, 0xf4, 0x11, 0x11,
    0x11, 0x11, 0x11, 0x11, 0x11, 0xef, 0xf8, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe2,
    0xef, 0xff, 0xff, 0xea, 0xaa, 0xaa, 0xef, 0xda,
    0xaa, 0xaa, 0xaa, 0xaa, 0xaa, 0xff, 0xff, 0x80,
    0x8f, 0xff, 0xff, 0x70, 0x0, 0x0, 0x2f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xb2, 0x0,
    0x9, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x8, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa5, 0x0, 0x0,
    0x0, 0x13, 0x10, 0x0, 0x0, 0x0, 0x1, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x60, 0xd, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1e,
    0xf6, 0x4e, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x16, 0x7e, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x33, 0x33, 0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x0, 0x69, 0xcd, 0xdc, 0x95, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7e, 0xff, 0xff, 0xff,
    0xff, 0xd4, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xf7, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xf2, 0x5f, 0xff, 0xff, 0xf3, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xf2, 0x6, 0xff, 0xff,
    0xfb, 0x0, 0x9, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x8f, 0xff, 0xff, 0x10, 0xe, 0xff, 0xff, 0xff,
    0xf2, 0x2, 0x9, 0xff, 0xff, 0x60, 0x2f, 0xff,
    0xd3, 0xdf, 0xf2, 0xe, 0x20, 0xaf, 0xff, 0xa0,
    0x5f, 0xff, 0x80, 0x1d, 0xf2, 0xf, 0xd0, 0xf,
    0xff, 0xc0, 0x7f, 0xff, 0xf7, 0x1, 0xd2, 0xe,
    0x20, 0xaf, 0xff, 0xe0, 0x9f, 0xff, 0xff, 0x70,
    0x11, 0x2, 0x9, 0xff, 0xff, 0xf0, 0x9f, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xf0,
    0xaf, 0xff, 0xff, 0xff, 0x60, 0x5, 0xff, 0xff,
    0xff, 0xf1, 0xaf, 0xff, 0xff, 0xfe, 0x20, 0x1,
    0xdf, 0xff, 0xff, 0xf1, 0x9f, 0xff, 0xff, 0xe2,
    0x0, 0x0, 0x1e, 0xff, 0xff, 0xf0, 0x8f, 0xff,
    0xfe, 0x20, 0x52, 0x7, 0x2, 0xef, 0xff, 0xf0,
    0x6f, 0xff, 0xe2, 0x5, 0xf2, 0xf, 0x60, 0x3f,
    0xff, 0xe0, 0x4f, 0xff, 0x70, 0x5f, 0xf2, 0xf,
    0xa0, 0x1d, 0xff, 0xc0, 0xf, 0xff, 0xf9, 0xff,
    0xf2, 0xa, 0x1, 0xdf, 0xff, 0x90, 0xc, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x1d, 0xff, 0xff, 0x40,
    0x5, 0xff, 0xff, 0xff, 0xf3, 0x1, 0xdf, 0xff,
    0xfe, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xf3, 0x1d,
    0xff, 0xff, 0xf7, 0x0, 0x0, 0x2e, 0xff, 0xff,
    0xf4, 0xdf, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x1,
    0xbf, 0xff, 0xfe, 0xff, 0xff, 0xfc, 0x10, 0x0,
    0x0, 0x0, 0x4, 0xae, 0xff, 0xff, 0xea, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x10,
    0x0, 0x0, 0x0, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x0, 0x7, 0xbb, 0xbb, 0xbb, 0x50,
    0x0, 0x0, 0x0, 0x12, 0x22, 0x22, 0x4f, 0xff,
    0xff, 0xff, 0xf3, 0x22, 0x22, 0x20, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xdd, 0xdd, 0xdd, 0xdd, 0xdd,
    0xdd, 0xdd, 0xdd, 0xdd, 0x40, 0x7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x7, 0xff, 0xfc, 0x7f, 0xff,
    0x9a, 0xff, 0xf6, 0xdf, 0xff, 0x50, 0x7, 0xff,
    0xf7, 0xf, 0xff, 0x24, 0xff, 0xd0, 0x9f, 0xff,
    0x50, 0x7, 0xff, 0xf7, 0xf, 0xff, 0x24, 0xff,
    0xd0, 0x9f, 0xff, 0x50, 0x7, 0xff, 0xf7, 0xf,
    0xff, 0x24, 0xff, 0xd0, 0x9f, 0xff, 0x50, 0x7,
    0xff, 0xf7, 0xf, 0xff, 0x24, 0xff, 0xd0, 0x9f,
    0xff, 0x50, 0x7, 0xff, 0xf7, 0xf, 0xff, 0x24,
    0xff, 0xd0, 0x9f, 0xff, 0x50, 0x7, 0xff, 0xf7,
    0xf, 0xff, 0x24, 0xff, 0xd0, 0x9f, 0xff, 0x50,
    0x7, 0xff, 0xf7, 0xf, 0xff, 0x24, 0xff, 0xd0,
    0x9f, 0xff, 0x50, 0x7, 0xff, 0xf7, 0xf, 0xff,
    0x24, 0xff, 0xd0, 0x9f, 0xff, 0x50, 0x7, 0xff,
    0xf7, 0xf, 0xff, 0x24, 0xff, 0xd0, 0x9f, 0xff,
    0x50, 0x7, 0xff, 0xf7, 0xf, 0xff, 0x24, 0xff,
    0xd0, 0x9f, 0xff, 0x50, 0x7, 0xff, 0xf7, 0xf,
    0xff, 0x24, 0xff, 0xd0, 0x9f, 0xff, 0x50, 0x7,
    0xff, 0xfa, 0x2f, 0xff, 0x57, 0xff, 0xe1, 0xbf,
    0xff, 0x50, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x20,
    0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x21, 0x0, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xdb, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xe2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xc, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9e, 0x20, 0xcf, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xe2, 0xc,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xfe, 0x20, 0xcf, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xe2, 0xc, 0xfc, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xfe, 0x20,
    0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xc1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xff, 0xff, 0xfc, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xec, 0xa8, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x0, 0x0, 0x4a, 0xbc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xcc, 0xa6, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0x84,
    0xff, 0xff, 0xfa, 0x3e, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x3f, 0xff, 0xa0, 0x2, 0xef, 0xff, 0xff, 0xf4,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x3, 0xfa, 0x0, 0x1, 0xef, 0xff, 0xff, 0xf4,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x20, 0x0, 0x1d, 0xff, 0xff, 0xff, 0xf4,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x1, 0xd6, 0x0, 0x3, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x1d, 0xff, 0x60, 0x0, 0xdf, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0x41,
    0xdf, 0xff, 0xf7, 0xb, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x8e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xea, 0x10,

    /* U+F7C2 "" */
    0x0, 0x0, 0x3, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xc5, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc0, 0x3, 0xff, 0x82,
    0x3f, 0x92, 0x3f, 0x92, 0x5f, 0xfe, 0x3, 0xff,
    0xf7, 0x1, 0xf7, 0x1, 0xf7, 0x3, 0xff, 0xe3,
    0xff, 0xff, 0x70, 0x1f, 0x70, 0x1f, 0x70, 0x3f,
    0xfe, 0xff, 0xff, 0xf7, 0x1, 0xf7, 0x1, 0xf7,
    0x3, 0xff, 0xef, 0xff, 0xff, 0x81, 0x2f, 0x81,
    0x2f, 0x81, 0x4f, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x9, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x12,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x10, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xf1, 0x0, 0x0, 0x5, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff,
    0xf1, 0x0, 0x0, 0x6f, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xff, 0xf1, 0x0, 0x8,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xff, 0xf1, 0x0, 0x9f, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xf1,
    0xa, 0xff, 0xff, 0xff, 0xcc, 0xcc, 0xcc, 0xcc,
    0xcc, 0xcc, 0xcf, 0xff, 0xf1, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1, 0x2e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd0, 0x2, 0xdf, 0xff, 0xfe,
    0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
    0x0, 0x0, 0x1d, 0xff, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xcf, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x52, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 240, .box_w = 5, .box_h = 19, .ofs_x = 5, .ofs_y = -4},
    {.bitmap_index = 48, .adv_w = 240, .box_w = 6, .box_h = 8, .ofs_x = 4, .ofs_y = 12},
    {.bitmap_index = 72, .adv_w = 240, .box_w = 11, .box_h = 3, .ofs_x = 2, .ofs_y = 5},
    {.bitmap_index = 89, .adv_w = 240, .box_w = 4, .box_h = 4, .ofs_x = 6, .ofs_y = -1},
    {.bitmap_index = 97, .adv_w = 180, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 157, .adv_w = 180, .box_w = 7, .box_h = 13, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 203, .adv_w = 180, .box_w = 8, .box_h = 12, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 251, .adv_w = 180, .box_w = 6, .box_h = 12, .ofs_x = 3, .ofs_y = -1},
    {.bitmap_index = 287, .adv_w = 180, .box_w = 9, .box_h = 13, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 346, .adv_w = 180, .box_w = 8, .box_h = 14, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 402, .adv_w = 180, .box_w = 8, .box_h = 12, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 450, .adv_w = 180, .box_w = 8, .box_h = 13, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 502, .adv_w = 180, .box_w = 7, .box_h = 12, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 544, .adv_w = 180, .box_w = 6, .box_h = 13, .ofs_x = 3, .ofs_y = -1},
    {.bitmap_index = 583, .adv_w = 240, .box_w = 8, .box_h = 19, .ofs_x = 4, .ofs_y = -3},
    {.bitmap_index = 659, .adv_w = 180, .box_w = 11, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 747, .adv_w = 180, .box_w = 11, .box_h = 16, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 835, .adv_w = 180, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 905, .adv_w = 180, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 980, .adv_w = 180, .box_w = 10, .box_h = 16, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 1060, .adv_w = 180, .box_w = 9, .box_h = 16, .ofs_x = 3, .ofs_y = -1},
    {.bitmap_index = 1132, .adv_w = 180, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1215, .adv_w = 180, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1298, .adv_w = 180, .box_w = 9, .box_h = 16, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 1370, .adv_w = 180, .box_w = 8, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1422, .adv_w = 180, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1497, .adv_w = 180, .box_w = 9, .box_h = 15, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1565, .adv_w = 180, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1648, .adv_w = 180, .box_w = 9, .box_h = 15, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1716, .adv_w = 180, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1786, .adv_w = 180, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1869, .adv_w = 180, .box_w = 9, .box_h = 17, .ofs_x = 2, .ofs_y = -3},
    {.bitmap_index = 1946, .adv_w = 180, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2021, .adv_w = 180, .box_w = 8, .box_h = 15, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 2081, .adv_w = 180, .box_w = 11, .box_h = 15, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2164, .adv_w = 180, .box_w = 10, .box_h = 15, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 2239, .adv_w = 180, .box_w = 10, .box_h = 14, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2309, .adv_w = 180, .box_w = 12, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2387, .adv_w = 180, .box_w = 10, .box_h = 15, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2462, .adv_w = 180, .box_w = 10, .box_h = 15, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2537, .adv_w = 180, .box_w = 10, .box_h = 15, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2612, .adv_w = 180, .box_w = 8, .box_h = 12, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 2660, .adv_w = 180, .box_w = 8, .box_h = 14, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 2716, .adv_w = 180, .box_w = 8, .box_h = 10, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2756, .adv_w = 180, .box_w = 10, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2841, .adv_w = 180, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2891, .adv_w = 180, .box_w = 11, .box_h = 17, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2985, .adv_w = 180, .box_w = 13, .box_h = 17, .ofs_x = 0, .ofs_y = -6},
    {.bitmap_index = 3096, .adv_w = 180, .box_w = 8, .box_h = 14, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 3152, .adv_w = 180, .box_w = 7, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3198, .adv_w = 180, .box_w = 7, .box_h = 14, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 3247, .adv_w = 180, .box_w = 8, .box_h = 16, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 3311, .adv_w = 180, .box_w = 7, .box_h = 18, .ofs_x = 3, .ofs_y = -1},
    {.bitmap_index = 3374, .adv_w = 180, .box_w = 11, .box_h = 13, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 3446, .adv_w = 180, .box_w = 8, .box_h = 11, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3490, .adv_w = 180, .box_w = 10, .box_h = 9, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 3535, .adv_w = 180, .box_w = 10, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3605, .adv_w = 180, .box_w = 7, .box_h = 15, .ofs_x = 3, .ofs_y = -2},
    {.bitmap_index = 3658, .adv_w = 180, .box_w = 7, .box_h = 10, .ofs_x = 3, .ofs_y = 0},
    {.bitmap_index = 3693, .adv_w = 180, .box_w = 8, .box_h = 13, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 3745, .adv_w = 180, .box_w = 12, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 3811, .adv_w = 180, .box_w = 8, .box_h = 10, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3851, .adv_w = 180, .box_w = 9, .box_h = 12, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 3905, .adv_w = 180, .box_w = 11, .box_h = 7, .ofs_x = 1, .ofs_y = 1},
    {.bitmap_index = 3944, .adv_w = 180, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 4004, .adv_w = 180, .box_w = 11, .box_h = 13, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 4076, .adv_w = 180, .box_w = 10, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 4136, .adv_w = 400, .box_w = 26, .box_h = 27, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 4487, .adv_w = 400, .box_w = 25, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4725, .adv_w = 400, .box_w = 25, .box_h = 23, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5013, .adv_w = 400, .box_w = 25, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5251, .adv_w = 275, .box_w = 18, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5413, .adv_w = 400, .box_w = 25, .box_h = 25, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5726, .adv_w = 400, .box_w = 25, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 6051, .adv_w = 450, .box_w = 29, .box_h = 23, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6385, .adv_w = 400, .box_w = 25, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 6710, .adv_w = 450, .box_w = 29, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6986, .adv_w = 400, .box_w = 25, .box_h = 25, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7299, .adv_w = 200, .box_w = 13, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7429, .adv_w = 300, .box_w = 19, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 7619, .adv_w = 450, .box_w = 29, .box_h = 25, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7982, .adv_w = 400, .box_w = 25, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8220, .adv_w = 350, .box_w = 16, .box_h = 24, .ofs_x = 3, .ofs_y = -3},
    {.bitmap_index = 8412, .adv_w = 350, .box_w = 22, .box_h = 27, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 8709, .adv_w = 350, .box_w = 22, .box_h = 23, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8962, .adv_w = 350, .box_w = 22, .box_h = 23, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 9215, .adv_w = 350, .box_w = 17, .box_h = 24, .ofs_x = 2, .ofs_y = -3},
    {.bitmap_index = 9419, .adv_w = 350, .box_w = 24, .box_h = 23, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 9695, .adv_w = 250, .box_w = 14, .box_h = 23, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 9856, .adv_w = 250, .box_w = 14, .box_h = 23, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 10017, .adv_w = 350, .box_w = 22, .box_h = 23, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10270, .adv_w = 350, .box_w = 22, .box_h = 5, .ofs_x = 0, .ofs_y = 7},
    {.bitmap_index = 10325, .adv_w = 450, .box_w = 29, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10601, .adv_w = 500, .box_w = 33, .box_h = 26, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 11030, .adv_w = 450, .box_w = 30, .box_h = 26, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 11420, .adv_w = 400, .box_w = 25, .box_h = 23, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11708, .adv_w = 350, .box_w = 22, .box_h = 14, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 11862, .adv_w = 350, .box_w = 22, .box_h = 14, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 12016, .adv_w = 500, .box_w = 32, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12320, .adv_w = 400, .box_w = 25, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12558, .adv_w = 400, .box_w = 25, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 12883, .adv_w = 400, .box_w = 26, .box_h = 27, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 13234, .adv_w = 350, .box_w = 23, .box_h = 23, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13499, .adv_w = 350, .box_w = 22, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 13785, .adv_w = 350, .box_w = 22, .box_h = 23, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 14038, .adv_w = 250, .box_w = 17, .box_h = 26, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 14259, .adv_w = 350, .box_w = 22, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 14545, .adv_w = 350, .box_w = 22, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 14831, .adv_w = 450, .box_w = 29, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15107, .adv_w = 400, .box_w = 27, .box_h = 27, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 15472, .adv_w = 300, .box_w = 19, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 15719, .adv_w = 500, .box_w = 32, .box_h = 24, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 16103, .adv_w = 500, .box_w = 32, .box_h = 17, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 16375, .adv_w = 500, .box_w = 32, .box_h = 17, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 16647, .adv_w = 500, .box_w = 32, .box_h = 17, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 16919, .adv_w = 500, .box_w = 32, .box_h = 17, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 17191, .adv_w = 500, .box_w = 32, .box_h = 17, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 17463, .adv_w = 500, .box_w = 32, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 17783, .adv_w = 350, .box_w = 20, .box_h = 26, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 18043, .adv_w = 350, .box_w = 22, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 18329, .adv_w = 400, .box_w = 26, .box_h = 26, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 18667, .adv_w = 500, .box_w = 32, .box_h = 19, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 18971, .adv_w = 300, .box_w = 19, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 19218, .adv_w = 402, .box_w = 26, .box_h = 17, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_0[] = {
    0x0, 0x1, 0xc, 0xd, 0xf, 0x10, 0x11, 0x12,
    0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x1e
};

static const uint16_t unicode_list_3[] = {
    0x0, 0x7, 0xa, 0xb, 0xc, 0x10, 0x12, 0x14,
    0x18, 0x1b, 0x20, 0x25, 0x26, 0x27, 0x3d, 0x47,
    0x4a, 0x4b, 0x4c, 0x50, 0x51, 0x52, 0x53, 0x66,
    0x67, 0x6d, 0x6f, 0x70, 0x73, 0x76, 0x77, 0x78,
    0x7a, 0x92, 0x94, 0xc3, 0xc4, 0xc6, 0xe6, 0xe9,
    0xf2, 0x11b, 0x123, 0x15a, 0x1ea, 0x23f, 0x240, 0x241,
    0x242, 0x243, 0x286, 0x292, 0x2ec, 0x303, 0x559, 0x7c1,
    0x8a1
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 33, .range_length = 31, .glyph_id_start = 1,
        .unicode_list = unicode_list_0, .glyph_id_ofs_list = NULL, .list_length = 15, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    },
    {
        .range_start = 65, .range_length = 26, .glyph_id_start = 16,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 97, .range_length = 26, .glyph_id_start = 42,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 61441, .range_length = 2210, .glyph_id_start = 68,
        .unicode_list = unicode_list_3, .glyph_id_ofs_list = NULL, .list_length = 57, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 4,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t lv_font_Slideyouran_Regular_25 = {
#else
lv_font_t lv_font_Slideyouran_Regular_25 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 29,          /*The maximum line height required by the font*/
    .base_line = 6,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -2,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};



#endif /*#if LV_FONT_SLIDEYOURAN_REGULAR_25*/

