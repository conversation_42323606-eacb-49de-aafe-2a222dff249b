#ifndef _Cloud_H_
#define _Cloud_H_

#include "stm32f10x.h"
#include "MQTTPacket.h"
#include "Esp8266.h"
#include <string.h>
#include "uart1.h"
#include <stdio.h>
#include "dht11.h"
#include "kqm.h"
#include "BH1750.h"
#define Client_ID "6835184a9314d118511fe8fb_Device_204_0_0_2025060509"
#define Password "899612e8791ddaaebf00d360fa75ab8170f96dbd708aa01b5dc9ad453658e8f5"
#define Username "6835184a9314d118511fe8fb_Device_204"
#define Publish_Topic "$oc/devices/6835184a9314d118511fe8fb_Device_204/sys/properties/report"
#define  Subcribe_Topic   "$oc/devices/683420b39314d118511fa8f1_Device_204/sys/commands/#"
extern uint16_t PUBLISH_Period[2];
extern uint16_t HEART_Period[2];
void Cloud_Connect(void);
void Cloud_Publish(void);
void Cloud_Rec_Time(void);
void Cloud_MQTT_Ping(void);
void Cloud_MQTT_Sub(void);
void Cloud_Cmd(void);
#endif
