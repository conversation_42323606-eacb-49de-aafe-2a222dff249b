/*
 * Copyright 2025 NXP
 * SPDX-License-Identifier: MIT
 */

#ifndef GUI_GUIDER_H
#define GUI_GUIDER_H
#ifdef __cplusplus
extern "C" {
#endif

#include "lvgl.h"
#include "guider_fonts.h"

typedef struct
{
	lv_obj_t *screen;
	lv_obj_t *screen_btn_1;
	lv_obj_t *screen_btn_1_label;
	lv_obj_t *screen_label_1;
	lv_obj_t *screen_1;
	lv_obj_t *screen_1_img_1;
	lv_obj_t *screen_1_cont_1;
	lv_obj_t *screen_1_label_7;
	lv_obj_t *screen_1_label_6;
	lv_obj_t *screen_1_label_4;
	lv_obj_t *screen_1_img_3;
	lv_obj_t *screen_1_label_2;
	lv_obj_t *screen_1_img_2;
	lv_obj_t *screen_1_label_1;
	lv_obj_t *screen_1_label_3;
	lv_obj_t *screen_1_label_5;
	lv_obj_t *screen_1_label_8;
	lv_obj_t *screen_1_cont_2;
	lv_obj_t *screen_1_img_6;
	lv_obj_t *screen_1_img_5;
	lv_obj_t *screen_1_img_4;
	lv_obj_t *screen_1_label_11;
	lv_obj_t *screen_1_label_10;
	lv_obj_t *screen_1_label_9;
	lv_obj_t *screen_1_cont_3;
	lv_obj_t *screen_1_label_19;
	lv_obj_t *screen_1_label_18;
	lv_obj_t *screen_1_label_17;
	lv_obj_t *screen_1_label_16;
	lv_obj_t *screen_1_label_15;
	lv_obj_t *screen_1_label_14;
	lv_obj_t *screen_1_label_13;
	lv_obj_t *screen_1_label_12;
	lv_obj_t *screen_1_img_10;
	lv_obj_t *screen_1_img_9;
	lv_obj_t *screen_1_img_8;
	lv_obj_t *screen_1_img_7;
}lv_ui;

void setup_ui(lv_ui *ui);
extern lv_ui guider_ui;
void setup_scr_screen(lv_ui *ui);
void setup_scr_screen_1(lv_ui *ui);
LV_IMG_DECLARE(_dianliang_alpha_39x27);
LV_IMG_DECLARE(_CO2_alpha_40x40);
LV_IMG_DECLARE(_TMP_alpha_20x20);
LV_IMG_DECLARE(_sunshine_alpha_36x38);
LV_IMG_DECLARE(_wifi_alpha_55x55);
LV_IMG_DECLARE(_SUN_alpha_40x40);
LV_IMG_DECLARE(_didian_alpha_39x30);
LV_IMG_DECLARE(_wifi_you_alpha_55x55);
LV_IMG_DECLARE(_HMP_alpha_20x20);
LV_IMG_DECLARE(_9_alpha_320x240);

#ifdef __cplusplus
}
#endif
#endif
