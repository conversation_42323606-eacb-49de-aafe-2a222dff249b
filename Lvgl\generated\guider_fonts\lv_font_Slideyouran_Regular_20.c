/*******************************************************************************
 * Size: 20 px
 * Bpp: 4
 * Opts: 
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef LV_FONT_SLIDEYOURAN_REGULAR_20
#define LV_FONT_SLIDEYOURAN_REGULAR_20 1
#endif

#if LV_FONT_SLIDEYOURAN_REGULAR_20

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+0021 "!" */
    0x0, 0x0, 0x3d, 0x30, 0x1f, 0xe0, 0x2f, 0xf1,
    0x2f, 0xf1, 0x2f, 0xf0, 0xf, 0xf0, 0xe, 0xf0,
    0xc, 0xd0, 0x9, 0x60, 0x2, 0x20, 0x0, 0x0,
    0x28, 0x20, 0x4f, 0xf2, 0x5, 0x30,

    /* U+0022 "\"" */
    0x3, 0x3, 0x90, 0xc0, 0x7e, 0x1f, 0x7, 0xe3,
    0xe0, 0x4d, 0x1a, 0x0, 0x20, 0x0, 0x0,

    /* U+002D "-" */
    0x13, 0x45, 0x78, 0x85, 0xb, 0xff, 0xfe, 0xff,
    0xe0, 0x24, 0x40, 0x0, 0x1, 0x0,

    /* U+002E "." */
    0x3, 0x72, 0xf, 0xfc, 0xb, 0xe4,

    /* U+0030 "0" */
    0x0, 0x59, 0x99, 0x20, 0x6, 0x70, 0x9, 0xc0,
    0xc, 0x0, 0x6, 0xf0, 0xc, 0x0, 0x6, 0xf0,
    0x2b, 0x0, 0x7, 0xf0, 0x4b, 0x0, 0x9, 0xf0,
    0x6b, 0x0, 0xe, 0xb0, 0x5d, 0x10, 0x8f, 0x40,
    0x7, 0xec, 0x92, 0x0,

    /* U+0031 "1" */
    0x0, 0x1, 0x0, 0x38, 0xf5, 0x0, 0x3f, 0x10,
    0x3, 0xf0, 0x0, 0x6d, 0x0, 0x7, 0xc0, 0x0,
    0x9c, 0x0, 0xc, 0xa0, 0x0, 0xf9, 0x7, 0x9f,
    0xe7, 0x0, 0x0, 0x0,

    /* U+0032 "2" */
    0x0, 0x68, 0x87, 0x0, 0x42, 0x0, 0xd2, 0x0,
    0x0, 0x1f, 0x30, 0x0, 0xa, 0xc0, 0x0, 0x6,
    0xd1, 0x0, 0x2, 0xe2, 0x0, 0x0, 0xc3, 0x0,
    0x0, 0x86, 0x0, 0x0, 0x1f, 0xbc, 0xc7, 0x0,
    0x62, 0x0, 0x0,

    /* U+0033 "3" */
    0x0, 0x3, 0x10, 0x4, 0x98, 0xf0, 0x4, 0x0,
    0xe1, 0x0, 0x2, 0xc0, 0x0, 0x6d, 0x20, 0x5,
    0x8a, 0xd0, 0x0, 0x0, 0xd1, 0x0, 0x0, 0xe0,
    0x20, 0xa, 0xa0, 0x2a, 0xc5, 0x0,

    /* U+0034 "4" */
    0x0, 0x0, 0x30, 0x0, 0x0, 0x7, 0xa0, 0x0,
    0x0, 0x1e, 0x60, 0x0, 0x0, 0xa6, 0x80, 0x0,
    0x3, 0xa0, 0x70, 0x0, 0xa, 0x10, 0x71, 0x10,
    0x1d, 0xac, 0xff, 0xf0, 0x2, 0x46, 0x74, 0x10,
    0x0, 0x4, 0x30, 0x0, 0x0, 0x6, 0x10, 0x0,
    0x0, 0x2, 0x0, 0x0,

    /* U+0035 "5" */
    0x11, 0x0, 0x0, 0x2f, 0xff, 0xf3, 0x39, 0x0,
    0x10, 0x48, 0x0, 0x0, 0x5b, 0xcd, 0x80, 0x14,
    0x0, 0xd0, 0x0, 0x0, 0xc1, 0x0, 0x0, 0xe0,
    0x40, 0x8, 0x70, 0x9a, 0xba, 0x0, 0x8, 0x40,
    0x0,

    /* U+0036 "6" */
    0x0, 0x7, 0x70, 0x1, 0xdb, 0x70, 0xb, 0x80,
    0x0, 0x2d, 0x57, 0x20, 0x6a, 0x1, 0xd3, 0x86,
    0x0, 0xa6, 0xa6, 0x0, 0xb5, 0xb7, 0x3, 0xe0,
    0x3b, 0xad, 0x30, 0x0, 0x0, 0x0,

    /* U+0037 "7" */
    0x10, 0x0, 0x0, 0xaf, 0xff, 0xfe, 0x27, 0x65,
    0x87, 0x0, 0x0, 0xa0, 0x0, 0x5, 0x50, 0x0,
    0xa, 0x0, 0x0, 0x55, 0x0, 0x0, 0xa0, 0x0,
    0x2, 0xa0, 0x0, 0x3, 0x40, 0x0,

    /* U+0038 "8" */
    0x0, 0x11, 0x0, 0x4, 0x51, 0x90, 0xa, 0x0,
    0xa2, 0x1a, 0x0, 0xa0, 0xa, 0x55, 0x0, 0x1,
    0xbd, 0x30, 0x17, 0x3, 0xf0, 0x80, 0x3, 0xe0,
    0xb0, 0x7, 0xb0, 0x58, 0x7d, 0x30,

    /* U+0039 "9" */
    0x3, 0x69, 0x30, 0x70, 0x5b, 0x36, 0x4, 0xc4,
    0x80, 0x6a, 0x2b, 0x4a, 0x90, 0x23, 0x88, 0x0,
    0xa, 0x63, 0x0, 0xb2, 0x38, 0xa6, 0x0, 0x0,
    0x0,

    /* U+003F "?" */
    0x0, 0xae, 0xa1, 0xa, 0x63, 0xfa, 0x32, 0x0,
    0xed, 0x0, 0x0, 0xfc, 0x0, 0x6, 0xf6, 0x0,
    0x1e, 0x60, 0x0, 0xaa, 0x0, 0x0, 0xf6, 0x0,
    0x3, 0xf4, 0x0, 0x3, 0xf4, 0x0, 0x2, 0xc3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xe0, 0x0, 0x0, 0x10, 0x0,

    /* U+0041 "A" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xe1,
    0x0, 0x0, 0x0, 0xbf, 0x90, 0x0, 0x0, 0x2f,
    0xff, 0x20, 0x0, 0x8, 0xaf, 0xf7, 0x0, 0x0,
    0xd4, 0xcf, 0xa0, 0x0, 0x2e, 0x9, 0xfd, 0x0,
    0x8, 0xa0, 0x7f, 0xf1, 0x0, 0xee, 0xef, 0xff,
    0x30, 0x6c, 0x0, 0x2f, 0xf6, 0xc, 0x60, 0x0,
    0xff, 0x71, 0xf1, 0x0, 0xf, 0xf4, 0x1b, 0x0,
    0x0, 0x25, 0x0,

    /* U+0042 "B" */
    0x0, 0x3, 0x44, 0x20, 0x6, 0xc9, 0x89, 0xf6,
    0xd, 0x80, 0x0, 0xfd, 0xf, 0x70, 0x3, 0xfb,
    0x1f, 0x60, 0xc, 0xf6, 0x2f, 0xa7, 0xbf, 0xc0,
    0x3f, 0xef, 0xff, 0x80, 0x4f, 0x30, 0x7, 0xf3,
    0x5f, 0x20, 0x6, 0xf6, 0x8f, 0x10, 0xa, 0xf7,
    0xbd, 0x1, 0x9f, 0xf4, 0xbc, 0xdf, 0xfc, 0x50,
    0x1, 0x12, 0x0, 0x0,

    /* U+0043 "C" */
    0x0, 0x2, 0x9e, 0x40, 0x0, 0x6f, 0xde, 0xf4,
    0x6, 0xe3, 0x1, 0x72, 0xf, 0x30, 0x0, 0x0,
    0x4f, 0x0, 0x0, 0x0, 0x6f, 0x0, 0x0, 0x0,
    0x5f, 0x0, 0x0, 0x0, 0x5f, 0x10, 0x0, 0x0,
    0x3f, 0x50, 0x0, 0x0, 0xe, 0xd6, 0x69, 0x0,
    0x4, 0xef, 0xb4, 0x0,

    /* U+0044 "D" */
    0x2, 0x9e, 0xfb, 0x30, 0x18, 0x22, 0x4c, 0xf3,
    0xd, 0x10, 0x4, 0xf9, 0xf, 0x30, 0x5, 0xfc,
    0xf, 0x30, 0x8, 0xfb, 0x1f, 0x30, 0xc, 0xf9,
    0x1f, 0x20, 0x1f, 0xf5, 0x3f, 0x10, 0x7f, 0xf1,
    0x5e, 0x1, 0xff, 0xc0, 0x7d, 0x3c, 0xff, 0x60,
    0x7f, 0xff, 0xfb, 0x0, 0x0, 0x55, 0x10, 0x0,

    /* U+0045 "E" */
    0x1, 0x78, 0xbf, 0xf6, 0x3, 0xfb, 0xbb, 0xb2,
    0x5, 0xf1, 0x0, 0x0, 0x7, 0xf0, 0x0, 0x0,
    0x9, 0xf0, 0x0, 0x0, 0xa, 0xf7, 0x97, 0x20,
    0xb, 0xfc, 0xcc, 0x80, 0xc, 0xd0, 0x0, 0x0,
    0xd, 0xd0, 0x0, 0x0, 0xd, 0xb0, 0x0, 0x0,
    0xf, 0xc0, 0x0, 0x0, 0xa, 0xff, 0xff, 0x0,
    0x0, 0x13, 0x32, 0x0,

    /* U+0046 "F" */
    0x1, 0x0, 0x0, 0x0, 0xb, 0xef, 0xff, 0xe0,
    0xd, 0xb5, 0x64, 0x0, 0xf, 0xa0, 0x0, 0x0,
    0x1f, 0xc4, 0x43, 0x0, 0x2f, 0xfe, 0xeb, 0x0,
    0x3f, 0x80, 0x0, 0x0, 0x4f, 0x70, 0x0, 0x0,
    0x4f, 0x70, 0x0, 0x0, 0x4f, 0x50, 0x0, 0x0,
    0x5f, 0x50, 0x0, 0x0, 0x6f, 0x20, 0x0, 0x0,
    0x6, 0x0, 0x0, 0x0,

    /* U+0047 "G" */
    0x0, 0x0, 0x47, 0x10, 0x0, 0x1b, 0xb7, 0xc3,
    0x1, 0xd8, 0x0, 0x2c, 0x9, 0xb0, 0x0, 0x1,
    0x1f, 0x30, 0x0, 0x0, 0x6f, 0x0, 0x0, 0x0,
    0xab, 0x0, 0x69, 0xa3, 0xba, 0x0, 0x26, 0xf7,
    0xba, 0x0, 0x6, 0xf6, 0x9d, 0x0, 0x7, 0xf5,
    0x2f, 0x71, 0x1c, 0xf2, 0x3, 0xaf, 0xff, 0xb0,

    /* U+0048 "H" */
    0x1, 0x10, 0x0, 0x22, 0x9, 0x80, 0x0, 0xac,
    0xa, 0xd0, 0x0, 0xbf, 0xe, 0xd0, 0x0, 0xdd,
    0xf, 0xff, 0xff, 0xfa, 0x3f, 0xff, 0xfc, 0xf8,
    0x6f, 0xa0, 0x0, 0xf7, 0x7f, 0x90, 0x1, 0xf7,
    0x7f, 0x70, 0x2, 0xf6, 0x7f, 0x50, 0x4, 0xf4,
    0x6a, 0x10, 0x5, 0xf0, 0x30, 0x0, 0x0, 0x20,

    /* U+0049 "I" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xe0,
    0x0, 0x36, 0xf8, 0x10, 0x0, 0x3, 0xf5, 0x0,
    0x0, 0x5, 0xf3, 0x0, 0x0, 0x6, 0xf2, 0x0,
    0x0, 0x7, 0xf1, 0x0, 0x0, 0x8, 0xf0, 0x0,
    0x0, 0xa, 0xc0, 0x0, 0x0, 0xc, 0x70, 0x0,
    0x1, 0x3d, 0x93, 0x0, 0xc, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+004A "J" */
    0x0, 0x0, 0x7, 0x10, 0x0, 0x0, 0xf4, 0x0,
    0x0, 0x2f, 0x40, 0x0, 0x6, 0xf3, 0x0, 0x0,
    0x9f, 0x20, 0x0, 0xb, 0xf1, 0x0, 0x0, 0xdf,
    0x10, 0x0, 0xe, 0xe0, 0x0, 0x2, 0xfb, 0x0,
    0x72, 0xaf, 0x60, 0x8, 0xfd, 0x50, 0x0,

    /* U+004B "K" */
    0x0, 0x10, 0x0, 0x0, 0x7, 0xc0, 0x0, 0x89,
    0x9, 0xc0, 0x6, 0xf2, 0xa, 0xb0, 0x2f, 0x40,
    0xc, 0xa0, 0xd7, 0x0, 0xe, 0x98, 0xcb, 0x0,
    0xf, 0xbd, 0x1f, 0x10, 0x2f, 0xe2, 0xb, 0x70,
    0x3f, 0x60, 0x8, 0xc0, 0x5f, 0x30, 0x5, 0xf0,
    0x6f, 0x0, 0x0, 0xf5, 0x16, 0x0, 0x0, 0x74,

    /* U+004C "L" */
    0x0, 0x0, 0x0, 0x0, 0xc5, 0x0, 0x0, 0xf,
    0x60, 0x0, 0x2, 0xf5, 0x0, 0x0, 0x3f, 0x40,
    0x0, 0x5, 0xf2, 0x0, 0x0, 0x6f, 0x0, 0x0,
    0x8, 0xf0, 0x0, 0x0, 0x9d, 0x0, 0x0, 0xa,
    0xc0, 0x0, 0x0, 0x8b, 0x25, 0x66, 0x11, 0xdf,
    0xff, 0xe1,

    /* U+004D "M" */
    0x1, 0x90, 0x0, 0x6, 0xa0, 0x4f, 0x90, 0x3,
    0xff, 0x6, 0xef, 0x30, 0xbd, 0xf0, 0x86, 0xec,
    0x5b, 0x8e, 0x9, 0x44, 0xff, 0x29, 0xe0, 0xa3,
    0x4, 0xa0, 0xac, 0xb, 0x30, 0x0, 0xb, 0xb0,
    0xc2, 0x0, 0x0, 0xca, 0xe, 0x0, 0x0, 0xd,
    0x90, 0xe0, 0x0, 0x0, 0xd8, 0xa, 0x0, 0x0,
    0x7, 0x20, 0x0, 0x0, 0x0, 0x0,

    /* U+004E "N" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0xf9, 0x20, 0xaa,
    0x3, 0xff, 0x90, 0xdb, 0x6, 0xfd, 0xd0, 0xfb,
    0x8, 0xe9, 0xf2, 0xfb, 0x8, 0xd6, 0xf9, 0xfa,
    0x8, 0xb2, 0xfe, 0xf8, 0xa, 0xa0, 0xef, 0xf6,
    0xc, 0x80, 0x8f, 0xf5, 0xe, 0x50, 0xd, 0xf4,
    0xf, 0x40, 0x6, 0xd2, 0x6, 0x10, 0x0, 0x0,

    /* U+004F "O" */
    0x0, 0x9, 0xfe, 0x50, 0x0, 0xa5, 0x86, 0xf2,
    0x7, 0x60, 0x0, 0xc8, 0xe, 0x0, 0x0, 0xbb,
    0x5d, 0x0, 0x0, 0xbc, 0x9b, 0x0, 0x0, 0xdc,
    0xca, 0x0, 0x0, 0xfa, 0xda, 0x0, 0x4, 0xf7,
    0xcb, 0x0, 0x9, 0xf3, 0x9e, 0x0, 0x5f, 0xe0,
    0x2e, 0xb7, 0xff, 0x50, 0x2, 0x99, 0x34, 0x0,

    /* U+0050 "P" */
    0x0, 0x9d, 0xff, 0xa1, 0x3, 0xf3, 0x16, 0xfa,
    0x6, 0xe0, 0x4, 0xfe, 0x8, 0xd0, 0xc, 0xfc,
    0xa, 0xc2, 0xcf, 0xf4, 0xc, 0xff, 0xff, 0x80,
    0xe, 0xa7, 0x72, 0x0, 0xf, 0x80, 0x0, 0x0,
    0x2f, 0x70, 0x0, 0x0, 0x3f, 0x50, 0x0, 0x0,
    0x3f, 0x30, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,

    /* U+0051 "Q" */
    0x0, 0x39, 0x7b, 0x30, 0x1, 0xe2, 0x4, 0xe0,
    0x9, 0x90, 0x0, 0xf4, 0xf, 0x40, 0x0, 0xc9,
    0x2f, 0x0, 0x0, 0xca, 0x2e, 0x0, 0x0, 0xd9,
    0x1f, 0x0, 0x1, 0xf6, 0xe, 0x20, 0x8, 0xf1,
    0xc, 0xa5, 0x7f, 0x60, 0x4, 0xef, 0xf5, 0x0,
    0x0, 0x3, 0xf4, 0x0, 0x0, 0x0, 0x9d, 0x50,
    0x0, 0x0, 0x4, 0x70,

    /* U+0052 "R" */
    0x0, 0x39, 0xdc, 0xa0, 0x6, 0xd5, 0x2, 0xf5,
    0x8, 0x90, 0x7, 0xf5, 0xb, 0x80, 0x1e, 0xe0,
    0xc, 0x87, 0xef, 0x60, 0xe, 0xdc, 0xd7, 0x0,
    0xf, 0x40, 0x5b, 0x0, 0xf, 0x30, 0x2e, 0x0,
    0x2f, 0x20, 0xe, 0x20, 0x3f, 0x0, 0x9, 0x80,
    0x2b, 0x0, 0x5, 0xd0, 0x0, 0x0, 0x0, 0x20,

    /* U+0053 "S" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8e, 0xf8, 0x0,
    0xc9, 0x12, 0xc0, 0x6b, 0x0, 0x4, 0xa, 0x60,
    0x0, 0x0, 0x4a, 0x0, 0x0, 0x0, 0x3a, 0xa5,
    0x0, 0x0, 0x1, 0xf7, 0x0, 0x0, 0xe, 0xc0,
    0x0, 0x2, 0xfa, 0x2, 0x1, 0xbf, 0x42, 0xed,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,

    /* U+0054 "T" */
    0x56, 0x79, 0xef, 0xfd, 0x46, 0xff, 0xff, 0xff,
    0xf6, 0x7, 0x66, 0xf8, 0x76, 0x10, 0x0, 0xf,
    0x0, 0x0, 0x0, 0x2, 0xf0, 0x0, 0x0, 0x0,
    0x3e, 0x0, 0x0, 0x0, 0x4, 0xd0, 0x0, 0x0,
    0x0, 0x6c, 0x0, 0x0, 0x0, 0x7, 0x90, 0x0,
    0x0, 0x0, 0x98, 0x0, 0x0, 0x0, 0xa, 0x70,
    0x0, 0x0, 0x0, 0x31, 0x0, 0x0,

    /* U+0055 "U" */
    0x4, 0x0, 0x0, 0x7, 0xb, 0x0, 0x0, 0x1e,
    0xc, 0x0, 0x0, 0x2c, 0xc, 0x0, 0x0, 0x3b,
    0xc, 0x0, 0x0, 0x5a, 0x1d, 0x0, 0x0, 0x7a,
    0x4c, 0x0, 0x0, 0x99, 0x5b, 0x0, 0x0, 0xc7,
    0x5b, 0x0, 0x2, 0xf4, 0x4e, 0x0, 0x3b, 0xe0,
    0xa, 0xff, 0xb5, 0x20, 0x0, 0x0, 0x0, 0x0,

    /* U+0056 "V" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb3, 0x37, 0x0, 0x0, 0xf, 0x20, 0xc0, 0x0,
    0x5, 0xc0, 0xc, 0x20, 0x0, 0xc5, 0x0, 0x95,
    0x0, 0x3d, 0x0, 0x7, 0x80, 0xa, 0x60, 0x0,
    0x3b, 0x2, 0xe0, 0x0, 0x0, 0xe0, 0x98, 0x0,
    0x0, 0xc, 0x5e, 0x10, 0x0, 0x0, 0x7f, 0x90,
    0x0, 0x0, 0x1, 0xc2, 0x0, 0x0,

    /* U+0057 "W" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x4, 0x0, 0xb,
    0x30, 0x1d, 0xb, 0x0, 0x2f, 0x60, 0x6b, 0xa,
    0x0, 0x9f, 0x70, 0xa8, 0xa, 0x10, 0xed, 0x90,
    0xe4, 0x8, 0x34, 0xc9, 0xb2, 0xf0, 0x6, 0x59,
    0x78, 0xe7, 0xc0, 0x5, 0x7e, 0x26, 0xfc, 0x60,
    0x3, 0xcb, 0x3, 0xff, 0x20, 0x2, 0xe5, 0x0,
    0xfd, 0x0, 0x0, 0x50, 0x0, 0x35, 0x0,

    /* U+0058 "X" */
    0x0, 0x0, 0x0, 0x1, 0xa, 0x30, 0x0, 0x7c,
    0x9, 0xd0, 0x1, 0xea, 0x2, 0xf5, 0xb, 0xf2,
    0x0, 0xbb, 0x6f, 0x90, 0x0, 0x5f, 0xfe, 0x0,
    0x0, 0xf, 0xf4, 0x0, 0x0, 0x5f, 0xf3, 0x0,
    0x0, 0xde, 0xfc, 0x0, 0x9, 0xf5, 0xaf, 0x20,
    0x1f, 0xa0, 0x3f, 0x70, 0x7, 0x0, 0x8, 0x60,

    /* U+0059 "Y" */
    0x0, 0x0, 0x0, 0x10, 0x62, 0x0, 0x4, 0xe0,
    0x5d, 0x30, 0x3f, 0x70, 0xa, 0xf5, 0xea, 0x0,
    0x0, 0x9f, 0xb0, 0x0, 0x0, 0x4f, 0x20, 0x0,
    0x0, 0x6f, 0x0, 0x0, 0x0, 0x8d, 0x0, 0x0,
    0x0, 0xab, 0x0, 0x0, 0x0, 0xb9, 0x0, 0x0,
    0x0, 0xc6, 0x0, 0x0, 0x0, 0x53, 0x0, 0x0,

    /* U+005A "Z" */
    0x7, 0xff, 0xff, 0xf6, 0x1, 0x8a, 0xac, 0xf2,
    0x0, 0x0, 0xa, 0xa0, 0x0, 0x0, 0x3e, 0x10,
    0x0, 0x0, 0xd4, 0x0, 0x0, 0x8, 0xa0, 0x0,
    0x0, 0x4e, 0x10, 0x0, 0x0, 0xd6, 0x0, 0x0,
    0x7, 0xd0, 0x0, 0x0, 0x3f, 0xa7, 0x77, 0x20,
    0x6f, 0xff, 0xff, 0x70, 0x2, 0x10, 0x0, 0x0,

    /* U+0061 "a" */
    0x7, 0xec, 0xa0, 0x29, 0x10, 0x95, 0x0, 0x16,
    0xb5, 0x5, 0x73, 0xe3, 0x29, 0x0, 0xd1, 0x83,
    0x1, 0xf0, 0x92, 0x3, 0xe0, 0x2d, 0xdb, 0xd0,
    0x0, 0x6, 0xb0, 0x0, 0x0, 0x10,

    /* U+0062 "b" */
    0x0, 0x0, 0x0, 0x0, 0x9, 0x0, 0x0, 0x0,
    0xa0, 0x0, 0x0, 0xa, 0x0, 0x0, 0x0, 0xa0,
    0x10, 0x0, 0x1d, 0xba, 0xb0, 0x4, 0xa0, 0xa,
    0x30, 0x77, 0x0, 0xc3, 0xb, 0x60, 0x3f, 0x0,
    0xe8, 0x7e, 0x90, 0xe, 0xfd, 0x60, 0x0, 0x20,
    0x0, 0x0,

    /* U+0063 "c" */
    0x1, 0xbf, 0xb2, 0xc, 0x61, 0x73, 0x6a, 0x0,
    0x0, 0xc5, 0x0, 0x0, 0xe3, 0x0, 0x0, 0xf5,
    0x0, 0x0, 0x8e, 0x53, 0x40, 0x9, 0xfd, 0x30,

    /* U+0064 "d" */
    0x0, 0x0, 0x0, 0x60, 0x0, 0x0, 0x3, 0xf0,
    0x0, 0x0, 0x6, 0xf0, 0x0, 0x0, 0x9, 0xd0,
    0x0, 0x0, 0xb, 0xb0, 0x0, 0x0, 0xc, 0xa0,
    0x0, 0x16, 0x3e, 0x80, 0x4, 0x97, 0xff, 0x80,
    0xd, 0x10, 0xe, 0x70, 0x4e, 0x0, 0xf, 0x70,
    0x8b, 0x0, 0x1f, 0x60, 0x7c, 0x0, 0x4f, 0x40,
    0xb, 0xee, 0xc6, 0x0, 0x0, 0x22, 0x0, 0x0,

    /* U+0065 "e" */
    0x0, 0x6c, 0xca, 0x10, 0x7, 0x80, 0x7, 0xd0,
    0x1b, 0x0, 0x25, 0xf4, 0x6a, 0x45, 0x45, 0x40,
    0x96, 0x0, 0x0, 0x0, 0x97, 0x0, 0x0, 0x0,
    0x4d, 0x30, 0x0, 0x0, 0x4, 0xbd, 0xeb, 0x10,

    /* U+0066 "f" */
    0x0, 0x0, 0x1, 0x71, 0x0, 0x0, 0x3, 0xa6,
    0x50, 0x0, 0x0, 0x80, 0x0, 0x0, 0x0, 0xa,
    0x0, 0x0, 0x17, 0x9b, 0xec, 0xfc, 0x0, 0x0,
    0x59, 0x1, 0x0, 0x0, 0x7, 0x80, 0x0, 0x0,
    0x0, 0x96, 0x0, 0x0, 0x0, 0xa, 0x50, 0x0,
    0x0, 0x0, 0xc4, 0x0, 0x0, 0x0, 0xd, 0x30,
    0x0, 0x0, 0x0, 0xe2, 0x0, 0x0, 0x0, 0xe,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0067 "g" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xfd, 0xf4, 0x0, 0x9f, 0xb8, 0xfe, 0x10, 0x0,
    0x8f, 0x8d, 0xfa, 0x0, 0x2, 0xfe, 0xfe, 0x70,
    0x0, 0x8, 0xe0, 0x20, 0x0, 0x0, 0x7, 0xd0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xee, 0xc1, 0x0,
    0x1, 0xcf, 0x11, 0xce, 0x0, 0xb, 0x92, 0x0,
    0x4f, 0x40, 0x6d, 0x0, 0x0, 0x8f, 0x10, 0x6e,
    0x20, 0x5, 0xc2, 0x0, 0x5, 0xcd, 0xeb, 0x10,
    0x0,

    /* U+0068 "h" */
    0x0, 0x0, 0x0, 0xb, 0x10, 0x0, 0xe, 0x0,
    0x0, 0xe, 0x0, 0x0, 0x3c, 0x0, 0x0, 0x5b,
    0x8b, 0x90, 0x6b, 0x32, 0xf3, 0x87, 0x2, 0xf3,
    0xa7, 0x2, 0xf3, 0xa6, 0x2, 0xf3, 0xc4, 0x1,
    0xb1, 0x10, 0x0, 0x0,

    /* U+0069 "i" */
    0x0, 0x0, 0x0, 0x0, 0xa8, 0x0, 0x1, 0x2,
    0x8a, 0xe7, 0x0, 0xa, 0x60, 0x0, 0xc5, 0x0,
    0xf, 0x40, 0x1, 0xf4, 0x0, 0x3f, 0x30, 0x3,
    0xe0, 0x0, 0x0, 0x0,

    /* U+006A "j" */
    0x0, 0x0, 0x0, 0x1, 0xd9, 0x0, 0x2, 0x5,
    0x89, 0xb3, 0x0, 0x9, 0x20, 0x0, 0xc0, 0x0,
    0xc, 0x0, 0x1, 0xc0, 0x0, 0x4b, 0x1, 0x7e,
    0x80, 0x4f, 0x90, 0x0, 0x0, 0x0,

    /* U+006B "k" */
    0x2, 0x0, 0x0, 0x7, 0x0, 0x0, 0x8, 0x0,
    0x0, 0x8, 0x0, 0x0, 0x9, 0x0, 0x62, 0x9,
    0xb, 0xb0, 0x28, 0xab, 0x0, 0x5d, 0xd8, 0x0,
    0x77, 0x18, 0x50, 0xa3, 0x4, 0xb0, 0xc2, 0x0,
    0xc1, 0x20, 0x0, 0x0,

    /* U+006C "l" */
    0x1, 0x10, 0x0, 0x5f, 0xff, 0x40, 0x4, 0x38,
    0x50, 0x0, 0x8, 0x30, 0x0, 0x9, 0x10, 0x0,
    0xa, 0x0, 0x0, 0xb, 0x0, 0x0, 0xa, 0x0,
    0x0, 0x19, 0x0, 0x0, 0x28, 0x0, 0x0, 0x46,
    0x0, 0x0, 0x47, 0x0, 0x0, 0xc, 0x10, 0x0,
    0x3, 0xc5, 0x0, 0x0, 0x0,

    /* U+006D "m" */
    0x0, 0x0, 0x0, 0x0, 0x56, 0x8b, 0x6, 0xb3,
    0x86, 0x7, 0xa0, 0x4a, 0x82, 0x7, 0x40, 0x2d,
    0x83, 0x8, 0x30, 0x2c, 0xa3, 0xa, 0x10, 0x3c,
    0xc2, 0xb, 0x0, 0x4a, 0xe1, 0xb, 0x0, 0x59,
    0xf0, 0x8, 0x0, 0x54, 0x60, 0x0, 0x0, 0x0,

    /* U+006E "n" */
    0x0, 0x0, 0x0, 0x0, 0xa1, 0x79, 0xb1, 0xb,
    0x80, 0x6, 0x80, 0xc0, 0x0, 0x3c, 0xd, 0x0,
    0x4, 0xb0, 0xc0, 0x0, 0x5a, 0x1b, 0x0, 0x6,
    0x83, 0xa0, 0x0, 0x87, 0x2, 0x0, 0x7, 0x30,

    /* U+006F "o" */
    0x0, 0x8d, 0xd7, 0x0, 0xa, 0x90, 0x2e, 0x70,
    0x2d, 0x0, 0x9, 0xe0, 0x7a, 0x0, 0x6, 0xf1,
    0x89, 0x0, 0x5, 0xf0, 0x6a, 0x0, 0x7, 0xd0,
    0x2e, 0x41, 0x3d, 0x60, 0x5, 0xef, 0xe5, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+0070 "p" */
    0x2, 0x16, 0x99, 0x10, 0xa, 0x81, 0x5, 0xd0,
    0xb, 0x0, 0x1, 0xf3, 0xc, 0x0, 0x5, 0xf1,
    0xc, 0x0, 0xc, 0xb0, 0x2e, 0x24, 0xcd, 0x10,
    0x5d, 0xcc, 0x70, 0x0, 0x69, 0x0, 0x0, 0x0,
    0x78, 0x0, 0x0, 0x0, 0x87, 0x0, 0x0, 0x0,
    0x43, 0x0, 0x0, 0x0,

    /* U+0071 "q" */
    0x0, 0x0, 0x0, 0x0, 0x6b, 0xc5, 0x6, 0x60,
    0x9, 0x9, 0x0, 0x9, 0x55, 0x0, 0x9, 0x68,
    0x0, 0x8, 0x7, 0x96, 0x45, 0x0, 0x0, 0x52,
    0x0, 0x0, 0x60, 0x0, 0x0, 0x60, 0x0, 0x0,
    0x60, 0x0, 0x0, 0x10,

    /* U+0072 "r" */
    0x3, 0x0, 0x0, 0x9, 0x9, 0xc6, 0xb, 0xa3,
    0x0, 0xd, 0x20, 0x0, 0xc, 0x0, 0x0, 0x2a,
    0x0, 0x0, 0x48, 0x0, 0x0, 0x46, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+0073 "s" */
    0x0, 0x3, 0x86, 0x0, 0xa, 0x81, 0x20, 0x4,
    0x90, 0x0, 0x0, 0x57, 0x0, 0x0, 0x0, 0x9c,
    0xca, 0x0, 0x0, 0x0, 0xd7, 0x0, 0x0, 0xd,
    0x81, 0x20, 0x5, 0xf3, 0x3f, 0xcc, 0xf9, 0x0,
    0x4a, 0x62, 0x0,

    /* U+0074 "t" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x70,
    0x0, 0x0, 0x0, 0x3, 0x61, 0x11, 0x10, 0x2e,
    0xff, 0xff, 0xff, 0xe1, 0x2, 0x2a, 0x31, 0x11,
    0x0, 0x0, 0xc, 0x0, 0x0, 0x0, 0x0, 0xd,
    0x0, 0x0, 0x0, 0x0, 0xc, 0x30, 0x0, 0x0,
    0x0, 0x2, 0xbd, 0xd4, 0x0,

    /* U+0075 "u" */
    0x0, 0x0, 0x2, 0x74, 0x0, 0x6, 0xa4, 0x0,
    0x6, 0xc3, 0x0, 0x6, 0xd2, 0x0, 0x35, 0xe3,
    0x1, 0x94, 0x5b, 0xba, 0x93, 0x0, 0x0, 0x31,

    /* U+0076 "v" */
    0x0, 0x0, 0x0, 0x0, 0xc, 0x0, 0x0, 0x65,
    0xc, 0x30, 0x0, 0xd0, 0x8, 0x80, 0x6, 0x80,
    0x4, 0xc0, 0xd, 0x10, 0x0, 0xf0, 0x78, 0x0,
    0x0, 0xe5, 0xd0, 0x0, 0x0, 0xce, 0x40, 0x0,
    0x0, 0x7b, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0077 "w" */
    0x1d, 0x10, 0x79, 0x0, 0xb1, 0xf, 0x42, 0xbf,
    0x5, 0xc0, 0xd, 0x7c, 0x1c, 0x6e, 0x30, 0xb,
    0xf7, 0x3, 0xf9, 0x0, 0x7, 0x90, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+0078 "x" */
    0x0, 0x0, 0x0, 0x30, 0x6, 0x0, 0x3, 0x40,
    0x9, 0x50, 0x15, 0x0, 0x1, 0xd0, 0x70, 0x0,
    0x0, 0x7c, 0x10, 0x0, 0x0, 0x5e, 0x30, 0x0,
    0x2, 0x53, 0xe2, 0x0, 0x7, 0x0, 0x8d, 0x0,
    0x60, 0x0, 0xc, 0x60, 0x0, 0x0, 0x0, 0x0,

    /* U+0079 "y" */
    0x0, 0x0, 0x0, 0x3, 0x0, 0x0, 0x70, 0x1,
    0xd0, 0x0, 0x7, 0x60, 0xc5, 0x0, 0x0, 0xc,
    0x8a, 0x0, 0x0, 0x0, 0x3e, 0x0, 0x0, 0x0,
    0xc, 0x40, 0x0, 0x0, 0x7, 0x90, 0x0, 0x0,
    0x4, 0xe1, 0x0, 0x0, 0x27, 0xf5, 0x0, 0x0,
    0xc, 0xc8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+007A "z" */
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xf7,
    0x4, 0x52, 0x25, 0xb0, 0x0, 0x0, 0x39, 0x0,
    0x0, 0x2, 0x90, 0x0, 0x0, 0x19, 0x0, 0x0,
    0x1, 0x90, 0x0, 0x0, 0x1a, 0x0, 0x0, 0x0,
    0x8c, 0xce, 0xee, 0xc0, 0x0, 0x0, 0x0, 0x0,

    /* U+2103 "℃" */
    0x0, 0x0, 0x0, 0x12, 0x0, 0x1, 0x0, 0x3,
    0xef, 0x92, 0x8d, 0xb0, 0x2f, 0xb9, 0xd4, 0x98,
    0xe0, 0xac, 0x0, 0x0, 0x6a, 0x50, 0xe2, 0x0,
    0x0, 0x0, 0x0, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0xb4, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xa8, 0xc4, 0x0, 0x0, 0x3,
    0xbb, 0x20,

    /* U+4E1C "东" */
    0x0, 0x0, 0x19, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xf3, 0x20, 0x0, 0x0, 0x0, 0xbe, 0xfe, 0x10,
    0x0, 0x28, 0xef, 0x93, 0x0, 0x0, 0x0, 0x5e,
    0x65, 0x0, 0x0, 0x0, 0x2, 0xd1, 0xcf, 0xf0,
    0x0, 0x0, 0x5d, 0xff, 0xc2, 0x0, 0x0, 0x0,
    0x45, 0xb9, 0x7, 0x20, 0x0, 0x20, 0xb, 0xa0,
    0x4f, 0x30, 0xd4, 0x1, 0xd8, 0x0, 0xeb, 0x1,
    0x0, 0x6f, 0x50, 0x4, 0x40, 0x0, 0x0, 0x60,
    0x0, 0x0,

    /* U+5317 "北" */
    0x0, 0x0, 0x0, 0x26, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x50, 0x3f, 0x10, 0x0, 0x0, 0x0, 0x0,
    0xc2, 0x6c, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc4,
    0x9a, 0x0, 0x0, 0x0, 0x1, 0x7b, 0xd4, 0xa8,
    0x1a, 0x80, 0x0, 0x6f, 0xf7, 0xe5, 0xbd, 0xeb,
    0x10, 0x0, 0xa, 0x73, 0xf5, 0xb1, 0x0, 0x1,
    0x0, 0x6, 0x55, 0xf5, 0xa1, 0x0, 0xa, 0x0,
    0x29, 0xd2, 0xf5, 0x59, 0x0, 0xa, 0xc0, 0x7b,
    0x2b, 0xf5, 0xb, 0xec, 0xdf, 0xc0, 0x0, 0x0,
    0x50, 0x0, 0x59, 0x85, 0x0,

    /* U+5357 "南" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xd2, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xb8, 0x0,
    0x0, 0x13, 0x8e, 0xe9, 0x50, 0x0, 0x0, 0x68,
    0xc4, 0x11, 0x20, 0x0, 0x0, 0x26, 0x7d, 0xfc,
    0xd2, 0x2, 0xb, 0xfa, 0xf3, 0xf, 0xc2, 0xd0,
    0x3e, 0x8a, 0x3, 0xfc, 0x2f, 0x20, 0xdf, 0xb3,
    0x6f, 0xa0, 0xd5, 0x17, 0xef, 0xdc, 0xf6, 0x4,
    0x58, 0xef, 0xcb, 0xfe, 0x0, 0x1, 0x97, 0xd9,
    0x8, 0x20, 0x0, 0x0, 0xd, 0x80, 0x0, 0x0,
    0x0, 0x0, 0xd7, 0x0, 0x0, 0x0, 0x0, 0xb,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,

    /* U+5929 "天" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x16, 0xae, 0x80, 0x0, 0x0, 0x8, 0xff, 0xc6,
    0x10, 0x0, 0x0, 0x0, 0x49, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xa8, 0xcb, 0x20, 0x2, 0x3,
    0x8e, 0xff, 0xfc, 0x70, 0x1c, 0xef, 0xff, 0xa2,
    0x0, 0x0, 0x4, 0x85, 0x8f, 0x45, 0x0, 0x0,
    0x0, 0x1, 0xfc, 0x8, 0xe6, 0x0, 0x0, 0xb,
    0xf3, 0x0, 0x9f, 0x70, 0x0, 0x9e, 0x60, 0x0,
    0xc, 0xf0, 0x4, 0x40, 0x0, 0x0, 0x2, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20,

    /* U+5DDE "州" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd7, 0x0, 0x0, 0x0, 0x81,
    0x0, 0xe5, 0x0, 0x31, 0x0, 0xf3, 0x3, 0xf0,
    0x0, 0x4b, 0x1, 0xf0, 0x44, 0xe0, 0x24, 0x3c,
    0x2, 0xf2, 0xc4, 0xd0, 0xf, 0x5b, 0x55, 0xe0,
    0x96, 0xe0, 0xa, 0x6a, 0x88, 0xf2, 0x5, 0xe0,
    0x0, 0x98, 0x4, 0xf2, 0x6, 0xf0, 0x0, 0xe4,
    0x5, 0xe0, 0x5, 0xf0, 0x2, 0xb0, 0x4, 0x80,
    0x4, 0xf2, 0x0, 0x0, 0x0, 0x3, 0x99, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x3b, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+5E95 "底" */
    0x0, 0x0, 0x5d, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x35, 0x80, 0x0, 0x0, 0x0, 0x1, 0x7d,
    0xf9, 0x0, 0x0, 0x0, 0x2b, 0xfc, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x3a, 0x0, 0x71, 0x0, 0x0,
    0x0, 0x0, 0xb0, 0xcf, 0x64, 0x10, 0x0, 0x0,
    0x5b, 0x1b, 0x6c, 0xd1, 0x0, 0x0, 0xb, 0x79,
    0x4c, 0xd0, 0x0, 0x0, 0x2, 0xf2, 0xc9, 0x5c,
    0x30, 0x0, 0x0, 0xab, 0x3d, 0x50, 0x2d, 0x21,
    0x0, 0x2f, 0x36, 0xd0, 0xb1, 0x4e, 0x88, 0x7,
    0xa0, 0x0, 0x5, 0x10, 0x2b, 0xd0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+5EA6 "度" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x44, 0x84, 0x0, 0x0, 0x0, 0x0, 0x6c, 0xfc,
    0xb0, 0x0, 0x0, 0x7, 0xef, 0xb9, 0x5f, 0xa3,
    0x0, 0x0, 0x0, 0xc1, 0xec, 0xf7, 0x0, 0x0,
    0x0, 0x5b, 0xaf, 0xdc, 0x0, 0x0, 0x0, 0xd,
    0x40, 0xce, 0xb2, 0x0, 0x0, 0x6, 0xc0, 0x5f,
    0xff, 0x70, 0x0, 0x1, 0xe3, 0xb, 0xaa, 0xe2,
    0x0, 0x0, 0x9a, 0x2, 0x4c, 0xfc, 0x40, 0x0,
    0x3e, 0x10, 0x49, 0xa3, 0x9f, 0xb0, 0xe, 0x60,
    0x0, 0x0, 0x0, 0x1b, 0x24, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+65E5 "日" */
    0x0, 0x0, 0x1, 0x0, 0x10, 0x6, 0xde, 0xd2,
    0x19, 0x4, 0x50, 0xf7, 0x9, 0x70, 0x0, 0xf5,
    0xa, 0x70, 0x0, 0xf6, 0xc, 0x95, 0xcf, 0xf6,
    0xd, 0xff, 0xfa, 0xf7, 0xe, 0xb9, 0x71, 0xf8,
    0xe, 0xd9, 0xcc, 0xf8, 0x8, 0xff, 0xce, 0xf8,
    0x0, 0x34, 0x3, 0xd4,

    /* U+661F "星" */
    0x0, 0x0, 0x16, 0xbe, 0xa0, 0x0, 0x0, 0x45,
    0x59, 0x40, 0xaa, 0x0, 0x0, 0x3e, 0x27, 0xd9,
    0xf9, 0x0, 0x0, 0xf, 0xce, 0x78, 0xf3, 0x0,
    0x0, 0xb, 0xcb, 0xcf, 0xc0, 0x0, 0x0, 0x2,
    0xef, 0x9e, 0x30, 0x0, 0x0, 0x3, 0xd3, 0xc8,
    0x91, 0x0, 0x0, 0x4c, 0x9c, 0xfb, 0x60, 0x0,
    0x5, 0xc1, 0x17, 0xf6, 0x0, 0x0, 0xb, 0x10,
    0x7d, 0xfc, 0x20, 0x0, 0x0, 0x0, 0x7e, 0xe0,
    0x1, 0x30, 0x0, 0x14, 0xaf, 0xfd, 0xff, 0xfb,
    0x0, 0x37, 0x31, 0x0, 0x0, 0x0,

    /* U+671F "期" */
    0x0, 0x0, 0x0, 0x33, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0x2, 0xc0, 0x0, 0x47, 0x0, 0x0,
    0x0, 0xd5, 0xaf, 0x44, 0xcb, 0xa9, 0x0, 0x0,
    0x2e, 0xfc, 0xa3, 0xfc, 0x8, 0x80, 0x0, 0x4,
    0xf4, 0x79, 0x4, 0x90, 0xb7, 0x0, 0x0, 0xe,
    0xcd, 0x80, 0x7d, 0xff, 0x60, 0x0, 0x0, 0xfa,
    0xc7, 0x2c, 0x9a, 0xd8, 0x0, 0x0, 0xf, 0x6d,
    0xef, 0xfd, 0xcf, 0x50, 0x0, 0x5, 0xff, 0xf9,
    0x5d, 0xb5, 0xe6, 0x0, 0x7c, 0xc7, 0xcd, 0x28,
    0xb0, 0xe, 0x60, 0x5, 0x43, 0xc3, 0x5a, 0x16,
    0x90, 0xd7, 0x0, 0x2, 0xe1, 0x0, 0x60, 0x9,
    0xbe, 0x80, 0x0, 0x1, 0x0, 0x0, 0x0, 0x7,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+6C14 "气" */
    0x0, 0x0, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3f, 0x30, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xcb,
    0xe1, 0x0, 0x0, 0x0, 0x1c, 0x69, 0x93, 0x0,
    0x0, 0x0, 0x8, 0x24, 0xbd, 0x40, 0x0, 0x0,
    0x1, 0x0, 0x68, 0x68, 0x0, 0x0, 0x0, 0x0,
    0x39, 0xd9, 0xe4, 0x0, 0x0, 0x0, 0xba, 0x40,
    0x4a, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb1, 0x0, 0x5,
    0x0, 0x0, 0x0, 0x9, 0x70, 0x0, 0x56, 0x0,
    0x0, 0x0, 0x1e, 0xa5, 0x23, 0xf2, 0x0, 0x0,
    0x0, 0x1a, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x34, 0x10,

    /* U+6E29 "温" */
    0x0, 0x0, 0x0, 0x70, 0x2, 0x60, 0x0, 0x0,
    0x4, 0xb7, 0xf, 0x4c, 0xa9, 0x80, 0x0, 0x0,
    0x3d, 0x40, 0xf3, 0x7b, 0xe7, 0x0, 0x0, 0x0,
    0x10, 0xd, 0xff, 0x8f, 0x30, 0x0, 0x0, 0x53,
    0x0, 0x79, 0xce, 0xd0, 0x0, 0x0, 0xdf, 0x60,
    0x5, 0xc7, 0x51, 0x0, 0x0, 0x0, 0x50, 0x54,
    0x11, 0x59, 0xda, 0x0, 0x0, 0x0, 0x36, 0xb9,
    0xdc, 0x53, 0xf2, 0x0, 0x0, 0x1e, 0x1a, 0x7b,
    0xb4, 0xde, 0x10, 0x0, 0x3c, 0xc0, 0x24, 0x8d,
    0xdf, 0xed, 0xb0, 0xa, 0xf8, 0xbd, 0xfc, 0xa7,
    0x42, 0x26, 0x10, 0x2d, 0x11, 0x10, 0x0, 0x0,
    0x0, 0x0,

    /* U+897F "西" */
    0x0, 0x0, 0x3a, 0xc2, 0x0, 0x0, 0x8, 0xcf,
    0xe8, 0x10, 0x0, 0x0, 0x2d, 0x72, 0x30, 0x0,
    0x0, 0x0, 0x63, 0x7b, 0xbc, 0x40, 0x30, 0x19,
    0xff, 0xc5, 0x1e, 0x3c, 0x34, 0xe9, 0xca, 0x22,
    0xf4, 0xa8, 0x1f, 0x29, 0xd2, 0x8f, 0x4, 0xb0,
    0x39, 0xb8, 0x5f, 0xa0, 0x1, 0x6f, 0xfd, 0xcf,
    0xc1, 0x0, 0x0, 0x21, 0x0, 0x30, 0x0,

    /* U+90D1 "郑" */
    0x0, 0x0, 0x61, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9, 0x90, 0x0, 0x0, 0x0, 0x10, 0x1, 0xe3,
    0x0, 0x1, 0x84, 0x1, 0xd8, 0x69, 0x0, 0x18,
    0xff, 0x60, 0x8, 0xcc, 0x62, 0xa, 0xa5, 0xa0,
    0x0, 0x7, 0xfc, 0x10, 0x75, 0x70, 0x0, 0xa,
    0xfe, 0x9c, 0x70, 0xe5, 0x71, 0x0, 0x22, 0xff,
    0xd4, 0xf, 0x3, 0xd1, 0x3, 0xcf, 0x70, 0x0,
    0xf0, 0x9, 0x77, 0xff, 0xb7, 0x30, 0xf, 0x97,
    0xc5, 0x1, 0xf3, 0x3e, 0xc2, 0xf1, 0xbb, 0x0,
    0x59, 0x0, 0x1e, 0x9f, 0x10, 0x0, 0x7, 0x0,
    0x0, 0x35, 0xf1, 0x0, 0x0, 0x10, 0x0, 0x0,
    0x1f, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1a, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+98CE "风" */
    0x0, 0x0, 0x0, 0x4a, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xc9, 0x6c, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x72, 0x8, 0x90, 0x0, 0x0, 0x0, 0xa,
    0x40, 0x0, 0xa6, 0x0, 0x0, 0x0, 0x0, 0x58,
    0x0, 0xc, 0x40, 0x0, 0x0, 0x0, 0x7, 0x70,
    0x48, 0xc3, 0x0, 0x0, 0x0, 0x0, 0xb6, 0x3a,
    0x9a, 0x40, 0x0, 0x0, 0x0, 0xf, 0x2b, 0xfa,
    0x87, 0x0, 0x20, 0x0, 0x5, 0xc3, 0xe5, 0xcf,
    0xe0, 0x1, 0x60, 0x4, 0xc7, 0xc3, 0x0, 0x69,
    0xa0, 0x6, 0x50, 0x6d, 0x20, 0x0, 0x0, 0xb,
    0xd6, 0x4e, 0x1, 0x10, 0x0, 0x0, 0x0, 0x6,
    0xcf, 0xb0,

    /* U+9AD8 "高" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0x12, 0x10, 0x0, 0x0, 0x0, 0x69, 0xcf,
    0x90, 0x0, 0x0, 0x5c, 0xfa, 0x60, 0x0, 0x0,
    0x8, 0xb8, 0x7b, 0xf1, 0x0, 0x0, 0x0, 0x5,
    0xcc, 0x70, 0x0, 0x0, 0x0, 0x3, 0xbf, 0xd2,
    0x0, 0x0, 0x0, 0x0, 0xeb, 0x69, 0xde, 0x60,
    0x2, 0x0, 0x6c, 0xe8, 0x33, 0xf4, 0xb, 0x27,
    0xa5, 0x54, 0x5, 0xf3, 0x9, 0x70, 0x8e, 0xf6,
    0xa, 0xf1, 0x4, 0x90, 0x7c, 0xe2, 0x2f, 0xb0,
    0x0, 0x20, 0xb, 0x96, 0xdf, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x53, 0x0,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x31, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x15, 0xae, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x7c, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x49, 0xef, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x4, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x69, 0xff, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xfe, 0x95, 0x0, 0x8, 0xff,
    0x0, 0x0, 0xf, 0xff, 0xc7, 0x30, 0x0, 0x0,
    0x8, 0xff, 0x0, 0x0, 0xf, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0x0, 0x0, 0xf, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0x0, 0x0,
    0xf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x0, 0x0, 0xf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x0, 0x0, 0xf, 0xf8, 0x0, 0x0,
    0x1, 0x7b, 0xbd, 0xff, 0x0, 0x0, 0xf, 0xf8,
    0x0, 0x0, 0x1e, 0xff, 0xff, 0xff, 0x0, 0x13,
    0x3f, 0xf8, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0x2b, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xfc, 0xdf, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x6, 0xef, 0xff, 0xa1, 0xff, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x2, 0x20, 0x0, 0x7f, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x8a, 0xa6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F008 "" */
    0xc4, 0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x40, 0x4c, 0xfd, 0xcd, 0xff, 0xee, 0xee, 0xee,
    0xee, 0xff, 0xdc, 0xdf, 0xfa, 0x8a, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xb8, 0xaf, 0xf4, 0x3,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x40, 0x4f,
    0xf4, 0x4, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0x50, 0x4f, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xf7, 0x47, 0xfd, 0x77,
    0x77, 0x77, 0x77, 0xdf, 0x84, 0x7f, 0xf4, 0x3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x4f,
    0xf7, 0x47, 0xfd, 0x77, 0x77, 0x77, 0x77, 0xdf,
    0x84, 0x7f, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xff, 0xf4, 0x4, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0x50, 0x4f, 0xf4, 0x3,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x40, 0x4f,
    0xfa, 0x8a, 0xfa, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xb8, 0xaf, 0xfd, 0xcd, 0xff, 0xee, 0xee, 0xee,
    0xee, 0xff, 0xdc, 0xdf, 0xc4, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x4c,

    /* U+F00B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xfa, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xfd, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xcf, 0xff, 0xf9, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xf9, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xfd, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcf, 0xff,
    0xfa, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xf9, 0xa, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xfd, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xdf, 0xff, 0xfa, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4e, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0x60, 0x6, 0xe4, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xf6, 0x0, 0x7f, 0xff, 0x40, 0x0,
    0x0, 0x4f, 0xff, 0xff, 0x60, 0x0, 0xff, 0xff,
    0xf4, 0x0, 0x4, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0x40, 0x4f, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5e,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x6, 0x70, 0x0, 0x0, 0x0, 0x18, 0x40, 0x8f,
    0xfb, 0x0, 0x0, 0x1, 0xdf, 0xf4, 0xff, 0xff,
    0xb0, 0x0, 0x1d, 0xff, 0xfb, 0x7f, 0xff, 0xfb,
    0x1, 0xdf, 0xff, 0xf4, 0x8, 0xff, 0xff, 0xbd,
    0xff, 0xff, 0x40, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x1, 0xef, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x1,
    0xdf, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x1d, 0xff,
    0xff, 0x48, 0xff, 0xff, 0xb0, 0xcf, 0xff, 0xf4,
    0x0, 0x8f, 0xff, 0xf9, 0xdf, 0xff, 0x40, 0x0,
    0x8, 0xff, 0xf9, 0x2e, 0xf4, 0x0, 0x0, 0x0,
    0x8f, 0xc0, 0x0, 0x10, 0x0, 0x0, 0x0, 0x1,
    0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0x26, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x64, 0x0,
    0xdf, 0xf4, 0x0, 0x72, 0x0, 0x0, 0x0, 0xb,
    0xfe, 0x10, 0xdf, 0xf4, 0x9, 0xfe, 0x30, 0x0,
    0x0, 0xaf, 0xff, 0x50, 0xdf, 0xf4, 0xe, 0xff,
    0xe1, 0x0, 0x5, 0xff, 0xfb, 0x0, 0xdf, 0xf4,
    0x5, 0xff, 0xfb, 0x0, 0xd, 0xff, 0xb0, 0x0,
    0xdf, 0xf4, 0x0, 0x5f, 0xff, 0x40, 0x4f, 0xff,
    0x20, 0x0, 0xdf, 0xf4, 0x0, 0xb, 0xff, 0xa0,
    0x8f, 0xfb, 0x0, 0x0, 0xdf, 0xf4, 0x0, 0x4,
    0xff, 0xf0, 0xbf, 0xf7, 0x0, 0x0, 0xdf, 0xf4,
    0x0, 0x1, 0xff, 0xf1, 0xbf, 0xf6, 0x0, 0x0,
    0xdf, 0xf4, 0x0, 0x0, 0xff, 0xf2, 0xbf, 0xf7,
    0x0, 0x0, 0x8d, 0xc1, 0x0, 0x0, 0xff, 0xf1,
    0x8f, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xf0, 0x4f, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xb0, 0xe, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0x50, 0x6, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x4, 0xff, 0xfc, 0x0,
    0x0, 0xaf, 0xff, 0xd5, 0x10, 0x3, 0x9f, 0xff,
    0xf2, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xd2, 0x0, 0x0, 0x0, 0x0,
    0x2, 0x9e, 0xff, 0xff, 0xb6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x12, 0x10, 0x0, 0x0,
    0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x0, 0x2b, 0xff, 0xff, 0xb2, 0x0, 0x10, 0x0,
    0x0, 0x8f, 0x87, 0xff, 0xff, 0xff, 0xff, 0x79,
    0xf8, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x40, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x2f, 0xff,
    0xff, 0xff, 0xc7, 0x7c, 0xff, 0xff, 0xff, 0xf2,
    0x7, 0xff, 0xff, 0xfa, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0x70, 0x0, 0x6f, 0xff, 0xf1, 0x0, 0x0,
    0x1f, 0xff, 0xf6, 0x0, 0x0, 0x7f, 0xff, 0xe0,
    0x0, 0x0, 0xe, 0xff, 0xf7, 0x0, 0x0, 0x6f,
    0xff, 0xf1, 0x0, 0x0, 0x1f, 0xff, 0xf6, 0x0,
    0x7, 0xff, 0xff, 0xfa, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0x70, 0x2f, 0xff, 0xff, 0xff, 0xc7, 0x7c,
    0xff, 0xff, 0xff, 0xf2, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x8f, 0x97, 0xff, 0xff, 0xff, 0xff, 0x78,
    0xf8, 0x0, 0x0, 0x1, 0x0, 0x1b, 0xff, 0xff,
    0xb1, 0x0, 0x10, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0x20, 0x0, 0x0,
    0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x46, 0x10, 0x0,
    0x67, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xfe, 0x20, 0xf, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xbf, 0xff, 0xff, 0x51, 0xff,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdf, 0xfc,
    0x8f, 0xff, 0x8f, 0xff, 0x10, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xfa, 0x0, 0x4e, 0xff, 0xff, 0xf1,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xf7, 0x8, 0xd3,
    0x2d, 0xff, 0xff, 0x10, 0x0, 0x0, 0xa, 0xff,
    0xf5, 0x1b, 0xff, 0xf5, 0xb, 0xff, 0xf4, 0x0,
    0x0, 0x1c, 0xff, 0xe2, 0x2d, 0xff, 0xff, 0xf7,
    0x8, 0xff, 0xf6, 0x0, 0x3e, 0xff, 0xc1, 0x4e,
    0xff, 0xff, 0xff, 0xfa, 0x5, 0xff, 0xf9, 0xe,
    0xff, 0x90, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x13, 0xef, 0xf6, 0x4f, 0x70, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x31, 0xcc, 0x0, 0x0,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0x80, 0x1, 0xef, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xf6, 0x0, 0xe,
    0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0x60, 0x0, 0xef, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xf6, 0x0, 0xe, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xff,
    0x40, 0x0, 0xcf, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x56, 0x65, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xee, 0xef, 0xff, 0xff, 0xfe, 0xee,
    0x70, 0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x7a, 0xaa, 0xaa, 0x91,
    0x4f, 0xf4, 0x19, 0xaa, 0xaa, 0xa7, 0xff, 0xff,
    0xff, 0xfd, 0x23, 0x32, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x9f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xf, 0x48, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xef, 0xff,
    0x14, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x41,

    /* U+F01C "" */
    0x0, 0x0, 0x3d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0xa, 0xff, 0x98, 0x88, 0x88, 0x88, 0x88, 0xdf,
    0xf3, 0x0, 0x0, 0x5, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0xd0, 0x0, 0x1, 0xef,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0x80, 0x0, 0xaf, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xff, 0x20, 0x5f, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfc,
    0xd, 0xff, 0x98, 0x88, 0x70, 0x0, 0x0, 0x3,
    0x88, 0x88, 0xdf, 0xf5, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0x7f,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x8e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0,
    0x7, 0xba, 0x0, 0x0, 0x1, 0x7c, 0xff, 0xff,
    0xb5, 0x0, 0xb, 0xff, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0xe5, 0xb, 0xff, 0x0, 0xa,
    0xff, 0xff, 0xdb, 0xbe, 0xff, 0xff, 0x9a, 0xff,
    0x0, 0xaf, 0xff, 0xa2, 0x0, 0x0, 0x3b, 0xff,
    0xff, 0xff, 0x5, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0xd, 0xff, 0x60, 0x0,
    0x0, 0x7, 0xba, 0x9c, 0xff, 0xff, 0x3f, 0xfc,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff,
    0x7f, 0xf6, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0, 0x0,
    0x6f, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0xcf, 0xf3, 0xff, 0xff, 0xc9, 0xaa,
    0x70, 0x0, 0x0, 0x7, 0xff, 0xd0, 0xff, 0xff,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x50,
    0xff, 0xff, 0xff, 0xb3, 0x0, 0x0, 0x3b, 0xff,
    0xf9, 0x0, 0xff, 0xa9, 0xff, 0xff, 0xeb, 0xbd,
    0xff, 0xff, 0xa0, 0x0, 0xff, 0xb0, 0x5d, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0xff, 0xb0,
    0x0, 0x5b, 0xff, 0xff, 0xc8, 0x10, 0x0, 0x0,
    0xab, 0x70, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0,
    0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0,
    0x2, 0xee, 0x0, 0x0, 0x0, 0x2e, 0xff, 0x0,
    0x0, 0x2, 0xef, 0xff, 0x47, 0x77, 0x7e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x9, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x9f, 0xff, 0x0, 0x0,
    0x0, 0x9, 0xff, 0x0, 0x0, 0x0, 0x0, 0x89,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x89, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xf0, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x73, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x1f, 0xf4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x5f, 0xcf, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x4f, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x1f, 0xf5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x85, 0x4, 0x77, 0x77, 0xef, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xee, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x10, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7b, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x10, 0x0, 0x0, 0x9, 0xfe, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xee, 0x0, 0x0, 0x0, 0x9,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x2, 0xef, 0xf0,
    0x0, 0x8, 0x70, 0x8, 0xfd, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xff, 0x0, 0x0, 0xef, 0xb0, 0xc,
    0xf6, 0x4, 0x77, 0x77, 0xef, 0xff, 0xf0, 0x0,
    0x2, 0xdf, 0x80, 0x3f, 0xd0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x74, 0x1, 0xff, 0x10, 0xcf,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x1f, 0xf4,
    0x8, 0xf7, 0x7, 0xf6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x5f, 0xc0, 0x3f, 0xa0, 0x4f, 0x9f,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xff, 0x2,
    0xfb, 0x4, 0xf9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x5f, 0xc0, 0x3f, 0xa0, 0x5f, 0x9f, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x1f, 0xf4, 0x8, 0xf7,
    0x7, 0xf6, 0xdf, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x74, 0x2, 0xff, 0x10, 0xcf, 0x30, 0x0, 0x0,
    0x9f, 0xff, 0xf0, 0x0, 0x2, 0xef, 0x80, 0x3f,
    0xd0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0x0, 0x0,
    0xef, 0xb0, 0xc, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xf0, 0x0, 0x8, 0x70, 0x8, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x89, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7b,
    0x20, 0x0, 0x0,

    /* U+F03E "" */
    0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe5, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xf6, 0x38, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x30, 0x0, 0x7f, 0xff, 0xff, 0xfb, 0xef,
    0xff, 0xff, 0xff, 0x80, 0x0, 0xcf, 0xff, 0xff,
    0x90, 0x3e, 0xff, 0xff, 0xff, 0xfa, 0x7c, 0xff,
    0xff, 0xf9, 0x0, 0x3, 0xef, 0xff, 0xff, 0xff,
    0xfc, 0xff, 0xff, 0x90, 0x0, 0x0, 0x3e, 0xff,
    0xff, 0xff, 0x90, 0x6f, 0xf9, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xf9, 0x0, 0x6, 0x90, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xc8, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x8c, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x5e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe5,

    /* U+F048 "" */
    0x47, 0x60, 0x0, 0x0, 0x0, 0x16, 0x1b, 0xff,
    0x10, 0x0, 0x0, 0x2d, 0xfb, 0xbf, 0xf1, 0x0,
    0x0, 0x2e, 0xff, 0xcb, 0xff, 0x10, 0x0, 0x3e,
    0xff, 0xfc, 0xbf, 0xf1, 0x0, 0x4f, 0xff, 0xff,
    0xcb, 0xff, 0x10, 0x5f, 0xff, 0xff, 0xfc, 0xbf,
    0xf1, 0x6f, 0xff, 0xff, 0xff, 0xcb, 0xff, 0x9f,
    0xff, 0xff, 0xff, 0xfc, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xcb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xcb,
    0xff, 0x4e, 0xff, 0xff, 0xff, 0xfc, 0xbf, 0xf1,
    0x2d, 0xff, 0xff, 0xff, 0xcb, 0xff, 0x10, 0x1c,
    0xff, 0xff, 0xfc, 0xbf, 0xf1, 0x0, 0xc, 0xff,
    0xff, 0xcb, 0xff, 0x10, 0x0, 0xb, 0xff, 0xfc,
    0xbf, 0xf1, 0x0, 0x0, 0xa, 0xff, 0xca, 0xff,
    0x10, 0x0, 0x0, 0x8, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F04B "" */
    0x6, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfd, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xfa, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xd4, 0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb2, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x50, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x20, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x20, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x50, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa2, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xd4, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xfa, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xfd, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F04C "" */
    0x6, 0x77, 0x77, 0x30, 0x0, 0x6, 0x77, 0x77,
    0x30, 0xbf, 0xff, 0xff, 0xf3, 0x0, 0xbf, 0xff,
    0xff, 0xf3, 0xff, 0xff, 0xff, 0xf7, 0x0, 0xff,
    0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xf8,
    0x0, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0xff, 0xff, 0xff, 0xf8,
    0xef, 0xff, 0xff, 0xf6, 0x0, 0xef, 0xff, 0xff,
    0xf6, 0x6f, 0xff, 0xff, 0xc1, 0x0, 0x6f, 0xff,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F04D "" */
    0x5, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x30, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F051 "" */
    0x5, 0x20, 0x0, 0x0, 0x0, 0x57, 0x66, 0xff,
    0x40, 0x0, 0x0, 0xc, 0xff, 0x8f, 0xff, 0x60,
    0x0, 0x0, 0xdf, 0xf8, 0xff, 0xff, 0x70, 0x0,
    0xd, 0xff, 0x8f, 0xff, 0xff, 0x80, 0x0, 0xdf,
    0xf8, 0xff, 0xff, 0xff, 0x90, 0xd, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xb0, 0xdf, 0xf8, 0xff, 0xff,
    0xff, 0xff, 0xcd, 0xff, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xff, 0xff, 0xff, 0xff, 0x5d, 0xff, 0x8f, 0xff,
    0xff, 0xff, 0x40, 0xdf, 0xf8, 0xff, 0xff, 0xfe,
    0x30, 0xd, 0xff, 0x8f, 0xff, 0xfe, 0x20, 0x0,
    0xdf, 0xf8, 0xff, 0xfd, 0x20, 0x0, 0xd, 0xff,
    0x7f, 0xfd, 0x10, 0x0, 0x0, 0xdf, 0xf3, 0xfb,
    0x10, 0x0, 0x0, 0xc, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xe,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x60, 0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe2, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0x80, 0x0, 0x0, 0x0, 0x5f, 0xff,
    0x40, 0x0, 0x0, 0x5f, 0xff, 0xe2, 0x0, 0x0,
    0x5f, 0xff, 0xe2, 0x0, 0x0, 0x5f, 0xff, 0xe3,
    0x0, 0x0, 0x5f, 0xff, 0xe3, 0x0, 0x0, 0x5f,
    0xff, 0xe3, 0x0, 0x0, 0x5f, 0xff, 0xe3, 0x0,
    0x0, 0xd, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xe3, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xe3,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xe3, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xe3, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xe2,
    0x0, 0x0, 0x0, 0x5f, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x5f, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F054 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xdb, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xfc, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x9, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x9, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x9, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x9, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0xd, 0xff, 0xf6, 0x0,
    0x0, 0x9, 0xff, 0xfc, 0x0, 0x0, 0x9, 0xff,
    0xfc, 0x0, 0x0, 0x9, 0xff, 0xfc, 0x0, 0x0,
    0x9, 0xff, 0xfc, 0x0, 0x0, 0x9, 0xff, 0xfc,
    0x0, 0x0, 0x9, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0xcf, 0xfc, 0x0, 0x0, 0x0, 0x2, 0xdb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x2, 0x55, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x37,
    0x77, 0x77, 0x8f, 0xff, 0xc7, 0x77, 0x77, 0x60,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x14, 0x44, 0x44, 0x5f, 0xff,
    0xb4, 0x44, 0x44, 0x30, 0x0, 0x0, 0x0, 0x2f,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x21, 0x0,
    0x0, 0x0, 0x0,

    /* U+F068 "" */
    0x49, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99, 0x99,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x2, 0x22, 0x22, 0x22,
    0x22, 0x22, 0x22, 0x22, 0x10,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x6, 0xad, 0xff, 0xec, 0x83,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xef, 0xff, 0xfc, 0x40, 0x0, 0x0, 0x0,
    0x3, 0xef, 0xff, 0x92, 0x0, 0x5, 0xdf, 0xff,
    0x90, 0x0, 0x0, 0x4, 0xff, 0xff, 0x50, 0x2,
    0x52, 0x1, 0xcf, 0xff, 0xb0, 0x0, 0x4, 0xff,
    0xff, 0x80, 0x0, 0x7f, 0xf9, 0x1, 0xef, 0xff,
    0xb0, 0x1, 0xef, 0xff, 0xf0, 0x0, 0x8, 0xff,
    0xf7, 0x8, 0xff, 0xff, 0x80, 0xaf, 0xff, 0xfb,
    0x2, 0x25, 0xff, 0xff, 0xe0, 0x3f, 0xff, 0xff,
    0x2f, 0xff, 0xff, 0xa0, 0x7f, 0xff, 0xff, 0xff,
    0x2, 0xff, 0xff, 0xf7, 0x9f, 0xff, 0xfb, 0x5,
    0xff, 0xff, 0xff, 0xc0, 0x3f, 0xff, 0xff, 0x21,
    0xef, 0xff, 0xf0, 0xc, 0xff, 0xff, 0xf5, 0x7,
    0xff, 0xff, 0x80, 0x3, 0xff, 0xff, 0x80, 0x1a,
    0xff, 0xe5, 0x1, 0xef, 0xff, 0xb0, 0x0, 0x4,
    0xff, 0xff, 0x50, 0x0, 0x10, 0x1, 0xcf, 0xff,
    0xb0, 0x0, 0x0, 0x3, 0xdf, 0xff, 0x92, 0x0,
    0x5, 0xdf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xef, 0xff, 0xfc, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xad, 0xef, 0xec,
    0x83, 0x0, 0x0, 0x0, 0x0,

    /* U+F070 "" */
    0x0, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfc, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xe4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0x70, 0x4, 0x8c, 0xef, 0xed, 0x94,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xfb,
    0xef, 0xff, 0xfe, 0xff, 0xff, 0xe6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2d, 0xff, 0xff, 0xc4, 0x0,
    0x4, 0xcf, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0x60, 0x3, 0x10, 0x9, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xf9, 0x4f, 0xfa, 0x0, 0xcf, 0xff, 0xe1, 0x0,
    0x0, 0xb, 0xb0, 0x0, 0x4e, 0xff, 0xef, 0xff,
    0xa0, 0x4f, 0xff, 0xfb, 0x0, 0x0, 0x6f, 0xfd,
    0x30, 0x1, 0xcf, 0xff, 0xff, 0xf1, 0xf, 0xff,
    0xff, 0x50, 0x0, 0xbf, 0xff, 0xf6, 0x0, 0x8,
    0xff, 0xff, 0xf3, 0xe, 0xff, 0xff, 0xa0, 0x0,
    0x6f, 0xff, 0xff, 0x0, 0x0, 0x5f, 0xff, 0xf2,
    0xf, 0xff, 0xff, 0x50, 0x0, 0xc, 0xff, 0xff,
    0x40, 0x0, 0x2, 0xdf, 0xfe, 0x8f, 0xff, 0xfa,
    0x0, 0x0, 0x1, 0xef, 0xff, 0xc0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xff,
    0xc4, 0x0, 0x0, 0x3, 0xef, 0xfd, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xef, 0xff, 0xfe, 0xe3,
    0x0, 0x1b, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5, 0x9d, 0xef, 0xec, 0x20, 0x0, 0x8f,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xef, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2c, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x60,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xa3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xa2, 0x24, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0x90, 0x1, 0xff,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0xff, 0xa0, 0x2, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xb0, 0x3, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xc0, 0x4, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xff, 0xff, 0xd0, 0x5, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xf9, 0x9c, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0xe, 0xff, 0xff, 0xff, 0xf5, 0x2b, 0xff,
    0xff, 0xff, 0xf7, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0x90, 0x1, 0xff, 0xff, 0xff, 0xfe, 0x10,
    0x1, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0xff,
    0xff, 0xff, 0xff, 0x90, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x1a, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0xb, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x1, 0x68, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x40,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0x50, 0xef, 0xff,
    0xf6, 0x0, 0x0, 0x1, 0xcf, 0xff, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0x50, 0x0, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xf7, 0x22, 0x23, 0xdf, 0xf8,
    0x9, 0xff, 0xf7, 0x2f, 0xff, 0x80, 0x0, 0x0,
    0x2e, 0xb0, 0x7f, 0xff, 0x90, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x3, 0x6, 0xff, 0xfa, 0x0, 0x7,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xfd, 0x3, 0x0, 0x7, 0x60, 0x0, 0x0, 0x0,
    0x2e, 0xff, 0xe1, 0x3f, 0x90, 0xf, 0xf8, 0x0,
    0x22, 0x23, 0xdf, 0xfe, 0x22, 0xef, 0xf7, 0x2f,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xf3, 0x0, 0xcf,
    0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0xff, 0x50,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xfe, 0xef, 0xff,
    0xf6, 0x0, 0x0, 0x1, 0xcf, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0x30, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x0, 0x94, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xae, 0xff, 0xf5, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xf9, 0x2, 0xef, 0xff, 0x50, 0x0, 0x0, 0xcf,
    0xff, 0x90, 0x0, 0x2e, 0xff, 0xf5, 0x0, 0xc,
    0xff, 0xf9, 0x0, 0x0, 0x2, 0xef, 0xff, 0x50,
    0xaf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x2e, 0xff,
    0xf2, 0x7f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xe1, 0x6, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x29, 0x20,

    /* U+F078 "" */
    0x6, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0x29,
    0x20, 0x7f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xef, 0xe1, 0x9f, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x3e, 0xff, 0xf2, 0xb, 0xff, 0xf9, 0x0, 0x0,
    0x3, 0xef, 0xff, 0x50, 0x0, 0xbf, 0xff, 0x90,
    0x0, 0x3e, 0xff, 0xf5, 0x0, 0x0, 0xb, 0xff,
    0xf9, 0x3, 0xef, 0xff, 0x50, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xae, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x94, 0x0,
    0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x9c, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xd1, 0x0, 0x58, 0x88, 0x88, 0x88, 0x88, 0x81,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xfd, 0x20, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xe2, 0x4e, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0x8f, 0xfc, 0xff, 0xcf,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xf6, 0x0,
    0x0, 0x7f, 0xc2, 0xff, 0x67, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0, 0x3, 0x1,
    0xff, 0x60, 0x30, 0x0, 0x0, 0x0, 0x0, 0x1f,
    0xf6, 0x0, 0x0, 0x0, 0x1, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf6, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf6, 0x0, 0x0, 0x0, 0x1, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x3, 0xd7, 0x1f, 0xf6,
    0x3d, 0x70, 0x0, 0x1, 0xff, 0x60, 0x0, 0x0,
    0x0, 0xa, 0xff, 0x7f, 0xf9, 0xef, 0xf0, 0x0,
    0x1, 0xff, 0xb8, 0x88, 0x88, 0x88, 0x32, 0xef,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x1, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf2, 0x2e, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xe2, 0x2, 0xef, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2e,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x30, 0x0, 0x0,

    /* U+F07B "" */
    0x5e, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb8, 0x88, 0x88, 0x88, 0x60, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x5e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xe5,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x11, 0x1b, 0xff, 0xff, 0x51, 0x11,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x7a, 0xaa, 0xaa, 0x2b,
    0xff, 0xff, 0x42, 0xaa, 0xaa, 0xa7, 0xff, 0xff,
    0xff, 0x82, 0x67, 0x76, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x77, 0x77, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xf, 0x48, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xef, 0xff,
    0x14, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x41,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0x62, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0xfe, 0xb5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x1, 0x30, 0x0, 0x0, 0x1d, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x29, 0xff, 0x70, 0x0, 0x3e, 0xff,
    0xff, 0x30, 0x0, 0x4, 0xbf, 0xff, 0xff, 0x40,
    0x7f, 0xff, 0xff, 0x50, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xfe, 0xef, 0xff, 0xff, 0x50, 0x0, 0x0,
    0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x40,
    0x0, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x10, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff,
    0xff, 0xff, 0xd5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1f, 0xff, 0xff, 0xea, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x57, 0x64, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F0C4 "" */
    0x0, 0x25, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xfe, 0x30, 0x0, 0x0, 0x4,
    0xaa, 0x50, 0x7f, 0xff, 0xff, 0xe0, 0x0, 0x0,
    0x8f, 0xff, 0xf5, 0xef, 0xd3, 0x7f, 0xf6, 0x0,
    0x8, 0xff, 0xff, 0xb0, 0xff, 0x80, 0xf, 0xf7,
    0x0, 0x8f, 0xff, 0xfb, 0x0, 0xdf, 0xe7, 0xaf,
    0xf5, 0x8, 0xff, 0xff, 0xb0, 0x0, 0x5f, 0xff,
    0xff, 0xfd, 0x9f, 0xff, 0xfb, 0x0, 0x0, 0x5,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x1, 0x5f, 0xff, 0xff, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x25, 0x9f, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xfb,
    0x6f, 0xff, 0xfd, 0x10, 0x0, 0xef, 0xd3, 0x7f,
    0xf5, 0x5, 0xff, 0xff, 0xd1, 0x0, 0xff, 0x80,
    0xf, 0xf7, 0x0, 0x5f, 0xff, 0xfd, 0x10, 0xdf,
    0xe7, 0xaf, 0xf5, 0x0, 0x5, 0xff, 0xff, 0xd1,
    0x5f, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x4f, 0xff,
    0xf4, 0x5, 0xef, 0xfb, 0x10, 0x0, 0x0, 0x1,
    0x66, 0x20, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x8, 0xbb, 0xbb, 0xbb, 0x50, 0x90,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0x81,
    0xfb, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0x81, 0xff, 0xb0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0x81, 0xff, 0xf8, 0x8c, 0xc9, 0xf, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0xff, 0xfc, 0xf,
    0xff, 0xff, 0xff, 0xd5, 0x44, 0x43, 0xff, 0xfc,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xfc, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xfc, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0xff, 0xfc, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0xff, 0xfc, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0xff, 0xfc, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0xff, 0xfc, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xfc, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xfc,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xfc, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xff, 0xfe, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0xff, 0xff, 0x91, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x58, 0x88, 0x88, 0x88,
    0x88, 0x87, 0x10, 0x0, 0x0,

    /* U+F0C7 "" */
    0x6, 0x77, 0x77, 0x77, 0x77, 0x77, 0x60, 0x0,
    0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc1, 0x0, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xfc, 0x10, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xc0, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xf3, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xf4, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xf4,
    0xff, 0xd8, 0x88, 0x88, 0x88, 0x88, 0xef, 0xff,
    0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0xe4, 0x2,
    0xcf, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x2f, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff,
    0x40, 0x0, 0xf, 0xff, 0xff, 0xf4, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x6f, 0xff, 0xff, 0xf4, 0xff,
    0xff, 0xff, 0xfc, 0x8a, 0xff, 0xff, 0xff, 0xf4,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf3, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F0E7 "" */
    0x0, 0x14, 0x44, 0x44, 0x41, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xef,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xff, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x2, 0xff, 0xff, 0xff,
    0xd0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x20, 0xb, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xe1, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x0, 0xb, 0xff, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0x40,
    0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x6, 0x71, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x14, 0x44, 0xbf, 0xfe, 0x44, 0x43, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xf9, 0x4f, 0xff, 0xff,
    0x40, 0x0, 0x0, 0xff, 0xff, 0xf8, 0x3f, 0xff,
    0xff, 0x50, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xa8, 0x88, 0x88, 0x20, 0x0, 0x0, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xf0, 0xcf, 0xff, 0xff, 0x51, 0xe2, 0x0,
    0xff, 0xff, 0xf0, 0xef, 0xff, 0xff, 0x51, 0xfe,
    0x20, 0xff, 0xff, 0xf0, 0xef, 0xff, 0xff, 0x51,
    0xff, 0xe2, 0xff, 0xff, 0xf0, 0xef, 0xff, 0xff,
    0x50, 0xbb, 0xb7, 0xff, 0xff, 0xf0, 0xef, 0xff,
    0xff, 0x80, 0x0, 0x0, 0xff, 0xff, 0xf0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff, 0xf0,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff, 0xff,
    0xf0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xfa, 0xff,
    0xff, 0xf0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0xcf, 0xff, 0xf0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xfa, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x7b,
    0xbb, 0xbb, 0xbb, 0xbb, 0xb4,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x0, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xfd, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xef, 0xff,
    0xfb, 0x20, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xf3, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x10, 0x0, 0x0, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x0, 0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf5, 0x0, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x9, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x9f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x75, 0x0, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xff,
    0xc8, 0x8f, 0xa8, 0xaf, 0x88, 0xbf, 0x88, 0xfb,
    0x88, 0xff, 0x8f, 0xf8, 0x0, 0xf4, 0x4, 0xf0,
    0x5, 0xe0, 0xe, 0x50, 0xf, 0xf8, 0xff, 0x80,
    0xf, 0x40, 0x4f, 0x0, 0x6f, 0x0, 0xf6, 0x0,
    0xff, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xff, 0xff, 0x94,
    0x6f, 0x64, 0x8f, 0x44, 0xbb, 0x44, 0xff, 0xff,
    0x8f, 0xff, 0xf6, 0x2, 0xf2, 0x5, 0xf0, 0x8,
    0x80, 0xe, 0xff, 0xf8, 0xff, 0xff, 0x94, 0x6f,
    0x64, 0x8f, 0x44, 0xbb, 0x44, 0xff, 0xff, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xff, 0x80, 0xf, 0x40, 0x0,
    0x0, 0x0, 0x0, 0xf6, 0x0, 0xff, 0x8f, 0xf8,
    0x0, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xe, 0x50,
    0xf, 0xf8, 0xff, 0xc8, 0x8f, 0xa8, 0x88, 0x88,
    0x88, 0x88, 0xfb, 0x88, 0xff, 0x8e, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x5e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x56, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x7e, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x8f, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xaf,
    0xff, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xbf, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x5, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0, 0x6, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x18, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd0, 0x0,
    0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x0, 0xe, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0xd, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0x0, 0x2, 0xac, 0xcc, 0xcc, 0xcd, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xff, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x27, 0x30, 0x0, 0x0, 0x0, 0x0,

    /* U+F15B "" */
    0x24, 0x44, 0x44, 0x44, 0x30, 0x30, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xfc, 0xf, 0x60, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0xff, 0x60, 0xf, 0xff,
    0xff, 0xff, 0xfc, 0xf, 0xff, 0x60, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0xff, 0xff, 0x6f, 0xff, 0xff,
    0xff, 0xfc, 0xb, 0xbb, 0xbb, 0xff, 0xff, 0xff,
    0xff, 0xe1, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8b, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x80,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x23, 0x43, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x8c, 0xff, 0xff, 0xff, 0xfc, 0x83, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6d, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x60, 0x0, 0x0, 0x3, 0xdf,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff,
    0xd3, 0x0, 0x8, 0xff, 0xff, 0xfb, 0x72, 0x0,
    0x0, 0x2, 0x7b, 0xff, 0xff, 0xf8, 0xa, 0xff,
    0xff, 0xa2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xaf, 0xff, 0xfa, 0xbf, 0xfd, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3d, 0xff, 0xb0,
    0xba, 0x10, 0x0, 0x5, 0x9d, 0xef, 0xed, 0x95,
    0x0, 0x0, 0x1a, 0xb0, 0x0, 0x0, 0x0, 0x6d,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xfa, 0x53, 0x23, 0x5a, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xb1, 0x0, 0x0,
    0x0, 0x1, 0xbf, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x9d, 0x91, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xef, 0xe3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F240 "" */
    0x17, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x60, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfd, 0xff, 0x84, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xc, 0xff, 0xff, 0xf8,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x5b, 0xff, 0xff, 0x84, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x8f, 0xff,
    0xf8, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x8, 0xff, 0xff, 0x84, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xc, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xc8, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8e,
    0xfd, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0,

    /* U+F241 "" */
    0x17, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x60, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfd, 0xff, 0x81, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x0, 0xc, 0xff, 0xff, 0xf8,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x5b, 0xff, 0xff, 0x81, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x8f, 0xff,
    0xf8, 0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x81, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0xc, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xc8, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8e,
    0xfd, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0,

    /* U+F242 "" */
    0x17, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x60, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfd, 0xff, 0x81, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf8,
    0x1f, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x5b, 0xff, 0xff, 0x81, 0xff, 0xff, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xf8, 0x1f, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x81, 0xff, 0xff,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xc8, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8e,
    0xfd, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0,

    /* U+F243 "" */
    0x17, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x60, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfd, 0xff, 0x81, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf8,
    0x1f, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5b, 0xff, 0xff, 0x81, 0xff, 0xff, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xf8, 0x1f, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x81, 0xff, 0xff,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xc8, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8e,
    0xfd, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0,

    /* U+F244 "" */
    0x17, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x60, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0xf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xfd, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5b, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xc8, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x8e,
    0xfd, 0x4e, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x4e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc2, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7c,
    0xdf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xfb, 0xbf, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0x70, 0xa, 0xfc, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x32, 0x0, 0x0, 0x9e, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff,
    0x90, 0x1, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1c, 0x30, 0x0, 0xcf, 0xff, 0xf6, 0x3c, 0xf3,
    0x33, 0x33, 0x33, 0x33, 0x33, 0x5f, 0xf9, 0x10,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0xcf, 0xff, 0xf6,
    0x33, 0x34, 0xed, 0x33, 0x33, 0x33, 0x33, 0x5f,
    0xfa, 0x10, 0x2d, 0xff, 0x90, 0x0, 0x0, 0x5f,
    0x30, 0x0, 0x0, 0x0, 0x1c, 0x30, 0x0, 0x0,
    0x32, 0x0, 0x0, 0x0, 0xd, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xf3, 0xa, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xce,
    0xae, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xbe, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x22, 0x20, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x0, 0x34, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xbf, 0xff, 0xff, 0xe7, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xfa, 0xff, 0xff, 0xb0, 0x0,
    0x4, 0xff, 0xff, 0xf1, 0x9f, 0xff, 0xf8, 0x0,
    0xd, 0xff, 0xff, 0xf1, 0xa, 0xff, 0xff, 0x10,
    0x3f, 0xff, 0xff, 0xf1, 0x0, 0xbf, 0xff, 0x60,
    0x7f, 0xfd, 0x8f, 0xf1, 0x66, 0xc, 0xff, 0xa0,
    0xaf, 0xf8, 0x7, 0xf1, 0x6f, 0x13, 0xff, 0xd0,
    0xcf, 0xff, 0x70, 0x71, 0x53, 0x1e, 0xff, 0xf0,
    0xdf, 0xff, 0xf7, 0x0, 0x1, 0xdf, 0xff, 0xf0,
    0xef, 0xff, 0xff, 0x60, 0xc, 0xff, 0xff, 0xf0,
    0xef, 0xff, 0xff, 0x30, 0x7, 0xff, 0xff, 0xf0,
    0xdf, 0xff, 0xf3, 0x0, 0x10, 0x8f, 0xff, 0xf0,
    0xcf, 0xff, 0x30, 0xb1, 0x67, 0x9, 0xff, 0xf0,
    0x9f, 0xf6, 0xb, 0xf2, 0x6e, 0x2, 0xff, 0xd0,
    0x6f, 0xff, 0xcf, 0xf2, 0x52, 0x2e, 0xff, 0xa0,
    0x1f, 0xff, 0xff, 0xf2, 0x2, 0xef, 0xff, 0x50,
    0x9, 0xff, 0xff, 0xf2, 0x2e, 0xff, 0xfe, 0x0,
    0x0, 0xdf, 0xff, 0xf4, 0xef, 0xff, 0xf5, 0x0,
    0x0, 0x1a, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x27, 0xab, 0xb9, 0x50, 0x0, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x2, 0xab, 0xbb, 0xb7, 0x0, 0x0,
    0x0, 0x57, 0x77, 0x7c, 0xff, 0xff, 0xff, 0x77,
    0x77, 0x72, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x88, 0x88, 0x20, 0xc, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0xc, 0xff, 0x77, 0xff, 0x3b, 0xfe, 0x1e, 0xff,
    0x40, 0xc, 0xff, 0x66, 0xff, 0x2a, 0xfe, 0xe,
    0xff, 0x40, 0xc, 0xff, 0x66, 0xff, 0x2a, 0xfe,
    0xe, 0xff, 0x40, 0xc, 0xff, 0x66, 0xff, 0x2a,
    0xfe, 0xe, 0xff, 0x40, 0xc, 0xff, 0x66, 0xff,
    0x2a, 0xfe, 0xe, 0xff, 0x40, 0xc, 0xff, 0x66,
    0xff, 0x2a, 0xfe, 0xe, 0xff, 0x40, 0xc, 0xff,
    0x66, 0xff, 0x2a, 0xfe, 0xe, 0xff, 0x40, 0xc,
    0xff, 0x66, 0xff, 0x2a, 0xfe, 0xe, 0xff, 0x40,
    0xc, 0xff, 0x66, 0xff, 0x2a, 0xfe, 0xe, 0xff,
    0x40, 0xc, 0xff, 0x77, 0xff, 0x3b, 0xfe, 0x1e,
    0xff, 0x40, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x7, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x57, 0x88, 0x88,
    0x88, 0x88, 0x88, 0x72, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0x64, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xdf, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0x90, 0x8f, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xb0, 0x8f, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xb0, 0x8f,
    0xfe, 0x10, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0xff, 0xb0, 0x8e, 0x20, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xff, 0xff, 0xff, 0xb0, 0x10, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xfe, 0x10,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x2, 0xef, 0xff,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x2, 0xef, 0xff, 0xff, 0xff, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x1, 0xef, 0xff, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xff, 0xfe, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0x75, 0x31, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x0, 0x6e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x20, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0x85, 0xff, 0xff, 0x58, 0xff,
    0xff, 0xff, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xb0,
    0x4, 0xff, 0x40, 0xb, 0xff, 0xff, 0xf0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x4, 0x40, 0x4,
    0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0,
    0xef, 0xff, 0xff, 0xff, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x40, 0x0, 0x4, 0xff, 0xff, 0xff,
    0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0x40, 0x4,
    0x40, 0x4, 0xff, 0xff, 0xff, 0x0, 0x8f, 0xff,
    0xff, 0xff, 0xb0, 0x4, 0xff, 0x40, 0xb, 0xff,
    0xff, 0xf0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0x85,
    0xff, 0xff, 0x58, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x6e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfc, 0x20,

    /* U+F7C2 "" */
    0x0, 0x0, 0x28, 0x88, 0x88, 0x88, 0x73, 0x0,
    0x0, 0x2e, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x1d,
    0xf6, 0xe, 0x50, 0xd6, 0x8, 0xff, 0x1d, 0xff,
    0x60, 0xe5, 0xd, 0x60, 0x8f, 0xfc, 0xff, 0xf6,
    0xe, 0x50, 0xd6, 0x8, 0xff, 0xff, 0xff, 0x60,
    0xe5, 0xd, 0x60, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x7, 0xab,
    0xbb, 0xbb, 0xbb, 0xbb, 0xa6, 0x0,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xf1, 0x0, 0x0, 0x1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0x10,
    0x0, 0xa, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xf1, 0x0, 0xb, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0x10, 0xc, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf1, 0x1d,
    0xff, 0xff, 0xcb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbf,
    0xff, 0x1c, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x9f,
    0xff, 0xf9, 0x66, 0x66, 0x66, 0x66, 0x66, 0x66,
    0x40, 0x0, 0x8f, 0xff, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5c, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+FF08 "（" */
    0x0, 0x0, 0x0, 0x0, 0x6, 0xc1, 0x0, 0x9c,
    0x10, 0x5, 0xf5, 0x0, 0xd, 0xf1, 0x0, 0x1f,
    0xc0, 0x0, 0x2f, 0xb0, 0x0, 0x3f, 0xc0, 0x0,
    0x2f, 0xe0, 0x0, 0xf, 0xf0, 0x0, 0xe, 0xf4,
    0x0, 0x9, 0xfa, 0x0, 0x1, 0xef, 0x50, 0x0,
    0x3d, 0xf5, 0x0, 0x0, 0x52,

    /* U+FF09 "）" */
    0x5, 0x60, 0x0, 0x0, 0xcd, 0x20, 0x0, 0x1e,
    0xb0, 0x0, 0x9, 0xf6, 0x0, 0x6, 0xfc, 0x0,
    0x7, 0xfe, 0x0, 0x8, 0xfd, 0x0, 0xa, 0xf9,
    0x0, 0xc, 0xf6, 0x0, 0xd, 0xf5, 0x0, 0x2f,
    0xf2, 0x1, 0xdf, 0xb0, 0xc, 0xfe, 0x10, 0x2,
    0x82, 0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 192, .box_w = 4, .box_h = 15, .ofs_x = 4, .ofs_y = -2},
    {.bitmap_index = 30, .adv_w = 192, .box_w = 5, .box_h = 6, .ofs_x = 3, .ofs_y = 9},
    {.bitmap_index = 45, .adv_w = 192, .box_w = 9, .box_h = 3, .ofs_x = 2, .ofs_y = 4},
    {.bitmap_index = 59, .adv_w = 192, .box_w = 4, .box_h = 3, .ofs_x = 4, .ofs_y = 0},
    {.bitmap_index = 65, .adv_w = 144, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 101, .adv_w = 144, .box_w = 5, .box_h = 11, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 129, .adv_w = 144, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 164, .adv_w = 144, .box_w = 6, .box_h = 10, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 194, .adv_w = 144, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 238, .adv_w = 144, .box_w = 6, .box_h = 11, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 271, .adv_w = 144, .box_w = 6, .box_h = 10, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 301, .adv_w = 144, .box_w = 6, .box_h = 10, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 331, .adv_w = 144, .box_w = 6, .box_h = 10, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 361, .adv_w = 144, .box_w = 5, .box_h = 10, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 386, .adv_w = 192, .box_w = 6, .box_h = 15, .ofs_x = 3, .ofs_y = -3},
    {.bitmap_index = 431, .adv_w = 144, .box_w = 9, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 490, .adv_w = 144, .box_w = 8, .box_h = 13, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 542, .adv_w = 144, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 586, .adv_w = 144, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 634, .adv_w = 144, .box_w = 8, .box_h = 13, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 686, .adv_w = 144, .box_w = 8, .box_h = 13, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 738, .adv_w = 144, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 786, .adv_w = 144, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 834, .adv_w = 144, .box_w = 8, .box_h = 13, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 886, .adv_w = 144, .box_w = 7, .box_h = 11, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 925, .adv_w = 144, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 973, .adv_w = 144, .box_w = 7, .box_h = 12, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1015, .adv_w = 144, .box_w = 9, .box_h = 12, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 1069, .adv_w = 144, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1117, .adv_w = 144, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1165, .adv_w = 144, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1213, .adv_w = 144, .box_w = 8, .box_h = 13, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 1265, .adv_w = 144, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1313, .adv_w = 144, .box_w = 7, .box_h = 13, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1359, .adv_w = 144, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1413, .adv_w = 144, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1461, .adv_w = 144, .box_w = 9, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1515, .adv_w = 144, .box_w = 10, .box_h = 11, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1570, .adv_w = 144, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1618, .adv_w = 144, .box_w = 8, .box_h = 12, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1666, .adv_w = 144, .box_w = 8, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1714, .adv_w = 144, .box_w = 6, .box_h = 10, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 1744, .adv_w = 144, .box_w = 7, .box_h = 12, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1786, .adv_w = 144, .box_w = 6, .box_h = 8, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 1810, .adv_w = 144, .box_w = 8, .box_h = 14, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1866, .adv_w = 144, .box_w = 8, .box_h = 8, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1898, .adv_w = 144, .box_w = 9, .box_h = 14, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 1961, .adv_w = 144, .box_w = 10, .box_h = 13, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 2026, .adv_w = 144, .box_w = 6, .box_h = 12, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 2062, .adv_w = 144, .box_w = 5, .box_h = 11, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2090, .adv_w = 144, .box_w = 5, .box_h = 12, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 2120, .adv_w = 144, .box_w = 6, .box_h = 12, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 2156, .adv_w = 144, .box_w = 6, .box_h = 15, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 2201, .adv_w = 144, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2241, .adv_w = 144, .box_w = 7, .box_h = 9, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2273, .adv_w = 144, .box_w = 8, .box_h = 9, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2309, .adv_w = 144, .box_w = 8, .box_h = 11, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 2353, .adv_w = 144, .box_w = 6, .box_h = 12, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 2389, .adv_w = 144, .box_w = 6, .box_h = 9, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 2416, .adv_w = 144, .box_w = 7, .box_h = 10, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2451, .adv_w = 144, .box_w = 10, .box_h = 9, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2496, .adv_w = 144, .box_w = 6, .box_h = 8, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 2520, .adv_w = 144, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2560, .adv_w = 144, .box_w = 10, .box_h = 6, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2590, .adv_w = 144, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2630, .adv_w = 144, .box_w = 9, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2680, .adv_w = 144, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2720, .adv_w = 192, .box_w = 10, .box_h = 10, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2770, .adv_w = 286, .box_w = 11, .box_h = 12, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 2836, .adv_w = 286, .box_w = 14, .box_h = 11, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 2913, .adv_w = 286, .box_w = 11, .box_h = 16, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 3001, .adv_w = 286, .box_w = 12, .box_h = 13, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 3079, .adv_w = 286, .box_w = 12, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3163, .adv_w = 286, .box_w = 13, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3248, .adv_w = 286, .box_w = 13, .box_h = 15, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 3346, .adv_w = 286, .box_w = 8, .box_h = 11, .ofs_x = 3, .ofs_y = -1},
    {.bitmap_index = 3390, .adv_w = 286, .box_w = 12, .box_h = 13, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 3468, .adv_w = 286, .box_w = 15, .box_h = 14, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 3573, .adv_w = 286, .box_w = 13, .box_h = 14, .ofs_x = 2, .ofs_y = -3},
    {.bitmap_index = 3664, .adv_w = 286, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 3754, .adv_w = 286, .box_w = 11, .box_h = 10, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 3809, .adv_w = 286, .box_w = 13, .box_h = 20, .ofs_x = 1, .ofs_y = -8},
    {.bitmap_index = 3939, .adv_w = 286, .box_w = 15, .box_h = 12, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4029, .adv_w = 286, .box_w = 12, .box_h = 14, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 4113, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 4323, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4473, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 4663, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4813, .adv_w = 220, .box_w = 14, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 4918, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5128, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5338, .adv_w = 360, .box_w = 23, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 5557, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 5767, .adv_w = 360, .box_w = 23, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5940, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 6150, .adv_w = 160, .box_w = 10, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6230, .adv_w = 240, .box_w = 15, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6350, .adv_w = 360, .box_w = 23, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 6569, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6719, .adv_w = 280, .box_w = 13, .box_h = 19, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 6843, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 7032, .adv_w = 280, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7203, .adv_w = 280, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 7374, .adv_w = 280, .box_w = 13, .box_h = 19, .ofs_x = 2, .ofs_y = -2},
    {.bitmap_index = 7498, .adv_w = 280, .box_w = 19, .box_h = 19, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 7679, .adv_w = 200, .box_w = 11, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 7784, .adv_w = 200, .box_w = 11, .box_h = 19, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 7889, .adv_w = 280, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8060, .adv_w = 280, .box_w = 18, .box_h = 5, .ofs_x = 0, .ofs_y = 5},
    {.bitmap_index = 8105, .adv_w = 360, .box_w = 23, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8278, .adv_w = 400, .box_w = 26, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 8551, .adv_w = 360, .box_w = 24, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 8803, .adv_w = 320, .box_w = 20, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 8993, .adv_w = 280, .box_w = 18, .box_h = 11, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 9092, .adv_w = 280, .box_w = 18, .box_h = 11, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 9191, .adv_w = 400, .box_w = 26, .box_h = 16, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 9399, .adv_w = 320, .box_w = 20, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9549, .adv_w = 320, .box_w = 20, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9759, .adv_w = 320, .box_w = 21, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 9980, .adv_w = 280, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10151, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10340, .adv_w = 280, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10511, .adv_w = 200, .box_w = 14, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 10658, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10847, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11036, .adv_w = 360, .box_w = 23, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 11209, .adv_w = 320, .box_w = 22, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 11440, .adv_w = 240, .box_w = 15, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11598, .adv_w = 400, .box_w = 25, .box_h = 19, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11836, .adv_w = 400, .box_w = 25, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 11999, .adv_w = 400, .box_w = 25, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 12162, .adv_w = 400, .box_w = 25, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 12325, .adv_w = 400, .box_w = 25, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 12488, .adv_w = 400, .box_w = 25, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 12651, .adv_w = 400, .box_w = 26, .box_h = 17, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 12872, .adv_w = 280, .box_w = 16, .box_h = 21, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 13040, .adv_w = 280, .box_w = 18, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 13229, .adv_w = 320, .box_w = 21, .box_h = 21, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 13450, .adv_w = 400, .box_w = 25, .box_h = 15, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13638, .adv_w = 240, .box_w = 15, .box_h = 21, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 13796, .adv_w = 322, .box_w = 21, .box_h = 13, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 13933, .adv_w = 192, .box_w = 6, .box_h = 15, .ofs_x = 2, .ofs_y = -3},
    {.bitmap_index = 13978, .adv_w = 192, .box_w = 6, .box_h = 14, .ofs_x = 3, .ofs_y = -2}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_0[] = {
    0x0, 0x1, 0xc, 0xd, 0xf, 0x10, 0x11, 0x12,
    0x13, 0x14, 0x15, 0x16, 0x17, 0x18, 0x1e
};

static const uint16_t unicode_list_3[] = {
    0x0, 0x2d19, 0x3214, 0x3254, 0x3826, 0x3cdb, 0x3d92, 0x3da3,
    0x44e2, 0x451c, 0x461c, 0x4b11, 0x4d26, 0x687c, 0x6fce, 0x77cb,
    0x79d5, 0xcefe, 0xcf05, 0xcf08, 0xcf09, 0xcf0a, 0xcf0e, 0xcf10,
    0xcf12, 0xcf16, 0xcf19, 0xcf1e, 0xcf23, 0xcf24, 0xcf25, 0xcf3b,
    0xcf45, 0xcf48, 0xcf49, 0xcf4a, 0xcf4e, 0xcf4f, 0xcf50, 0xcf51,
    0xcf64, 0xcf65, 0xcf6b, 0xcf6d, 0xcf6e, 0xcf71, 0xcf74, 0xcf75,
    0xcf76, 0xcf78, 0xcf90, 0xcf92, 0xcfc1, 0xcfc2, 0xcfc4, 0xcfe4,
    0xcfe7, 0xcff0, 0xd019, 0xd021, 0xd058, 0xd0e8, 0xd13d, 0xd13e,
    0xd13f, 0xd140, 0xd141, 0xd184, 0xd190, 0xd1ea, 0xd201, 0xd457,
    0xd6bf, 0xd79f, 0xde05, 0xde06
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 33, .range_length = 31, .glyph_id_start = 1,
        .unicode_list = unicode_list_0, .glyph_id_ofs_list = NULL, .list_length = 15, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    },
    {
        .range_start = 65, .range_length = 26, .glyph_id_start = 16,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 97, .range_length = 26, .glyph_id_start = 42,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 8451, .range_length = 56839, .glyph_id_start = 68,
        .unicode_list = unicode_list_3, .glyph_id_ofs_list = NULL, .list_length = 76, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LV_VERSION_CHECK(8, 0, 0)
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 4,
    .bpp = 4,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LV_VERSION_CHECK(8, 0, 0)
    .cache = &cache
#endif
};


/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LV_VERSION_CHECK(8, 0, 0)
const lv_font_t lv_font_Slideyouran_Regular_20 = {
#else
lv_font_t lv_font_Slideyouran_Regular_20 = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 26,          /*The maximum line height required by the font*/
    .base_line = 8,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};



#endif /*#if LV_FONT_SLIDEYOURAN_REGULAR_20*/

