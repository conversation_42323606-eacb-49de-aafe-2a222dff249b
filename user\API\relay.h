#ifndef __RELAY_H_
#define __RELAY_H_

#include "stm32f10x.h"
#include "main.h"

#if (USB_STD_LIB==0)
#define RELAY_ON()    	GPIOA->ODR |= (0x01<<11) 
#define RELAY_OFF()    	GPIOA->ODR &= ~(0x01<<11) 
#elif (USB_STD_LIB==1)
#define RELAY_ON()    	GPIO_SetBits(GPIOA,GPIO_Pin_11)
#define RELAY_OFF()    	GPIO_ResetBits(GPIOA,GPIO_Pin_11)
#define RELAY_TOGGLE()  GPIO_WriteBit(GPIOA,GPIO_Pin_11,(BitAction)(1-GPIO_ReadOutputDataBit(GPIOA,GPIO_Pin_11)))
#endif
void RELAY_Config(void);

#endif
