#include "main.h"
/* BaseType_t xTaskCreate( TaskFunction_t pvTaskCode,  //任务入口函数
                         const char * const pcName,    //任务名字
                         const configSTACK_DEPTH_TYPE uxStackDepth,//分配给任务堆栈空间  所用空间等于字节数*深度
                         void *pvParameters, //作为参数传递给所创建任务的值
                         UBaseType_t uxPriority, //优先级
                         TaskHandle_t *pxCreatedTask //用于将句柄传递至由 xTaskCreate() 函数创建的任务。pxCreatedTask 是可选参数， 可设置为 NULL。句柄就是类似于身份证
                       ); 创建完任务要启用调度vTaskStartScheduler();
*/
TaskHandle_t vSpace=NULL;
TaskHandle_t vLcd=NULL;
TaskHandle_t vKqm_TaskHandle_t=NULL;
TaskHandle_t vKey_TaskHandle_t=NULL;
TaskHandle_t vBH1750_TaskHandle_t=NULL;
TaskHandle_t Control_TaskHandle_t=NULL;
TaskHandle_t vSu03_TaskHandle_t=NULL;
TaskHandle_t Cloud_TaskHandle_t=NULL;
TaskHandle_t xLvglTaskHandle = NULL;   //接口的适配23
SemaphoreHandle_t vKqm_SemaphoreHandle_t=NULL;
SemaphoreHandle_t vESP8266_SemaphoreHandle_t=NULL;
TimerHandle_t xTimer1 = NULL;
uint16_t Senor_speed=500;

int main(void)
{
	IIC_Init();
	Dma_Init();
	LED_Config();
	UART1_Config();
	KEY_Config();
	BEEP_Config();
	KQM_Config();
	BH1750_Config();
	DHT11_Config();
	ADC_Config();
	RTC_Config();
	RELAY_Config();
	PWM_Config();
	SU03T_Config();
	ESP8266_Config();
	TIME6_Config();
//	PARA_Init();
	lv_init();      //LVGL的初始化   接口的适配18
	lv_port_disp_init();  //LVGL显示设备的初始化  接口的适配19
	vKqm_SemaphoreHandle_t=xSemaphoreCreateBinary();
	BaseType_t xReturn=NULL;
	xReturn=xTaskCreate(vLED_TaskCode,"LED",128,NULL,1,&vLcd);	if(xReturn!=NULL) printf("LED任务已经创建\r\n");
	xReturn=xTaskCreate(vSpace_TaskCode,"Space",256,NULL,1,&vSpace);if(xReturn!=NULL) printf("Space任务已经创建\r\n");
	xReturn=xTaskCreate(vKqm_TaskCode,"KQM",128,NULL,2,&vKqm_TaskHandle_t);if(xReturn!=NULL) printf("KQm任务已经创建\r\n");
	xReturn=xTaskCreate(vKey_TaskCode,"Key",128,NULL,4,&vKey_TaskHandle_t);if(xReturn!=NULL) printf("Key任务已经创建\r\n");
	xReturn=xTaskCreate(vSenor_TaskCode,"Senor",128,NULL,2,&vBH1750_TaskHandle_t);if(xReturn!=NULL) printf("Senor任务已经创建\r\n");
	xReturn=xTaskCreate(vControl_TaskCode,"Control",128,NULL,3,&Control_TaskHandle_t);if(xReturn!=NULL) printf("Control任务已经创建\r\n");
	xReturn=xTaskCreate(vSU03_TaskCode,"Control",128,NULL,3,&vSu03_TaskHandle_t);if(xReturn!=NULL) printf("SU035任务已经创建\r\n");
	xReturn=xTaskCreate(vCloud_TaskCode,"ESP8266",512,NULL,7,&Cloud_TaskHandle_t);if(xReturn!=NULL) printf("Cloud任务已经创建\r\n");
	xReturn=xTaskCreate(vLvglTaskFunction,"LVGL",512,NULL,6,&xLvglTaskHandle);if(xReturn!=NULL) printf("Cloud任务已经创建\r\n");
	xTimer1=xTimerCreate("Time",1000,pdTRUE,(void *) 1,xTime_CALLBACKFUNCTION);if(xTimer1!=NULL) printf("软件定时器任务创建\r\n");
	xTimerStart(xTimer1,0);
	vTaskStartScheduler();
}

void vApplicationStackOverflowHook(void)
{
	while(1)
	{
		
	}
}
void vLED_TaskCode(void * pvParameters)
{
	while(1)
	{
		LED1_TOGGLE();
		vTaskDelay(1000);
	}
}
void vSpace_TaskCode(void * pvParameters)
{
	TaskStatus_t LED_Status={};
	while(1)
	{
		vTaskGetInfo(vLcd,&LED_Status,pdTRUE,eInvalid);
		printf("系统剩余空间大小%d字节\r\n",xPortGetFreeHeapSize());
		printf("测试任务LED1的空间大小字%d字\r\n",LED_Status.usStackHighWaterMark);
		vTaskDelay(2000);
	}
}
void vKqm_TaskCode(void * pvParameters)
{
	BaseType_t xReturn=NULL;
	xReturn=xSemaphoreTake(vKqm_SemaphoreHandle_t,portMAX_DELAY);
	while(1)
	{
		if(xReturn==pdTRUE)
		{
			KQM_Handle();
			vTaskDelay(500);
		}
	}
}
void vKey_TaskCode(void * pvParameters)
{
	while(1)
	{
		KEY_Handle();
		vTaskDelay(10);
	}
}
void vSenor_TaskCode(void * pvParameters)
{
	while(1)
	{
		taskENTER_CRITICAL();
		DHT11_Handle();
		taskEXIT_CRITICAL();
		BH1750_Handle();
		DMA_Handle();
		vTaskDelay(Senor_speed);
	}
}

void xTime_CALLBACKFUNCTION(TimerHandle_t xTimer)
{
		Now_time_cnt=RTC_GetCounter();   //获取当前的RTC计数值
		Now_time=localtime(&Now_time_cnt);  //把获取的时间戳转换成时间结构体
		printf("当前时间:%04d-%02d-%02d %02d:%02d:%02d\r\n",
		Now_time->tm_year+1900,Now_time->tm_mon+1,Now_time->tm_mday,
		Now_time->tm_hour,Now_time->tm_min,Now_time->tm_sec);
		if(mode==AT)
			printf("自动模式\r\n");
		else printf("手动模式\r\n");
		
}
void vControl_TaskCode(void * pvParameters)
{
	while(1)
	{
		if(mode==AT)
		{
			if(Dht.Tem>28||Dht.Hum>70||Sensor.CO2>400)
				RELAY_ON();
			else RELAY_OFF();
			if(BH1750_Value<10)
				TIM_SetCompare1(TIM3,0);
			else if(BH1750_Value>=10&&BH1750_Value<100)
				TIM_SetCompare1(TIM3,50);
			else TIM_SetCompare1(TIM3,90);
		}
		vTaskDelay(20);
	}
}
void vSU03_TaskCode(void * pvParameters)
{
	while(1)
	{
		SU03T_Handle();
		vTaskDelay(100);
	}
}
void vCloud_TaskCode(void * pvParameters)
{
	while(1)
	{
		switch(NET_State)
		{
			case 0:
				ESP_Connetc_Time();
			break;
			case 1:
				ESP_Connetc_Net();
			break;
			case 2:
				Cloud_Connect();
			break;
			case 3:
				Cloud_MQTT_Sub();
			break;
			case 4:
				if(PUBLISH_Period[0]>PUBLISH_Period[1])
				{
					PUBLISH_Period[0]=0;
					Cloud_Publish();
				}
				if(HEART_Period[0]>HEART_Period[1])
				{
					HEART_Period[0]=0;
					Cloud_MQTT_Ping();
				}
			default:
			break;
		}
		Cloud_Cmd();
		vTaskDelay(10);
	}
}
void vLvglTaskFunction( void * pvParameters )
{
	TickType_t xLastWakeTime;
	const TickType_t xPeriod = pdMS_TO_TICKS( 5 );
	xLastWakeTime = xTaskGetTickCount();  
//	Lvgl_Demo2();//验证父子对象
//	Lvgl_Demo3();  //验证对象层级
//	Lvgl_Demo4();  //验证事件
//	Lvgl_Demo5();    //验证屏幕
//	Lvgl_Demo6();    //验证内置字体的显示
//	Lvgl_Demo7();     //显示官方工具生成的字体
//	Lvgl_Demo8();      //显示官方工具生成的图标字体
//	Lvgl_Demo9();    //显示官方工具生成的图片
//	Lvgl_Demo10();     //容器的使用
//	Lvgl_Demo11();      //外部字库显示
	setup_scr_screen(&guider_ui);    //创建屏幕1和屏幕中的对象
	setup_scr_screen_1(&guider_ui);    //创建屏幕2和屏幕中的对象
	lv_scr_load(guider_ui.screen);    //加载屏幕1
	//PS：不要软件定时器创建之后启动,要等LVGL创建对象之后,再启动
	if(xTimerStart(xTimer1,0)==pdPASS)     //启动软件定时器
	{
		printf("软件定时器1启动成功\r\n");				
	}
	for(;;)
	{		
		lv_task_handler();
		vTaskDelayUntil( &xLastWakeTime, xPeriod );//绝对延时
	}
}
void vApplicationTickHook(void)
{
	lv_tick_inc(1);  //接口的适配22  告诉LVGL过去多长时间
}
