#include "../../lvgl.h"

/*******************************************************************************
 * Size: 24 px
 * Bpp: 4
 * Opts: --no-compress --no-prefilter --bpp 4 --size 24 --font <PERSON>-Medium.ttf -r 0x20-0x7F,0xB0,0x2022 --font <PERSON>ont<PERSON>wesome5-Solid+Brands+Regular.woff -r 61441,61448,61451,61452,61452,61453,61457,61459,61461,61465,61468,61473,61478,61479,61480,61502,61512,61515,61516,61517,61521,61522,61523,61524,61543,61544,61550,61552,61553,61556,61559,61560,61561,61563,61587,61589,61636,61637,61639,61671,61674,61683,61724,61732,61787,61931,62016,62017,62018,62019,62020,62087,62099,62212,62189,62810,63426,63650 --format lvgl -o lv_font_montserrat_24.c --force-fast-kern-format
 ******************************************************************************/

#ifndef LV_FONT_MONTSERRAT_24
#define LV_FONT_MONTSERRAT_24 1
#endif

#if LV_FONT_MONTSERRAT_24

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t gylph_bitmap[] = {
    /* U+20 " " */

    /* U+21 "!" */
    0x1f, 0xf8, 0x1f, 0xf8, 0xf, 0xf7, 0xf, 0xf7,
    0xf, 0xf6, 0xe, 0xf5, 0xe, 0xf5, 0xd, 0xf4,
    0xd, 0xf3, 0xc, 0xf3, 0xb, 0xf2, 0x7, 0xa1,
    0x0, 0x0, 0x0, 0x0, 0x1d, 0xf6, 0x4f, 0xfb,
    0xc, 0xe5,

    /* U+22 "\"" */
    0x7f, 0x80, 0x2f, 0xd7, 0xf7, 0x1, 0xfd, 0x6f,
    0x70, 0x1f, 0xc6, 0xf7, 0x1, 0xfc, 0x6f, 0x60,
    0xf, 0xc5, 0xf6, 0x0, 0xfb, 0x38, 0x30, 0x8,
    0x60,

    /* U+23 "#" */
    0x0, 0x0, 0x8, 0xf2, 0x0, 0x5, 0xf5, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0x0, 0x0, 0x7f, 0x30,
    0x0, 0x0, 0x0, 0xd, 0xe0, 0x0, 0x9, 0xf1,
    0x0, 0x0, 0x0, 0x0, 0xfc, 0x0, 0x0, 0xbf,
    0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x30, 0x7c, 0xcd, 0xfe, 0xcc, 0xcc,
    0xfe, 0xcc, 0xc2, 0x0, 0x0, 0x4f, 0x60, 0x0,
    0x1f, 0x90, 0x0, 0x0, 0x0, 0x6, 0xf4, 0x0,
    0x3, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x20,
    0x0, 0x4f, 0x60, 0x0, 0x0, 0x0, 0xa, 0xf0,
    0x0, 0x6, 0xf4, 0x0, 0x0, 0x0, 0x0, 0xce,
    0x0, 0x0, 0x8f, 0x20, 0x0, 0x5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x4c, 0xcc,
    0xfe, 0xcc, 0xcc, 0xff, 0xcc, 0xc5, 0x0, 0x0,
    0x2f, 0x90, 0x0, 0xe, 0xd0, 0x0, 0x0, 0x0,
    0x4, 0xf7, 0x0, 0x0, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0x50, 0x0, 0x2f, 0x90, 0x0, 0x0,
    0x0, 0x8, 0xf3, 0x0, 0x4, 0xf7, 0x0, 0x0,
    0x0,

    /* U+24 "$" */
    0x0, 0x0, 0x0, 0x3f, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0x50, 0x0, 0x0, 0x0, 0x1, 0x7c,
    0xff, 0xfe, 0xb6, 0x0, 0x0, 0x3e, 0xff, 0xff,
    0xff, 0xff, 0xe1, 0x1, 0xef, 0xe6, 0x5f, 0x73,
    0x7d, 0xc0, 0x6, 0xff, 0x20, 0x3f, 0x50, 0x0,
    0x20, 0x9, 0xfc, 0x0, 0x3f, 0x50, 0x0, 0x0,
    0x8, 0xfe, 0x0, 0x3f, 0x50, 0x0, 0x0, 0x3,
    0xff, 0xc3, 0x3f, 0x50, 0x0, 0x0, 0x0, 0x8f,
    0xff, 0xef, 0x91, 0x0, 0x0, 0x0, 0x4, 0xcf,
    0xff, 0xff, 0xc5, 0x0, 0x0, 0x0, 0x1, 0x7f,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x3f, 0x53,
    0xbf, 0xf7, 0x0, 0x0, 0x0, 0x3f, 0x50, 0xb,
    0xfc, 0x0, 0x0, 0x0, 0x3f, 0x50, 0x7, 0xfe,
    0x5, 0x70, 0x0, 0x3f, 0x50, 0xc, 0xfb, 0xc,
    0xfd, 0x73, 0x4f, 0x64, 0xbf, 0xf4, 0x5, 0xef,
    0xff, 0xff, 0xff, 0xff, 0x70, 0x0, 0x6, 0xbe,
    0xff, 0xfe, 0x93, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0x50, 0x0,
    0x0,

    /* U+25 "%" */
    0x0, 0x3c, 0xfe, 0x90, 0x0, 0x0, 0x0, 0x6f,
    0x70, 0x0, 0x3, 0xfd, 0x78, 0xfb, 0x0, 0x0,
    0x2, 0xfc, 0x0, 0x0, 0xb, 0xe1, 0x0, 0x6f,
    0x40, 0x0, 0xc, 0xf2, 0x0, 0x0, 0xf, 0x90,
    0x0, 0xf, 0x90, 0x0, 0x7f, 0x60, 0x0, 0x0,
    0x1f, 0x70, 0x0, 0xe, 0xa0, 0x2, 0xfb, 0x0,
    0x0, 0x0, 0xf, 0x80, 0x0, 0xf, 0x90, 0xc,
    0xf1, 0x0, 0x0, 0x0, 0xd, 0xd0, 0x0, 0x3f,
    0x60, 0x7f, 0x60, 0x0, 0x0, 0x0, 0x5, 0xf9,
    0x24, 0xde, 0x2, 0xfb, 0x0, 0x1, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xd3, 0xc, 0xe1, 0x8, 0xff,
    0xe7, 0x0, 0x0, 0x1, 0x43, 0x0, 0x7f, 0x50,
    0x9f, 0x85, 0xaf, 0x70, 0x0, 0x0, 0x0, 0x2,
    0xfa, 0x2, 0xf8, 0x0, 0xb, 0xf0, 0x0, 0x0,
    0x0, 0xd, 0xe1, 0x5, 0xf3, 0x0, 0x5, 0xf4,
    0x0, 0x0, 0x0, 0x8f, 0x50, 0x6, 0xf1, 0x0,
    0x3, 0xf5, 0x0, 0x0, 0x3, 0xfa, 0x0, 0x5,
    0xf3, 0x0, 0x5, 0xf3, 0x0, 0x0, 0xd, 0xe1,
    0x0, 0x1, 0xf8, 0x0, 0xa, 0xe0, 0x0, 0x0,
    0x8f, 0x40, 0x0, 0x0, 0x8f, 0x84, 0x9f, 0x60,
    0x0, 0x3, 0xfa, 0x0, 0x0, 0x0, 0x7, 0xdf,
    0xd6, 0x0,

    /* U+26 "&" */
    0x0, 0x1, 0x9e, 0xfe, 0xa2, 0x0, 0x0, 0x0,
    0x0, 0x2e, 0xfe, 0xbd, 0xff, 0x30, 0x0, 0x0,
    0x0, 0xaf, 0xb0, 0x0, 0x7f, 0xb0, 0x0, 0x0,
    0x0, 0xdf, 0x50, 0x0, 0x2f, 0xd0, 0x0, 0x0,
    0x0, 0xcf, 0x70, 0x0, 0x6f, 0xa0, 0x0, 0x0,
    0x0, 0x6f, 0xe1, 0x6, 0xff, 0x20, 0x0, 0x0,
    0x0, 0xb, 0xfd, 0xbf, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xfb, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0x8, 0xff, 0x51, 0xcf, 0xd1, 0x0, 0xac, 0x20,
    0x4f, 0xf2, 0x0, 0xc, 0xfd, 0x20, 0xff, 0x0,
    0xbf, 0x80, 0x0, 0x0, 0xcf, 0xe8, 0xfb, 0x0,
    0xef, 0x60, 0x0, 0x0, 0xc, 0xff, 0xf3, 0x0,
    0xcf, 0xa0, 0x0, 0x0, 0x1, 0xff, 0xf2, 0x0,
    0x6f, 0xfa, 0x20, 0x1, 0x6e, 0xff, 0xfe, 0x20,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xa1, 0xbf, 0xd0,
    0x0, 0x39, 0xdf, 0xfd, 0x93, 0x0, 0xb, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+27 "'" */
    0x7f, 0x87, 0xf7, 0x6f, 0x76, 0xf7, 0x6f, 0x65,
    0xf6, 0x38, 0x30,

    /* U+28 "(" */
    0x0, 0x2f, 0xf1, 0x0, 0x9f, 0x90, 0x1, 0xff,
    0x20, 0x6, 0xfc, 0x0, 0xc, 0xf7, 0x0, 0xf,
    0xf3, 0x0, 0x3f, 0xf0, 0x0, 0x6f, 0xd0, 0x0,
    0x8f, 0xb0, 0x0, 0x9f, 0xa0, 0x0, 0xaf, 0x90,
    0x0, 0xbf, 0x80, 0x0, 0xaf, 0x90, 0x0, 0x9f,
    0xa0, 0x0, 0x8f, 0xb0, 0x0, 0x6f, 0xd0, 0x0,
    0x3f, 0xf0, 0x0, 0xf, 0xf3, 0x0, 0xc, 0xf7,
    0x0, 0x6, 0xfc, 0x0, 0x1, 0xff, 0x20, 0x0,
    0x9f, 0x90, 0x0, 0x2f, 0xf1,

    /* U+29 ")" */
    0xf, 0xf3, 0x0, 0x8, 0xfb, 0x0, 0x1, 0xff,
    0x30, 0x0, 0xbf, 0x80, 0x0, 0x6f, 0xd0, 0x0,
    0x1f, 0xf2, 0x0, 0xe, 0xf5, 0x0, 0xb, 0xf8,
    0x0, 0x9, 0xfa, 0x0, 0x8, 0xfb, 0x0, 0x7,
    0xfc, 0x0, 0x6, 0xfd, 0x0, 0x7, 0xfc, 0x0,
    0x8, 0xfb, 0x0, 0x9, 0xfa, 0x0, 0xb, 0xf8,
    0x0, 0xe, 0xf5, 0x0, 0x1f, 0xf2, 0x0, 0x6f,
    0xd0, 0x0, 0xbf, 0x80, 0x1, 0xff, 0x30, 0x8,
    0xfb, 0x0, 0xf, 0xf3, 0x0,

    /* U+2A "*" */
    0x0, 0x0, 0xe8, 0x0, 0x0, 0x3, 0x0, 0xe8,
    0x0, 0x20, 0x4f, 0xa1, 0xe8, 0x4d, 0xe0, 0x8,
    0xff, 0xfe, 0xfd, 0x40, 0x0, 0x2e, 0xff, 0xb0,
    0x0, 0x7, 0xef, 0xff, 0xfc, 0x30, 0x5f, 0xb2,
    0xe8, 0x5e, 0xe0, 0x4, 0x0, 0xe8, 0x0, 0x30,
    0x0, 0x0, 0xe8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+2B "+" */
    0x0, 0x0, 0xa, 0xa0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x5e, 0xee, 0xef, 0xfe,
    0xee, 0xe5, 0x0, 0x0, 0xf, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf0,
    0x0, 0x0,

    /* U+2C "," */
    0x3b, 0x80, 0xcf, 0xf3, 0xaf, 0xf3, 0xf, 0xe0,
    0x1f, 0x90, 0x5f, 0x40, 0x9e, 0x0,

    /* U+2D "-" */
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xfd, 0xaf,
    0xff, 0xff, 0xd0,

    /* U+2E "." */
    0x4, 0x10, 0x9f, 0xf1, 0xdf, 0xf4, 0x6f, 0xb0,

    /* U+2F "/" */
    0x0, 0x0, 0x0, 0x0, 0xef, 0x20, 0x0, 0x0,
    0x0, 0x4f, 0xd0, 0x0, 0x0, 0x0, 0x9, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0xef, 0x20, 0x0, 0x0,
    0x0, 0x4f, 0xc0, 0x0, 0x0, 0x0, 0xa, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x5f, 0xc0, 0x0, 0x0, 0x0, 0xa, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x5f, 0xb0, 0x0, 0x0, 0x0, 0xb, 0xf6,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x6f, 0xb0, 0x0, 0x0, 0x0, 0xb, 0xf5,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xa0, 0x0, 0x0, 0x0, 0xc, 0xf5,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xa0, 0x0, 0x0, 0x0, 0xd, 0xf4,
    0x0, 0x0, 0x0, 0x2, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x90, 0x0, 0x0, 0x0, 0x0,

    /* U+30 "0" */
    0x0, 0x1, 0x8d, 0xff, 0xd8, 0x10, 0x0, 0x0,
    0x2e, 0xff, 0xff, 0xff, 0xe2, 0x0, 0x1, 0xff,
    0xe7, 0x33, 0x7e, 0xff, 0x10, 0xb, 0xfe, 0x20,
    0x0, 0x2, 0xef, 0xb0, 0x2f, 0xf5, 0x0, 0x0,
    0x0, 0x5f, 0xf2, 0x7f, 0xe0, 0x0, 0x0, 0x0,
    0xe, 0xf7, 0xaf, 0xb0, 0x0, 0x0, 0x0, 0xb,
    0xfa, 0xcf, 0x90, 0x0, 0x0, 0x0, 0x9, 0xfc,
    0xdf, 0x80, 0x0, 0x0, 0x0, 0x8, 0xfd, 0xcf,
    0x90, 0x0, 0x0, 0x0, 0x9, 0xfc, 0xaf, 0xb0,
    0x0, 0x0, 0x0, 0xb, 0xfa, 0x7f, 0xe0, 0x0,
    0x0, 0x0, 0xe, 0xf7, 0x2f, 0xf5, 0x0, 0x0,
    0x0, 0x5f, 0xf2, 0xb, 0xfe, 0x10, 0x0, 0x2,
    0xef, 0xb0, 0x1, 0xff, 0xe7, 0x33, 0x7e, 0xff,
    0x10, 0x0, 0x3e, 0xff, 0xff, 0xff, 0xe2, 0x0,
    0x0, 0x1, 0x7c, 0xee, 0xc7, 0x10, 0x0,

    /* U+31 "1" */
    0xdf, 0xff, 0xff, 0x5d, 0xff, 0xff, 0xf5, 0x11,
    0x11, 0xff, 0x50, 0x0, 0xf, 0xf5, 0x0, 0x0,
    0xff, 0x50, 0x0, 0xf, 0xf5, 0x0, 0x0, 0xff,
    0x50, 0x0, 0xf, 0xf5, 0x0, 0x0, 0xff, 0x50,
    0x0, 0xf, 0xf5, 0x0, 0x0, 0xff, 0x50, 0x0,
    0xf, 0xf5, 0x0, 0x0, 0xff, 0x50, 0x0, 0xf,
    0xf5, 0x0, 0x0, 0xff, 0x50, 0x0, 0xf, 0xf5,
    0x0, 0x0, 0xff, 0x50,

    /* U+32 "2" */
    0x0, 0x17, 0xce, 0xfe, 0xc6, 0x0, 0x0, 0x7f,
    0xff, 0xff, 0xff, 0xfc, 0x10, 0x6f, 0xfc, 0x63,
    0x35, 0xbf, 0xfa, 0x0, 0x97, 0x0, 0x0, 0x0,
    0xaf, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x3, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf2, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x70, 0x0, 0x0, 0x0, 0x2,
    0xef, 0xc0, 0x0, 0x0, 0x0, 0x2, 0xef, 0xd1,
    0x0, 0x0, 0x0, 0x2, 0xef, 0xd1, 0x0, 0x0,
    0x0, 0x3, 0xef, 0xc1, 0x0, 0x0, 0x0, 0x3,
    0xef, 0xc0, 0x0, 0x0, 0x0, 0x3, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xc2, 0x11, 0x11,
    0x11, 0x11, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,

    /* U+33 "3" */
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe0, 0x1, 0x11, 0x11,
    0x11, 0x7f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x3f,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x3,
    0xff, 0xfc, 0x71, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x15, 0xef,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x81, 0xa1, 0x0, 0x0,
    0x0, 0x4f, 0xf5, 0x9f, 0xfa, 0x53, 0x34, 0x9f,
    0xfd, 0x3, 0xdf, 0xff, 0xff, 0xff, 0xfd, 0x20,
    0x0, 0x49, 0xdf, 0xfe, 0xb6, 0x0, 0x0,

    /* U+34 "4" */
    0x0, 0x0, 0x0, 0x0, 0xd, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xff, 0x60, 0x0, 0x9d, 0x70, 0x0,
    0x0, 0x1e, 0xfa, 0x0, 0x0, 0xbf, 0x90, 0x0,
    0x0, 0xbf, 0xd0, 0x0, 0x0, 0xbf, 0x90, 0x0,
    0x7, 0xff, 0x41, 0x11, 0x11, 0xbf, 0x91, 0x11,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x1f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x90, 0x0,

    /* U+35 "5" */
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xbf, 0x81,
    0x11, 0x11, 0x11, 0x0, 0xd, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0x40, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x31, 0x10, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xfe, 0xa3, 0x0, 0x5, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x26, 0xdf,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xd0, 0x93, 0x0, 0x0,
    0x0, 0x1e, 0xfa, 0x5f, 0xfb, 0x63, 0x24, 0x7e,
    0xff, 0x31, 0xbf, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x0, 0x28, 0xce, 0xfe, 0xc8, 0x10, 0x0,

    /* U+36 "6" */
    0x0, 0x0, 0x4a, 0xdf, 0xfe, 0xb5, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0x10, 0x0, 0xcf,
    0xf9, 0x42, 0x23, 0x76, 0x0, 0x8, 0xfe, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0xa0, 0x5b, 0xef, 0xd9, 0x30,
    0x0, 0xbf, 0x9a, 0xff, 0xff, 0xff, 0xf7, 0x0,
    0xdf, 0xff, 0xc4, 0x1, 0x4c, 0xff, 0x40, 0xcf,
    0xfc, 0x0, 0x0, 0x0, 0xcf, 0xc0, 0xbf, 0xf5,
    0x0, 0x0, 0x0, 0x5f, 0xf0, 0x8f, 0xf2, 0x0,
    0x0, 0x0, 0x3f, 0xf1, 0x4f, 0xf4, 0x0, 0x0,
    0x0, 0x4f, 0xf0, 0xd, 0xfc, 0x0, 0x0, 0x0,
    0xbf, 0xb0, 0x4, 0xff, 0xc3, 0x0, 0x3b, 0xff,
    0x30, 0x0, 0x5f, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x1, 0x8d, 0xff, 0xd9, 0x20, 0x0,

    /* U+37 "7" */
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x4f, 0xf2,
    0x11, 0x11, 0x11, 0x8f, 0xf1, 0x4f, 0xf0, 0x0,
    0x0, 0x0, 0xef, 0x90, 0x4f, 0xf0, 0x0, 0x0,
    0x5, 0xff, 0x20, 0x2, 0x20, 0x0, 0x0, 0xc,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1f, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xfb, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xd0, 0x0, 0x0, 0x0,

    /* U+38 "8" */
    0x0, 0x6, 0xbe, 0xff, 0xd9, 0x20, 0x0, 0x2,
    0xdf, 0xff, 0xff, 0xff, 0xf7, 0x0, 0xd, 0xff,
    0x71, 0x0, 0x4b, 0xff, 0x40, 0x3f, 0xf5, 0x0,
    0x0, 0x0, 0xcf, 0xa0, 0x4f, 0xf1, 0x0, 0x0,
    0x0, 0x9f, 0xc0, 0x2f, 0xf5, 0x0, 0x0, 0x0,
    0xdf, 0x90, 0xa, 0xff, 0x72, 0x1, 0x4c, 0xff,
    0x20, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xd3, 0x0,
    0x4, 0xdf, 0xff, 0xef, 0xff, 0xf8, 0x0, 0x2f,
    0xfc, 0x40, 0x0, 0x18, 0xff, 0x90, 0xaf, 0xd0,
    0x0, 0x0, 0x0, 0x6f, 0xf2, 0xdf, 0x80, 0x0,
    0x0, 0x0, 0xf, 0xf5, 0xdf, 0x80, 0x0, 0x0,
    0x0, 0x1f, 0xf5, 0xaf, 0xe1, 0x0, 0x0, 0x0,
    0x8f, 0xf2, 0x3f, 0xfd, 0x51, 0x0, 0x3a, 0xff,
    0xa0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0,
    0x0, 0x17, 0xce, 0xff, 0xd9, 0x40, 0x0,

    /* U+39 "9" */
    0x0, 0x4, 0xae, 0xfe, 0xc7, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xe2, 0x0, 0x8, 0xff,
    0x82, 0x0, 0x4c, 0xfe, 0x10, 0x1f, 0xf6, 0x0,
    0x0, 0x0, 0xcf, 0xa0, 0x3f, 0xf0, 0x0, 0x0,
    0x0, 0x6f, 0xf1, 0x4f, 0xf0, 0x0, 0x0, 0x0,
    0x6f, 0xf5, 0x1f, 0xf6, 0x0, 0x0, 0x0, 0xcf,
    0xf8, 0xa, 0xff, 0x82, 0x0, 0x4c, 0xff, 0xf9,
    0x0, 0xcf, 0xff, 0xff, 0xff, 0x9c, 0xfa, 0x0,
    0x6, 0xbe, 0xfe, 0xa4, 0xd, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0x50, 0x0, 0xa7, 0x31, 0x25, 0xcf, 0xf9,
    0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x7b, 0xdf, 0xec, 0x82, 0x0, 0x0,

    /* U+3A ":" */
    0x5f, 0xb0, 0xdf, 0xf4, 0x9f, 0xf1, 0x4, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x10, 0x9f, 0xf1, 0xdf, 0xf4,
    0x6f, 0xb0,

    /* U+3B ";" */
    0x5f, 0xb0, 0xdf, 0xf4, 0x9f, 0xf1, 0x4, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5e, 0xb0, 0xcf, 0xf4,
    0x8f, 0xf2, 0xf, 0xd0, 0x2f, 0x80, 0x6f, 0x30,
    0x7b, 0x0,

    /* U+3C "<" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0x84, 0x0, 0x0,
    0x0, 0x4, 0xbf, 0xf5, 0x0, 0x0, 0x18, 0xef,
    0xfe, 0x81, 0x0, 0x4b, 0xff, 0xfa, 0x40, 0x0,
    0x3e, 0xff, 0xd7, 0x10, 0x0, 0x0, 0x6f, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x3e, 0xff, 0xd7, 0x10,
    0x0, 0x0, 0x0, 0x4b, 0xff, 0xfa, 0x40, 0x0,
    0x0, 0x0, 0x18, 0xef, 0xfe, 0x81, 0x0, 0x0,
    0x0, 0x4, 0xbf, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x84,

    /* U+3D "=" */
    0x5e, 0xee, 0xee, 0xee, 0xee, 0xe5, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5e, 0xee, 0xee, 0xee,
    0xee, 0xe5, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xf5,

    /* U+3E ">" */
    0x47, 0x10, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xfa,
    0x40, 0x0, 0x0, 0x0, 0x18, 0xef, 0xfe, 0x71,
    0x0, 0x0, 0x0, 0x4, 0xbf, 0xff, 0xb4, 0x0,
    0x0, 0x0, 0x1, 0x7d, 0xff, 0xe3, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xf5, 0x0, 0x0, 0x1, 0x7d,
    0xff, 0xe3, 0x0, 0x4, 0xaf, 0xff, 0xb4, 0x0,
    0x18, 0xef, 0xfe, 0x71, 0x0, 0x0, 0x6f, 0xfb,
    0x40, 0x0, 0x0, 0x0, 0x47, 0x10, 0x0, 0x0,
    0x0, 0x0,

    /* U+3F "?" */
    0x0, 0x28, 0xce, 0xfe, 0xc7, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xfd, 0x10, 0x7f, 0xfb, 0x52,
    0x13, 0xaf, 0xfb, 0x0, 0x96, 0x0, 0x0, 0x0,
    0xaf, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x3f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xfa, 0x0, 0x0, 0x0, 0x0, 0xe, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x48, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xfb, 0x0, 0x0, 0x0,

    /* U+40 "@" */
    0x0, 0x0, 0x0, 0x16, 0xad, 0xff, 0xfd, 0xa6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff,
    0xca, 0x9a, 0xcf, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x2, 0xdf, 0xc5, 0x0, 0x0, 0x0, 0x4, 0xcf,
    0xd2, 0x0, 0x0, 0x2, 0xef, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xe2, 0x0, 0x0, 0xdf,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xd0, 0x0, 0x8f, 0x80, 0x0, 0x7, 0xcf, 0xfc,
    0x60, 0xaf, 0x70, 0x7f, 0x70, 0xf, 0xe0, 0x0,
    0x2d, 0xff, 0xfe, 0xff, 0xcb, 0xf7, 0x0, 0xde,
    0x5, 0xf7, 0x0, 0xd, 0xfd, 0x30, 0x1, 0x7f,
    0xff, 0x70, 0x7, 0xf4, 0x9f, 0x30, 0x6, 0xfe,
    0x10, 0x0, 0x0, 0x6f, 0xf7, 0x0, 0x2f, 0x7c,
    0xf0, 0x0, 0xbf, 0x70, 0x0, 0x0, 0x0, 0xef,
    0x70, 0x0, 0xf9, 0xde, 0x0, 0xd, 0xf3, 0x0,
    0x0, 0x0, 0xa, 0xf7, 0x0, 0xf, 0xad, 0xe0,
    0x0, 0xdf, 0x30, 0x0, 0x0, 0x0, 0xaf, 0x70,
    0x0, 0xf9, 0xbf, 0x0, 0xb, 0xf7, 0x0, 0x0,
    0x0, 0xd, 0xf7, 0x0, 0x2f, 0x79, 0xf3, 0x0,
    0x6f, 0xe0, 0x0, 0x0, 0x6, 0xff, 0x70, 0x6,
    0xf4, 0x5f, 0x70, 0x0, 0xdf, 0xc3, 0x0, 0x7,
    0xff, 0xfb, 0x1, 0xde, 0x0, 0xee, 0x0, 0x2,
    0xdf, 0xfe, 0xdf, 0xfc, 0x3f, 0xfe, 0xff, 0x50,
    0x8, 0xf9, 0x0, 0x0, 0x7c, 0xff, 0xc7, 0x0,
    0x5d, 0xfd, 0x50, 0x0, 0xd, 0xf5, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2e, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2d, 0xfc, 0x50, 0x0,
    0x0, 0x2, 0x81, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xfc, 0xaa, 0xbd, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x1, 0x6a, 0xdf, 0xff,
    0xd9, 0x40, 0x0, 0x0, 0x0,

    /* U+41 "A" */
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfe,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0x5d, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xe0, 0x6f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf7, 0x0, 0xef, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0x10, 0x8, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xa0, 0x0,
    0x1f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xf3,
    0x0, 0x0, 0xaf, 0xc0, 0x0, 0x0, 0x0, 0xa,
    0xfc, 0x0, 0x0, 0x4, 0xff, 0x30, 0x0, 0x0,
    0x2, 0xff, 0x50, 0x0, 0x0, 0xd, 0xfa, 0x0,
    0x0, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf2, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x7, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x8, 0xff, 0x10, 0x0, 0xef, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf7, 0x0, 0x5f,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xe0,
    0xc, 0xfa, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x50,

    /* U+42 "B" */
    0x7f, 0xff, 0xff, 0xff, 0xfe, 0xb6, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x7f,
    0xe0, 0x0, 0x0, 0x2, 0x9f, 0xfa, 0x7, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xf0, 0x7f, 0xe0,
    0x0, 0x0, 0x0, 0x5, 0xff, 0x7, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xd0, 0x7f, 0xe0, 0x0,
    0x0, 0x2, 0x9f, 0xf5, 0x7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd4, 0x7, 0xfe, 0x0, 0x0, 0x0,
    0x14, 0xcf, 0xf3, 0x7f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xa7, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xfd, 0x7f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xe7, 0xfe, 0x0, 0x0, 0x0, 0x0, 0xc,
    0xfc, 0x7f, 0xe0, 0x0, 0x0, 0x0, 0x3b, 0xff,
    0x67, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x90,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xd9, 0x30, 0x0,

    /* U+43 "C" */
    0x0, 0x0, 0x5, 0xad, 0xff, 0xda, 0x50, 0x0,
    0x0, 0x3, 0xdf, 0xff, 0xff, 0xff, 0xfd, 0x30,
    0x0, 0x5f, 0xff, 0xb6, 0x43, 0x59, 0xff, 0xf2,
    0x3, 0xff, 0xd3, 0x0, 0x0, 0x0, 0x1c, 0x80,
    0xd, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xff, 0xd3, 0x0, 0x0, 0x0, 0x1c, 0x80,
    0x0, 0x5f, 0xff, 0xb5, 0x33, 0x49, 0xff, 0xf2,
    0x0, 0x3, 0xdf, 0xff, 0xff, 0xff, 0xfd, 0x30,
    0x0, 0x0, 0x5, 0xae, 0xff, 0xda, 0x50, 0x0,

    /* U+44 "D" */
    0x7f, 0xff, 0xff, 0xff, 0xfd, 0x94, 0x0, 0x0,
    0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x20,
    0x0, 0x7f, 0xe1, 0x11, 0x12, 0x36, 0xbf, 0xff,
    0x40, 0x7, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x4e,
    0xfe, 0x20, 0x7f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xfb, 0x7, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xf2, 0x7f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xff, 0x77, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf9, 0x7f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xa7, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xf9, 0x7f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xff, 0x77, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xf2, 0x7f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xfb, 0x7, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x3e, 0xfe, 0x20, 0x7f,
    0xe1, 0x11, 0x11, 0x35, 0xbf, 0xff, 0x40, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x20, 0x0,
    0x7f, 0xff, 0xff, 0xff, 0xfd, 0x94, 0x0, 0x0,
    0x0,

    /* U+45 "E" */
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x67, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x7f, 0xe1, 0x11,
    0x11, 0x11, 0x11, 0x7, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x7, 0xfe, 0x11, 0x11, 0x11, 0x11,
    0x0, 0x7f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xe1, 0x11, 0x11, 0x11,
    0x11, 0x17, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0,

    /* U+46 "F" */
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x67, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0x7f, 0xe1, 0x11,
    0x11, 0x11, 0x11, 0x7, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xf5, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x7f, 0xe1, 0x11, 0x11, 0x11, 0x10, 0x7,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+47 "G" */
    0x0, 0x0, 0x5, 0xad, 0xff, 0xeb, 0x60, 0x0,
    0x0, 0x3, 0xdf, 0xff, 0xff, 0xff, 0xfe, 0x50,
    0x0, 0x5f, 0xff, 0xb6, 0x43, 0x48, 0xef, 0xf5,
    0x3, 0xff, 0xd3, 0x0, 0x0, 0x0, 0x9, 0xb0,
    0xd, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x90, 0x0, 0x0, 0x0, 0x0, 0x7, 0x94,
    0xcf, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf7,
    0x9f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf7,
    0x5f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf7,
    0xd, 0xfe, 0x10, 0x0, 0x0, 0x0, 0xd, 0xf7,
    0x3, 0xff, 0xd3, 0x0, 0x0, 0x0, 0xd, 0xf7,
    0x0, 0x5f, 0xff, 0xb6, 0x32, 0x47, 0xdf, 0xf7,
    0x0, 0x3, 0xdf, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x5, 0xad, 0xff, 0xeb, 0x71, 0x0,

    /* U+48 "H" */
    0x7f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf7,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x7f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf7, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x7f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xf7, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0x7f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf7, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x7f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf7, 0xfe, 0x11, 0x11, 0x11,
    0x11, 0x17, 0xff, 0x7f, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xf7, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xff, 0x7f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf7, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0x7f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xf7, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0x7f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf0,

    /* U+49 "I" */
    0x7f, 0xe7, 0xfe, 0x7f, 0xe7, 0xfe, 0x7f, 0xe7,
    0xfe, 0x7f, 0xe7, 0xfe, 0x7f, 0xe7, 0xfe, 0x7f,
    0xe7, 0xfe, 0x7f, 0xe7, 0xfe, 0x7f, 0xe7, 0xfe,
    0x7f, 0xe0,

    /* U+4A "J" */
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xf0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x1, 0x11, 0x11, 0x7f,
    0xf0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xf0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0,
    0x7, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xd0,
    0x5a, 0x0, 0x0, 0xd, 0xfa, 0xf, 0xfc, 0x41,
    0x3b, 0xff, 0x50, 0x5f, 0xff, 0xff, 0xff, 0xa0,
    0x0, 0x29, 0xdf, 0xfc, 0x60, 0x0,

    /* U+4B "K" */
    0x7f, 0xe0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x50,
    0x7f, 0xe0, 0x0, 0x0, 0x0, 0x6f, 0xf6, 0x0,
    0x7f, 0xe0, 0x0, 0x0, 0x5, 0xff, 0x60, 0x0,
    0x7f, 0xe0, 0x0, 0x0, 0x5f, 0xf7, 0x0, 0x0,
    0x7f, 0xe0, 0x0, 0x4, 0xff, 0x80, 0x0, 0x0,
    0x7f, 0xe0, 0x0, 0x4f, 0xf9, 0x0, 0x0, 0x0,
    0x7f, 0xe0, 0x3, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0x7f, 0xe0, 0x3f, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xe3, 0xef, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x7f, 0xfe, 0xfd, 0xdf, 0xf2, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xd1, 0x2f, 0xfd, 0x10, 0x0, 0x0,
    0x7f, 0xfe, 0x10, 0x4, 0xff, 0xb0, 0x0, 0x0,
    0x7f, 0xf2, 0x0, 0x0, 0x6f, 0xf8, 0x0, 0x0,
    0x7f, 0xe0, 0x0, 0x0, 0x8, 0xff, 0x50, 0x0,
    0x7f, 0xe0, 0x0, 0x0, 0x0, 0xbf, 0xf3, 0x0,
    0x7f, 0xe0, 0x0, 0x0, 0x0, 0xc, 0xfe, 0x10,
    0x7f, 0xe0, 0x0, 0x0, 0x0, 0x1, 0xef, 0xc0,

    /* U+4C "L" */
    0x7f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f,
    0xe0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xe0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xe0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7f, 0xe1, 0x11, 0x11, 0x11,
    0x11, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x7f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,

    /* U+4D "M" */
    0x7f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x67, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xf6, 0x7f, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1f, 0xff, 0x67, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xf6, 0x7f, 0xff,
    0xf2, 0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0x67,
    0xfd, 0xbf, 0xb0, 0x0, 0x0, 0x0, 0xcf, 0x8e,
    0xf6, 0x7f, 0xd2, 0xff, 0x40, 0x0, 0x0, 0x5f,
    0xe0, 0xef, 0x67, 0xfd, 0x8, 0xfd, 0x0, 0x0,
    0xe, 0xf6, 0xe, 0xf6, 0x7f, 0xd0, 0xe, 0xf7,
    0x0, 0x8, 0xfc, 0x0, 0xef, 0x67, 0xfd, 0x0,
    0x5f, 0xf1, 0x1, 0xff, 0x30, 0xe, 0xf6, 0x7f,
    0xd0, 0x0, 0xbf, 0xa0, 0xaf, 0x90, 0x0, 0xef,
    0x67, 0xfd, 0x0, 0x2, 0xff, 0x7f, 0xe1, 0x0,
    0xe, 0xf6, 0x7f, 0xd0, 0x0, 0x8, 0xff, 0xf6,
    0x0, 0x0, 0xef, 0x67, 0xfd, 0x0, 0x0, 0xe,
    0xfd, 0x0, 0x0, 0xe, 0xf6, 0x7f, 0xd0, 0x0,
    0x0, 0x5f, 0x40, 0x0, 0x0, 0xef, 0x67, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf6,
    0x7f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0x60,

    /* U+4E "N" */
    0x7f, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xf7,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0x7, 0xff, 0x7f,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x7f, 0xf7, 0xff,
    0xff, 0x40, 0x0, 0x0, 0x7, 0xff, 0x7f, 0xfd,
    0xfe, 0x20, 0x0, 0x0, 0x7f, 0xf7, 0xfe, 0x3f,
    0xfd, 0x0, 0x0, 0x7, 0xff, 0x7f, 0xe0, 0x5f,
    0xfa, 0x0, 0x0, 0x7f, 0xf7, 0xfe, 0x0, 0x8f,
    0xf7, 0x0, 0x7, 0xff, 0x7f, 0xe0, 0x0, 0xbf,
    0xf4, 0x0, 0x7f, 0xf7, 0xfe, 0x0, 0x1, 0xef,
    0xe1, 0x7, 0xff, 0x7f, 0xe0, 0x0, 0x3, 0xff,
    0xc0, 0x7f, 0xf7, 0xfe, 0x0, 0x0, 0x6, 0xff,
    0xa7, 0xff, 0x7f, 0xe0, 0x0, 0x0, 0x9, 0xff,
    0xdf, 0xf7, 0xfe, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0x7f, 0xe0, 0x0, 0x0, 0x0, 0x1e, 0xff,
    0xf7, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0x7f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xf0,

    /* U+4F "O" */
    0x0, 0x0, 0x5, 0xad, 0xff, 0xeb, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x2d, 0xff, 0xff, 0xff, 0xff,
    0xe4, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xb6, 0x33,
    0x5a, 0xff, 0xf7, 0x0, 0x0, 0x3f, 0xfd, 0x30,
    0x0, 0x0, 0x1, 0xcf, 0xf5, 0x0, 0xd, 0xfe,
    0x10, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xf1, 0x4,
    0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x70, 0x9f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xfc, 0xc, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf0, 0xdf, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xc, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xf0, 0x9f,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfc,
    0x5, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x70, 0xd, 0xfe, 0x10, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf1, 0x0, 0x3f, 0xfd, 0x30, 0x0,
    0x0, 0x1, 0xcf, 0xf5, 0x0, 0x0, 0x5f, 0xff,
    0xb5, 0x33, 0x59, 0xff, 0xf8, 0x0, 0x0, 0x0,
    0x3d, 0xff, 0xff, 0xff, 0xff, 0xe4, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xad, 0xff, 0xeb, 0x60, 0x0,
    0x0, 0x0,

    /* U+50 "P" */
    0x7f, 0xff, 0xff, 0xff, 0xeb, 0x60, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd2, 0x0, 0x7f,
    0xe1, 0x11, 0x12, 0x49, 0xff, 0xe1, 0x7, 0xfe,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x90, 0x7f, 0xe0,
    0x0, 0x0, 0x0, 0x9, 0xfe, 0x7, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf0, 0x7f, 0xe0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x7, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xe0, 0x7f, 0xe0, 0x0, 0x0,
    0x0, 0x3f, 0xf9, 0x7, 0xfe, 0x11, 0x11, 0x24,
    0x8f, 0xfe, 0x10, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x20, 0x7, 0xff, 0xff, 0xff, 0xfe, 0xb6,
    0x0, 0x0, 0x7f, 0xe0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+51 "Q" */
    0x0, 0x0, 0x5, 0xad, 0xff, 0xeb, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x2d, 0xff, 0xff, 0xff, 0xff,
    0xe4, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xb6, 0x33,
    0x5a, 0xff, 0xf7, 0x0, 0x0, 0x2f, 0xfd, 0x30,
    0x0, 0x0, 0x2, 0xcf, 0xf5, 0x0, 0xd, 0xfe,
    0x10, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xf1, 0x4,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x70, 0x9f, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xfc, 0xc, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf0, 0xdf, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xc, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0xaf,
    0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb, 0xfc,
    0x5, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x70, 0xe, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xf1, 0x0, 0x4f, 0xfc, 0x20, 0x0,
    0x0, 0x1, 0xbf, 0xf6, 0x0, 0x0, 0x7f, 0xff,
    0xa4, 0x22, 0x48, 0xef, 0xf9, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0x17, 0xce, 0xff, 0xfd, 0x71, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xf7,
    0x10, 0x4, 0xc3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3a, 0xef, 0xea, 0x40,

    /* U+52 "R" */
    0x7f, 0xff, 0xff, 0xff, 0xeb, 0x60, 0x0, 0x7,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd2, 0x0, 0x7f,
    0xe1, 0x11, 0x12, 0x49, 0xff, 0xe1, 0x7, 0xfe,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x90, 0x7f, 0xe0,
    0x0, 0x0, 0x0, 0x9, 0xfe, 0x7, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xf0, 0x7f, 0xe0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0x7, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x9f, 0xe0, 0x7f, 0xe0, 0x0, 0x0,
    0x0, 0x3f, 0xf8, 0x7, 0xfe, 0x11, 0x11, 0x13,
    0x8f, 0xfe, 0x10, 0x7f, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x20, 0x7, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0x0, 0x7f, 0xe0, 0x0, 0x0, 0x7f, 0xf2,
    0x0, 0x7, 0xfe, 0x0, 0x0, 0x0, 0xcf, 0xd0,
    0x0, 0x7f, 0xe0, 0x0, 0x0, 0x1, 0xef, 0x90,
    0x7, 0xfe, 0x0, 0x0, 0x0, 0x5, 0xff, 0x40,
    0x7f, 0xe0, 0x0, 0x0, 0x0, 0x9, 0xfe, 0x10,

    /* U+53 "S" */
    0x0, 0x0, 0x6b, 0xef, 0xfd, 0xa5, 0x0, 0x0,
    0x2e, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0xef,
    0xe7, 0x21, 0x14, 0x8e, 0xc0, 0x6, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x20, 0x9, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xff, 0xc3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xd9, 0x51, 0x0, 0x0,
    0x0, 0x4, 0xbf, 0xff, 0xff, 0xc5, 0x0, 0x0,
    0x0, 0x1, 0x59, 0xdf, 0xff, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xaf, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xfe, 0x5, 0x80, 0x0, 0x0, 0x0,
    0xc, 0xfb, 0xd, 0xfe, 0x84, 0x21, 0x25, 0xcf,
    0xf4, 0x3, 0xdf, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x4, 0x9d, 0xef, 0xec, 0x82, 0x0,

    /* U+54 "T" */
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x11, 0x11,
    0x13, 0xff, 0x51, 0x11, 0x11, 0x0, 0x0, 0x2,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x30, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x2,
    0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x2, 0xff, 0x30, 0x0, 0x0,

    /* U+55 "U" */
    0xaf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x9a,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0xc, 0xf9, 0xaf,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0x9a, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xf9, 0xaf, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0x9a, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xf9, 0xaf, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0x9a, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0xc, 0xf9, 0xaf, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0x99, 0xfc, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xf9, 0x9f, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0x87, 0xff, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf6, 0x3f, 0xf5, 0x0, 0x0, 0x0, 0x5,
    0xff, 0x20, 0xdf, 0xe1, 0x0, 0x0, 0x1, 0xef,
    0xc0, 0x3, 0xff, 0xe7, 0x32, 0x38, 0xef, 0xf3,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xe4, 0x0,
    0x0, 0x1, 0x7c, 0xef, 0xec, 0x71, 0x0, 0x0,

    /* U+56 "V" */
    0xd, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xfe, 0x0, 0x6f, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0x70, 0x0, 0xef, 0xa0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf1, 0x0, 0x8, 0xff, 0x10,
    0x0, 0x0, 0x0, 0xc, 0xf9, 0x0, 0x0, 0x1f,
    0xf8, 0x0, 0x0, 0x0, 0x4, 0xff, 0x20, 0x0,
    0x0, 0xaf, 0xe0, 0x0, 0x0, 0x0, 0xbf, 0xb0,
    0x0, 0x0, 0x3, 0xff, 0x50, 0x0, 0x0, 0x2f,
    0xf4, 0x0, 0x0, 0x0, 0xc, 0xfc, 0x0, 0x0,
    0x9, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xf3,
    0x0, 0x0, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xa0, 0x0, 0x7f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x7, 0xff, 0x10, 0xd, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf8, 0x5, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xe0, 0xcf,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0xff,
    0x9f, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xb, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xe0, 0x0, 0x0,
    0x0, 0x0,

    /* U+57 "W" */
    0x1f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xe0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0x10, 0xbf, 0xb0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x8f, 0xc0, 0x6, 0xff, 0x10, 0x0, 0x0,
    0x5, 0xff, 0xf8, 0x0, 0x0, 0x0, 0xd, 0xf6,
    0x0, 0x1f, 0xf6, 0x0, 0x0, 0x0, 0xbf, 0xef,
    0xe0, 0x0, 0x0, 0x3, 0xff, 0x10, 0x0, 0xbf,
    0xb0, 0x0, 0x0, 0x1f, 0xf4, 0xff, 0x30, 0x0,
    0x0, 0x8f, 0xc0, 0x0, 0x6, 0xff, 0x10, 0x0,
    0x6, 0xfd, 0xc, 0xf8, 0x0, 0x0, 0xd, 0xf6,
    0x0, 0x0, 0x1f, 0xf5, 0x0, 0x0, 0xbf, 0x70,
    0x6f, 0xe0, 0x0, 0x3, 0xff, 0x10, 0x0, 0x0,
    0xcf, 0xb0, 0x0, 0x1f, 0xf2, 0x1, 0xff, 0x30,
    0x0, 0x8f, 0xc0, 0x0, 0x0, 0x6, 0xff, 0x0,
    0x6, 0xfd, 0x0, 0xc, 0xf8, 0x0, 0xd, 0xf7,
    0x0, 0x0, 0x0, 0x1f, 0xf5, 0x0, 0xbf, 0x70,
    0x0, 0x6f, 0xe0, 0x3, 0xff, 0x10, 0x0, 0x0,
    0x0, 0xcf, 0xb0, 0x1f, 0xf2, 0x0, 0x1, 0xff,
    0x30, 0x8f, 0xc0, 0x0, 0x0, 0x0, 0x6, 0xff,
    0x6, 0xfc, 0x0, 0x0, 0xb, 0xf8, 0xd, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf5, 0xcf, 0x70,
    0x0, 0x0, 0x6f, 0xe3, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0xcf, 0xcf, 0xf2, 0x0, 0x0, 0x1,
    0xff, 0xbf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0xb, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x6f, 0xff, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xf1, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xc0, 0x0, 0x0, 0x0,

    /* U+58 "X" */
    0xe, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xe1,
    0x3, 0xff, 0x80, 0x0, 0x0, 0x4, 0xff, 0x50,
    0x0, 0x8f, 0xf3, 0x0, 0x0, 0x1e, 0xf9, 0x0,
    0x0, 0xc, 0xfe, 0x10, 0x0, 0xbf, 0xd0, 0x0,
    0x0, 0x2, 0xff, 0xa0, 0x6, 0xff, 0x20, 0x0,
    0x0, 0x0, 0x5f, 0xf6, 0x2f, 0xf6, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xdf, 0xb0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xef, 0xfe, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9f, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x1e, 0xfa, 0x9f, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0xbf, 0xe1, 0xd, 0xfd, 0x0, 0x0,
    0x0, 0x6, 0xff, 0x40, 0x2, 0xff, 0x90, 0x0,
    0x0, 0x2f, 0xf8, 0x0, 0x0, 0x6f, 0xf4, 0x0,
    0x0, 0xdf, 0xd0, 0x0, 0x0, 0xb, 0xfe, 0x10,
    0x9, 0xff, 0x20, 0x0, 0x0, 0x1, 0xef, 0xb0,
    0x5f, 0xf6, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf7,

    /* U+59 "Y" */
    0xc, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x1, 0xff,
    0x50, 0x3f, 0xf5, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xb0, 0x0, 0x9f, 0xe0, 0x0, 0x0, 0x0, 0x4f,
    0xf2, 0x0, 0x1, 0xef, 0x90, 0x0, 0x0, 0xd,
    0xf8, 0x0, 0x0, 0x6, 0xff, 0x20, 0x0, 0x7,
    0xfe, 0x0, 0x0, 0x0, 0xc, 0xfc, 0x0, 0x1,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x3f, 0xf5, 0x0,
    0xaf, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xe0,
    0x4f, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x1, 0xef,
    0x9d, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0x0, 0x0, 0x0,
    0x0,

    /* U+5A "Z" */
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0xc,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb0, 0x11,
    0x11, 0x11, 0x11, 0x11, 0xcf, 0xf2, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5f, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0x20, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x2, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x4f, 0xfa, 0x11, 0x11, 0x11, 0x11, 0x11,
    0xe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf1,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x10,

    /* U+5B "[" */
    0x7f, 0xff, 0xf8, 0x7f, 0xfe, 0xe7, 0x7f, 0xd0,
    0x0, 0x7f, 0xd0, 0x0, 0x7f, 0xd0, 0x0, 0x7f,
    0xd0, 0x0, 0x7f, 0xd0, 0x0, 0x7f, 0xd0, 0x0,
    0x7f, 0xd0, 0x0, 0x7f, 0xd0, 0x0, 0x7f, 0xd0,
    0x0, 0x7f, 0xd0, 0x0, 0x7f, 0xd0, 0x0, 0x7f,
    0xd0, 0x0, 0x7f, 0xd0, 0x0, 0x7f, 0xd0, 0x0,
    0x7f, 0xd0, 0x0, 0x7f, 0xd0, 0x0, 0x7f, 0xd0,
    0x0, 0x7f, 0xd0, 0x0, 0x7f, 0xd0, 0x0, 0x7f,
    0xfe, 0xe7, 0x7f, 0xff, 0xf8,

    /* U+5C "\\" */
    0xbf, 0x50, 0x0, 0x0, 0x0, 0x6, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x1f, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x60, 0x0, 0x0, 0x0, 0x5, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x0, 0x0,
    0x0, 0x0, 0xaf, 0x60, 0x0, 0x0, 0x0, 0x5,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0xf, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0x70, 0x0, 0x0, 0x0,
    0x4, 0xfc, 0x0, 0x0, 0x0, 0x0, 0xe, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0x70, 0x0, 0x0,
    0x0, 0x4, 0xfd, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xf2, 0x0, 0x0, 0x0, 0x0, 0x9f, 0x80, 0x0,
    0x0, 0x0, 0x3, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x8f, 0x80,
    0x0, 0x0, 0x0, 0x3, 0xfe, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x8f,
    0x90, 0x0, 0x0, 0x0, 0x2, 0xfe, 0x0,

    /* U+5D "]" */
    0x8f, 0xff, 0xf7, 0x8e, 0xef, 0xf7, 0x0, 0xd,
    0xf7, 0x0, 0xd, 0xf7, 0x0, 0xd, 0xf7, 0x0,
    0xd, 0xf7, 0x0, 0xd, 0xf7, 0x0, 0xd, 0xf7,
    0x0, 0xd, 0xf7, 0x0, 0xd, 0xf7, 0x0, 0xd,
    0xf7, 0x0, 0xd, 0xf7, 0x0, 0xd, 0xf7, 0x0,
    0xd, 0xf7, 0x0, 0xd, 0xf7, 0x0, 0xd, 0xf7,
    0x0, 0xd, 0xf7, 0x0, 0xd, 0xf7, 0x0, 0xd,
    0xf7, 0x0, 0xd, 0xf7, 0x0, 0xd, 0xf7, 0x8e,
    0xef, 0xf7, 0x8f, 0xff, 0xf7,

    /* U+5E "^" */
    0x0, 0x0, 0x2f, 0xf2, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xfc, 0xbf,
    0x10, 0x0, 0x0, 0x7, 0xf5, 0x5f, 0x70, 0x0,
    0x0, 0xd, 0xe0, 0xe, 0xd0, 0x0, 0x0, 0x4f,
    0x80, 0x8, 0xf4, 0x0, 0x0, 0xbf, 0x10, 0x1,
    0xfb, 0x0, 0x2, 0xfa, 0x0, 0x0, 0xaf, 0x20,
    0x9, 0xf4, 0x0, 0x0, 0x4f, 0x80, 0xf, 0xd0,
    0x0, 0x0, 0xd, 0xe0,

    /* U+5F "_" */
    0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff,

    /* U+60 "`" */
    0x8, 0xff, 0x40, 0x0, 0x4, 0xef, 0x60, 0x0,
    0x1, 0xbf, 0x70,

    /* U+61 "a" */
    0x0, 0x6b, 0xef, 0xfd, 0x81, 0x0, 0x2e, 0xff,
    0xff, 0xff, 0xfe, 0x10, 0xe, 0xb5, 0x10, 0x27,
    0xff, 0xb0, 0x1, 0x0, 0x0, 0x0, 0x6f, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x1f, 0xf3, 0x0, 0x37,
    0xaa, 0xaa, 0xaf, 0xf4, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x6f, 0xf6, 0x10, 0x0, 0xf, 0xf4,
    0xbf, 0x90, 0x0, 0x0, 0xf, 0xf4, 0xcf, 0x80,
    0x0, 0x0, 0x5f, 0xf4, 0x8f, 0xe2, 0x0, 0x4,
    0xff, 0xf4, 0x1d, 0xff, 0xcb, 0xdf, 0xdf, 0xf4,
    0x1, 0x8d, 0xff, 0xd8, 0xe, 0xf4,

    /* U+62 "b" */
    0xdf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0x70, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xdf, 0x70, 0x7c, 0xfe, 0xc7,
    0x10, 0x0, 0xdf, 0x9d, 0xff, 0xff, 0xff, 0xe3,
    0x0, 0xdf, 0xff, 0xa3, 0x12, 0x6e, 0xfe, 0x20,
    0xdf, 0xf8, 0x0, 0x0, 0x1, 0xef, 0xa0, 0xdf,
    0xe0, 0x0, 0x0, 0x0, 0x6f, 0xf1, 0xdf, 0x90,
    0x0, 0x0, 0x0, 0x1f, 0xf3, 0xdf, 0x70, 0x0,
    0x0, 0x0, 0xf, 0xf5, 0xdf, 0x90, 0x0, 0x0,
    0x0, 0x1f, 0xf3, 0xdf, 0xe0, 0x0, 0x0, 0x0,
    0x6f, 0xf1, 0xdf, 0xf8, 0x0, 0x0, 0x2, 0xef,
    0xa0, 0xdf, 0xff, 0xa3, 0x12, 0x6e, 0xff, 0x20,
    0xdf, 0x8d, 0xff, 0xff, 0xff, 0xe3, 0x0, 0xdf,
    0x60, 0x7d, 0xfe, 0xc7, 0x10, 0x0,

    /* U+63 "c" */
    0x0, 0x3, 0xad, 0xfe, 0xc6, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0xc1, 0x9, 0xff, 0x93, 0x12,
    0x7f, 0xfa, 0x4f, 0xf6, 0x0, 0x0, 0x3, 0x91,
    0xaf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x70,
    0x0, 0x0, 0x0, 0x0, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x70, 0x0, 0x0, 0x0, 0x0,
    0xaf, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf6,
    0x0, 0x0, 0x2, 0x91, 0x9, 0xff, 0x93, 0x12,
    0x7f, 0xfa, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xc1,
    0x0, 0x3, 0xad, 0xfe, 0xc6, 0x0,

    /* U+64 "d" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2f, 0xf3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x2f, 0xf3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xf3, 0x0, 0x5, 0xbe, 0xfe, 0x92,
    0x2f, 0xf3, 0x1, 0xbf, 0xff, 0xff, 0xff, 0x7f,
    0xf3, 0xb, 0xff, 0x93, 0x12, 0x7f, 0xff, 0xf3,
    0x5f, 0xf6, 0x0, 0x0, 0x3, 0xff, 0xf3, 0xbf,
    0xc0, 0x0, 0x0, 0x0, 0x8f, 0xf3, 0xdf, 0x70,
    0x0, 0x0, 0x0, 0x3f, 0xf3, 0xff, 0x50, 0x0,
    0x0, 0x0, 0x1f, 0xf3, 0xdf, 0x70, 0x0, 0x0,
    0x0, 0x3f, 0xf3, 0xbf, 0xb0, 0x0, 0x0, 0x0,
    0x7f, 0xf3, 0x5f, 0xf5, 0x0, 0x0, 0x2, 0xff,
    0xf3, 0xb, 0xff, 0x71, 0x0, 0x5e, 0xff, 0xf3,
    0x1, 0xbf, 0xff, 0xef, 0xff, 0x7f, 0xf3, 0x0,
    0x5, 0xbe, 0xfe, 0xa3, 0xf, 0xf3,

    /* U+65 "e" */
    0x0, 0x5, 0xbe, 0xfd, 0xa3, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0xa, 0xfe, 0x61,
    0x2, 0x8f, 0xf7, 0x4, 0xff, 0x20, 0x0, 0x0,
    0x5f, 0xf1, 0xaf, 0x90, 0x0, 0x0, 0x0, 0xcf,
    0x6d, 0xfc, 0xbb, 0xbb, 0xbb, 0xbd, 0xf9, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xad, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0x60, 0x0, 0x0, 0x6,
    0x0, 0xa, 0xff, 0xa3, 0x11, 0x4b, 0xf8, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x3,
    0xad, 0xff, 0xd8, 0x10, 0x0,

    /* U+66 "f" */
    0x0, 0x0, 0x8d, 0xfe, 0xa1, 0x0, 0xb, 0xff,
    0xff, 0xf0, 0x0, 0x4f, 0xf5, 0x0, 0x30, 0x0,
    0x7f, 0xd0, 0x0, 0x0, 0x0, 0x7f, 0xc0, 0x0,
    0x0, 0xaf, 0xff, 0xff, 0xff, 0x70, 0x9e, 0xff,
    0xfe, 0xee, 0x60, 0x0, 0x7f, 0xd0, 0x0, 0x0,
    0x0, 0x7f, 0xd0, 0x0, 0x0, 0x0, 0x7f, 0xd0,
    0x0, 0x0, 0x0, 0x7f, 0xd0, 0x0, 0x0, 0x0,
    0x7f, 0xd0, 0x0, 0x0, 0x0, 0x7f, 0xd0, 0x0,
    0x0, 0x0, 0x7f, 0xd0, 0x0, 0x0, 0x0, 0x7f,
    0xd0, 0x0, 0x0, 0x0, 0x7f, 0xd0, 0x0, 0x0,
    0x0, 0x7f, 0xd0, 0x0, 0x0, 0x0, 0x7f, 0xd0,
    0x0, 0x0,

    /* U+67 "g" */
    0x0, 0x5, 0xbe, 0xfe, 0xa3, 0xd, 0xf5, 0x0,
    0xbf, 0xff, 0xff, 0xff, 0x8d, 0xf5, 0xb, 0xff,
    0x93, 0x12, 0x6e, 0xff, 0xf5, 0x4f, 0xf6, 0x0,
    0x0, 0x1, 0xef, 0xf5, 0xbf, 0xc0, 0x0, 0x0,
    0x0, 0x5f, 0xf5, 0xdf, 0x70, 0x0, 0x0, 0x0,
    0xf, 0xf5, 0xff, 0x50, 0x0, 0x0, 0x0, 0xe,
    0xf5, 0xdf, 0x70, 0x0, 0x0, 0x0, 0xf, 0xf5,
    0xaf, 0xc0, 0x0, 0x0, 0x0, 0x5f, 0xf5, 0x4f,
    0xf6, 0x0, 0x0, 0x1, 0xef, 0xf5, 0xa, 0xff,
    0xa3, 0x12, 0x6e, 0xff, 0xf5, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0x8f, 0xf5, 0x0, 0x5, 0xbe, 0xfe,
    0xa3, 0xf, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf1, 0x3, 0x10, 0x0, 0x0, 0x0, 0xaf,
    0xd0, 0xd, 0xf9, 0x52, 0x11, 0x4b, 0xff, 0x50,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xf7, 0x0, 0x0,
    0x28, 0xce, 0xff, 0xd9, 0x30, 0x0,

    /* U+68 "h" */
    0xdf, 0x70, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x70, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x70, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf7, 0x18, 0xdf, 0xfc, 0x70, 0x0, 0xdf,
    0xae, 0xff, 0xff, 0xff, 0xc0, 0xd, 0xff, 0xf8,
    0x31, 0x4a, 0xff, 0x90, 0xdf, 0xf5, 0x0, 0x0,
    0xa, 0xff, 0xd, 0xfc, 0x0, 0x0, 0x0, 0x3f,
    0xf2, 0xdf, 0x80, 0x0, 0x0, 0x1, 0xff, 0x3d,
    0xf7, 0x0, 0x0, 0x0, 0xf, 0xf4, 0xdf, 0x70,
    0x0, 0x0, 0x0, 0xff, 0x4d, 0xf7, 0x0, 0x0,
    0x0, 0xf, 0xf4, 0xdf, 0x70, 0x0, 0x0, 0x0,
    0xff, 0x4d, 0xf7, 0x0, 0x0, 0x0, 0xf, 0xf4,
    0xdf, 0x70, 0x0, 0x0, 0x0, 0xff, 0x4d, 0xf7,
    0x0, 0x0, 0x0, 0xf, 0xf4,

    /* U+69 "i" */
    0xb, 0xf6, 0x2f, 0xfd, 0xb, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0xd, 0xf7, 0xd, 0xf7, 0xd, 0xf7,
    0xd, 0xf7, 0xd, 0xf7, 0xd, 0xf7, 0xd, 0xf7,
    0xd, 0xf7, 0xd, 0xf7, 0xd, 0xf7, 0xd, 0xf7,
    0xd, 0xf7, 0xd, 0xf7,

    /* U+6A "j" */
    0x0, 0x0, 0x9, 0xf8, 0x0, 0x0, 0x1, 0xff,
    0xf0, 0x0, 0x0, 0xa, 0xf8, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0x90, 0x0, 0x0, 0xb, 0xf9, 0x0,
    0x0, 0x0, 0xbf, 0x90, 0x0, 0x0, 0xb, 0xf9,
    0x0, 0x0, 0x0, 0xbf, 0x90, 0x0, 0x0, 0xb,
    0xf9, 0x0, 0x0, 0x0, 0xbf, 0x90, 0x0, 0x0,
    0xb, 0xf9, 0x0, 0x0, 0x0, 0xbf, 0x90, 0x0,
    0x0, 0xb, 0xf9, 0x0, 0x0, 0x0, 0xbf, 0x90,
    0x0, 0x0, 0xb, 0xf9, 0x0, 0x0, 0x0, 0xbf,
    0x90, 0x0, 0x0, 0xb, 0xf9, 0x0, 0x0, 0x0,
    0xdf, 0x80, 0x4, 0x20, 0x5f, 0xf4, 0x0, 0xef,
    0xff, 0xfc, 0x0, 0x9, 0xef, 0xe9, 0x0, 0x0,

    /* U+6B "k" */
    0xdf, 0x70, 0x0, 0x0, 0x0, 0x0, 0xd, 0xf7,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0x70, 0x0,
    0x0, 0x0, 0x0, 0xd, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x70, 0x0, 0x0, 0x0, 0x0,
    0xd, 0xf7, 0x0, 0x0, 0x3, 0xef, 0xa0, 0xdf,
    0x70, 0x0, 0x3, 0xff, 0xb0, 0xd, 0xf7, 0x0,
    0x4, 0xff, 0xb0, 0x0, 0xdf, 0x70, 0x5, 0xff,
    0xb0, 0x0, 0xd, 0xf7, 0x6, 0xff, 0xb0, 0x0,
    0x0, 0xdf, 0x77, 0xff, 0xf1, 0x0, 0x0, 0xd,
    0xfe, 0xff, 0xff, 0xb0, 0x0, 0x0, 0xdf, 0xff,
    0x87, 0xff, 0x70, 0x0, 0xd, 0xff, 0x70, 0xa,
    0xff, 0x40, 0x0, 0xdf, 0x90, 0x0, 0xd, 0xfe,
    0x10, 0xd, 0xf7, 0x0, 0x0, 0x2e, 0xfc, 0x0,
    0xdf, 0x70, 0x0, 0x0, 0x4f, 0xf9, 0xd, 0xf7,
    0x0, 0x0, 0x0, 0x7f, 0xf5,

    /* U+6C "l" */
    0xdf, 0x7d, 0xf7, 0xdf, 0x7d, 0xf7, 0xdf, 0x7d,
    0xf7, 0xdf, 0x7d, 0xf7, 0xdf, 0x7d, 0xf7, 0xdf,
    0x7d, 0xf7, 0xdf, 0x7d, 0xf7, 0xdf, 0x7d, 0xf7,
    0xdf, 0x7d, 0xf7,

    /* U+6D "m" */
    0xdf, 0x61, 0x9d, 0xfe, 0xb5, 0x0, 0x7, 0xcf,
    0xfd, 0x80, 0x0, 0xdf, 0xaf, 0xff, 0xff, 0xff,
    0x92, 0xef, 0xff, 0xff, 0xfd, 0x10, 0xdf, 0xfe,
    0x60, 0x3, 0xcf, 0xff, 0xfa, 0x20, 0x17, 0xff,
    0xa0, 0xdf, 0xf3, 0x0, 0x0, 0xe, 0xff, 0xa0,
    0x0, 0x0, 0x8f, 0xf0, 0xdf, 0xc0, 0x0, 0x0,
    0x9, 0xff, 0x20, 0x0, 0x0, 0x2f, 0xf3, 0xdf,
    0x80, 0x0, 0x0, 0x7, 0xff, 0x0, 0x0, 0x0,
    0xf, 0xf4, 0xdf, 0x70, 0x0, 0x0, 0x6, 0xfe,
    0x0, 0x0, 0x0, 0xf, 0xf4, 0xdf, 0x70, 0x0,
    0x0, 0x6, 0xfe, 0x0, 0x0, 0x0, 0xf, 0xf4,
    0xdf, 0x70, 0x0, 0x0, 0x6, 0xfe, 0x0, 0x0,
    0x0, 0xf, 0xf4, 0xdf, 0x70, 0x0, 0x0, 0x6,
    0xfe, 0x0, 0x0, 0x0, 0xf, 0xf4, 0xdf, 0x70,
    0x0, 0x0, 0x6, 0xfe, 0x0, 0x0, 0x0, 0xf,
    0xf4, 0xdf, 0x70, 0x0, 0x0, 0x6, 0xfe, 0x0,
    0x0, 0x0, 0xf, 0xf4, 0xdf, 0x70, 0x0, 0x0,
    0x6, 0xfe, 0x0, 0x0, 0x0, 0xf, 0xf4,

    /* U+6E "n" */
    0xdf, 0x61, 0x8d, 0xff, 0xc7, 0x0, 0xd, 0xf9,
    0xff, 0xff, 0xff, 0xfc, 0x0, 0xdf, 0xff, 0x61,
    0x2, 0x8f, 0xf9, 0xd, 0xff, 0x40, 0x0, 0x0,
    0x9f, 0xf0, 0xdf, 0xc0, 0x0, 0x0, 0x3, 0xff,
    0x2d, 0xf8, 0x0, 0x0, 0x0, 0x1f, 0xf3, 0xdf,
    0x70, 0x0, 0x0, 0x0, 0xff, 0x4d, 0xf7, 0x0,
    0x0, 0x0, 0xf, 0xf4, 0xdf, 0x70, 0x0, 0x0,
    0x0, 0xff, 0x4d, 0xf7, 0x0, 0x0, 0x0, 0xf,
    0xf4, 0xdf, 0x70, 0x0, 0x0, 0x0, 0xff, 0x4d,
    0xf7, 0x0, 0x0, 0x0, 0xf, 0xf4, 0xdf, 0x70,
    0x0, 0x0, 0x0, 0xff, 0x40,

    /* U+6F "o" */
    0x0, 0x4, 0xad, 0xfe, 0xb5, 0x0, 0x0, 0x0,
    0xaf, 0xff, 0xff, 0xff, 0xc1, 0x0, 0xa, 0xff,
    0x93, 0x12, 0x7f, 0xfd, 0x0, 0x4f, 0xf6, 0x0,
    0x0, 0x3, 0xff, 0x80, 0xaf, 0xc0, 0x0, 0x0,
    0x0, 0x8f, 0xe0, 0xdf, 0x70, 0x0, 0x0, 0x0,
    0x3f, 0xf1, 0xff, 0x50, 0x0, 0x0, 0x0, 0x1f,
    0xf3, 0xdf, 0x70, 0x0, 0x0, 0x0, 0x3f, 0xf1,
    0xaf, 0xc0, 0x0, 0x0, 0x0, 0x8f, 0xe0, 0x4f,
    0xf6, 0x0, 0x0, 0x3, 0xff, 0x80, 0xa, 0xff,
    0x93, 0x12, 0x7f, 0xfd, 0x0, 0x0, 0x9f, 0xff,
    0xff, 0xff, 0xc1, 0x0, 0x0, 0x4, 0xad, 0xfe,
    0xb5, 0x0, 0x0,

    /* U+70 "p" */
    0xdf, 0x61, 0x8d, 0xfe, 0xc7, 0x10, 0x0, 0xdf,
    0x8e, 0xff, 0xff, 0xff, 0xe3, 0x0, 0xdf, 0xff,
    0x92, 0x0, 0x5d, 0xfe, 0x20, 0xdf, 0xf7, 0x0,
    0x0, 0x1, 0xef, 0xa0, 0xdf, 0xd0, 0x0, 0x0,
    0x0, 0x5f, 0xf1, 0xdf, 0x90, 0x0, 0x0, 0x0,
    0x1f, 0xf3, 0xdf, 0x70, 0x0, 0x0, 0x0, 0xf,
    0xf5, 0xdf, 0x90, 0x0, 0x0, 0x0, 0x1f, 0xf3,
    0xdf, 0xe0, 0x0, 0x0, 0x0, 0x6f, 0xf1, 0xdf,
    0xf8, 0x0, 0x0, 0x2, 0xef, 0xa0, 0xdf, 0xff,
    0xa3, 0x12, 0x6e, 0xff, 0x20, 0xdf, 0x9d, 0xff,
    0xff, 0xff, 0xe3, 0x0, 0xdf, 0x70, 0x7c, 0xfe,
    0xc7, 0x10, 0x0, 0xdf, 0x70, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0x70, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xdf, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0x70, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+71 "q" */
    0x0, 0x5, 0xbe, 0xfe, 0xa2, 0xf, 0xf3, 0x1,
    0xbf, 0xff, 0xff, 0xff, 0x6f, 0xf3, 0xb, 0xff,
    0x93, 0x12, 0x7f, 0xff, 0xf3, 0x5f, 0xf6, 0x0,
    0x0, 0x3, 0xff, 0xf3, 0xbf, 0xc0, 0x0, 0x0,
    0x0, 0x8f, 0xf3, 0xdf, 0x70, 0x0, 0x0, 0x0,
    0x3f, 0xf3, 0xff, 0x50, 0x0, 0x0, 0x0, 0x1f,
    0xf3, 0xdf, 0x70, 0x0, 0x0, 0x0, 0x3f, 0xf3,
    0xbf, 0xc0, 0x0, 0x0, 0x0, 0x8f, 0xf3, 0x5f,
    0xf6, 0x0, 0x0, 0x3, 0xff, 0xf3, 0xb, 0xff,
    0x93, 0x12, 0x7f, 0xff, 0xf3, 0x1, 0xbf, 0xff,
    0xff, 0xff, 0x7f, 0xf3, 0x0, 0x5, 0xbe, 0xfe,
    0x92, 0x2f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2f, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f,
    0xf3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xf3, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2f, 0xf3,

    /* U+72 "r" */
    0xdf, 0x61, 0x8d, 0xf0, 0xdf, 0x7e, 0xff, 0xf0,
    0xdf, 0xff, 0xb5, 0x40, 0xdf, 0xf7, 0x0, 0x0,
    0xdf, 0xd0, 0x0, 0x0, 0xdf, 0x90, 0x0, 0x0,
    0xdf, 0x70, 0x0, 0x0, 0xdf, 0x70, 0x0, 0x0,
    0xdf, 0x70, 0x0, 0x0, 0xdf, 0x70, 0x0, 0x0,
    0xdf, 0x70, 0x0, 0x0, 0xdf, 0x70, 0x0, 0x0,
    0xdf, 0x70, 0x0, 0x0,

    /* U+73 "s" */
    0x0, 0x18, 0xcf, 0xfe, 0xb7, 0x10, 0x2, 0xef,
    0xff, 0xff, 0xff, 0xb0, 0xb, 0xfe, 0x41, 0x2,
    0x6c, 0x30, 0xf, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xd9, 0x52, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xe9, 0x10, 0x0, 0x0, 0x47, 0xad, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xf3, 0x1, 0x0,
    0x0, 0x0, 0xf, 0xf4, 0xe, 0xc6, 0x20, 0x3,
    0xaf, 0xf1, 0x3f, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x1, 0x7b, 0xef, 0xfd, 0x92, 0x0,

    /* U+74 "t" */
    0x0, 0x7f, 0xd0, 0x0, 0x0, 0x0, 0x7f, 0xd0,
    0x0, 0x0, 0x0, 0x7f, 0xd0, 0x0, 0x0, 0xaf,
    0xff, 0xff, 0xff, 0x70, 0x9e, 0xff, 0xfe, 0xee,
    0x60, 0x0, 0x7f, 0xd0, 0x0, 0x0, 0x0, 0x7f,
    0xd0, 0x0, 0x0, 0x0, 0x7f, 0xd0, 0x0, 0x0,
    0x0, 0x7f, 0xd0, 0x0, 0x0, 0x0, 0x7f, 0xd0,
    0x0, 0x0, 0x0, 0x7f, 0xd0, 0x0, 0x0, 0x0,
    0x7f, 0xd0, 0x0, 0x0, 0x0, 0x7f, 0xe0, 0x0,
    0x0, 0x0, 0x4f, 0xf7, 0x1, 0x50, 0x0, 0xc,
    0xff, 0xff, 0xf1, 0x0, 0x0, 0x9e, 0xfd, 0x81,

    /* U+75 "u" */
    0xff, 0x60, 0x0, 0x0, 0x4, 0xff, 0xf, 0xf6,
    0x0, 0x0, 0x0, 0x4f, 0xf0, 0xff, 0x60, 0x0,
    0x0, 0x4, 0xff, 0xf, 0xf6, 0x0, 0x0, 0x0,
    0x4f, 0xf0, 0xff, 0x60, 0x0, 0x0, 0x4, 0xff,
    0xf, 0xf6, 0x0, 0x0, 0x0, 0x4f, 0xf0, 0xff,
    0x60, 0x0, 0x0, 0x4, 0xff, 0xe, 0xf6, 0x0,
    0x0, 0x0, 0x5f, 0xf0, 0xdf, 0x80, 0x0, 0x0,
    0x8, 0xff, 0xa, 0xfe, 0x0, 0x0, 0x1, 0xef,
    0xf0, 0x3f, 0xfb, 0x20, 0x4, 0xdf, 0xff, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0x9f, 0xf0, 0x0, 0x4b,
    0xef, 0xea, 0x32, 0xff, 0x0,

    /* U+76 "v" */
    0xd, 0xf9, 0x0, 0x0, 0x0, 0x1, 0xff, 0x30,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x7f, 0xc0, 0x0,
    0xef, 0x60, 0x0, 0x0, 0xd, 0xf5, 0x0, 0x8,
    0xfd, 0x0, 0x0, 0x5, 0xfe, 0x0, 0x0, 0x2f,
    0xf3, 0x0, 0x0, 0xbf, 0x80, 0x0, 0x0, 0xbf,
    0xa0, 0x0, 0x2f, 0xf1, 0x0, 0x0, 0x4, 0xff,
    0x10, 0x9, 0xfa, 0x0, 0x0, 0x0, 0xd, 0xf7,
    0x1, 0xff, 0x30, 0x0, 0x0, 0x0, 0x6f, 0xe0,
    0x7f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xff, 0x5d,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfe, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2f, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf1, 0x0,
    0x0, 0x0,

    /* U+77 "w" */
    0xaf, 0x80, 0x0, 0x0, 0x5, 0xff, 0x0, 0x0,
    0x0, 0xc, 0xf4, 0x5f, 0xd0, 0x0, 0x0, 0xb,
    0xff, 0x50, 0x0, 0x0, 0x2f, 0xe0, 0xe, 0xf3,
    0x0, 0x0, 0x1f, 0xff, 0xb0, 0x0, 0x0, 0x8f,
    0x80, 0x9, 0xf9, 0x0, 0x0, 0x7f, 0xbf, 0xf1,
    0x0, 0x0, 0xef, 0x20, 0x3, 0xfe, 0x0, 0x0,
    0xcf, 0x4b, 0xf7, 0x0, 0x4, 0xfc, 0x0, 0x0,
    0xdf, 0x40, 0x2, 0xfe, 0x5, 0xfc, 0x0, 0xa,
    0xf6, 0x0, 0x0, 0x7f, 0xa0, 0x8, 0xf8, 0x0,
    0xef, 0x20, 0xf, 0xf1, 0x0, 0x0, 0x2f, 0xf0,
    0xe, 0xf2, 0x0, 0x9f, 0x80, 0x5f, 0xb0, 0x0,
    0x0, 0xc, 0xf5, 0x4f, 0xc0, 0x0, 0x3f, 0xe0,
    0xbf, 0x50, 0x0, 0x0, 0x6, 0xfb, 0xaf, 0x60,
    0x0, 0xd, 0xf5, 0xfe, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xff, 0x0, 0x0, 0x7, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xfa, 0x0, 0x0, 0x1,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf4,
    0x0, 0x0, 0x0, 0xbf, 0xd0, 0x0, 0x0,

    /* U+78 "x" */
    0x1e, 0xf8, 0x0, 0x0, 0x4, 0xff, 0x40, 0x4f,
    0xf4, 0x0, 0x1, 0xef, 0x80, 0x0, 0x8f, 0xe1,
    0x0, 0xbf, 0xb0, 0x0, 0x0, 0xcf, 0xb0, 0x7f,
    0xe1, 0x0, 0x0, 0x1, 0xef, 0xaf, 0xf4, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xf7, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0x10, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x4, 0xff, 0x5e,
    0xf7, 0x0, 0x0, 0x1, 0xef, 0x80, 0x4f, 0xf3,
    0x0, 0x0, 0xcf, 0xc0, 0x0, 0x8f, 0xe1, 0x0,
    0x8f, 0xe1, 0x0, 0x0, 0xcf, 0xb0, 0x4f, 0xf4,
    0x0, 0x0, 0x2, 0xff, 0x80,

    /* U+79 "y" */
    0xd, 0xf9, 0x0, 0x0, 0x0, 0x1, 0xff, 0x30,
    0x6f, 0xf0, 0x0, 0x0, 0x0, 0x7f, 0xc0, 0x0,
    0xef, 0x60, 0x0, 0x0, 0xd, 0xf5, 0x0, 0x8,
    0xfd, 0x0, 0x0, 0x4, 0xfe, 0x0, 0x0, 0x2f,
    0xf4, 0x0, 0x0, 0xbf, 0x80, 0x0, 0x0, 0xbf,
    0xb0, 0x0, 0x2f, 0xf1, 0x0, 0x0, 0x4, 0xff,
    0x10, 0x8, 0xfa, 0x0, 0x0, 0x0, 0xd, 0xf8,
    0x0, 0xef, 0x30, 0x0, 0x0, 0x0, 0x6f, 0xe0,
    0x6f, 0xc0, 0x0, 0x0, 0x0, 0x0, 0xef, 0x6c,
    0xf5, 0x0, 0x0, 0x0, 0x0, 0x8, 0xfe, 0xfe,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xff, 0x30, 0x0, 0x0,
    0x0, 0xa4, 0x2, 0xcf, 0xb0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0xff, 0xe1, 0x0, 0x0, 0x0, 0x0,
    0x8d, 0xfe, 0x91, 0x0, 0x0, 0x0, 0x0,

    /* U+7A "z" */
    0xe, 0xff, 0xff, 0xff, 0xff, 0xf6, 0xd, 0xee,
    0xee, 0xee, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x1,
    0xef, 0xa0, 0x0, 0x0, 0x0, 0xc, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x8f, 0xf2, 0x0, 0x0, 0x0,
    0x5, 0xff, 0x40, 0x0, 0x0, 0x0, 0x2f, 0xf8,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xb0, 0x0, 0x0,
    0x0, 0xb, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x7f,
    0xf2, 0x0, 0x0, 0x0, 0x4, 0xff, 0x50, 0x0,
    0x0, 0x0, 0xe, 0xff, 0xee, 0xee, 0xee, 0xe8,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xf9,

    /* U+7B "{" */
    0x0, 0x3, 0xce, 0xf0, 0x2, 0xff, 0xfe, 0x0,
    0x8f, 0xe2, 0x0, 0xa, 0xfa, 0x0, 0x0, 0xbf,
    0x90, 0x0, 0xb, 0xf9, 0x0, 0x0, 0xbf, 0x90,
    0x0, 0xb, 0xf9, 0x0, 0x0, 0xbf, 0x90, 0x0,
    0xb, 0xf9, 0x0, 0x1, 0xef, 0x70, 0xa, 0xff,
    0xd1, 0x0, 0x9f, 0xfe, 0x20, 0x0, 0xd, 0xf8,
    0x0, 0x0, 0xbf, 0x90, 0x0, 0xb, 0xf9, 0x0,
    0x0, 0xbf, 0x90, 0x0, 0xb, 0xf9, 0x0, 0x0,
    0xbf, 0x90, 0x0, 0xb, 0xfa, 0x0, 0x0, 0x9f,
    0xe2, 0x0, 0x3, 0xff, 0xfe, 0x0, 0x4, 0xcf,
    0xf0,

    /* U+7C "|" */
    0x7f, 0xa7, 0xfa, 0x7f, 0xa7, 0xfa, 0x7f, 0xa7,
    0xfa, 0x7f, 0xa7, 0xfa, 0x7f, 0xa7, 0xfa, 0x7f,
    0xa7, 0xfa, 0x7f, 0xa7, 0xfa, 0x7f, 0xa7, 0xfa,
    0x7f, 0xa7, 0xfa, 0x7f, 0xa7, 0xfa, 0x7f, 0xa7,
    0xfa, 0x7f, 0xa0,

    /* U+7D "}" */
    0x8f, 0xd7, 0x0, 0x0, 0x8f, 0xff, 0x80, 0x0,
    0x0, 0xaf, 0xf0, 0x0, 0x0, 0x3f, 0xf1, 0x0,
    0x0, 0x2f, 0xf2, 0x0, 0x0, 0x2f, 0xf2, 0x0,
    0x0, 0x2f, 0xf2, 0x0, 0x0, 0x2f, 0xf2, 0x0,
    0x0, 0x2f, 0xf2, 0x0, 0x0, 0x2f, 0xf2, 0x0,
    0x0, 0xf, 0xf6, 0x0, 0x0, 0x8, 0xff, 0xf0,
    0x0, 0x9, 0xff, 0xf0, 0x0, 0x1f, 0xf5, 0x0,
    0x0, 0x2f, 0xf2, 0x0, 0x0, 0x2f, 0xf2, 0x0,
    0x0, 0x2f, 0xf2, 0x0, 0x0, 0x2f, 0xf2, 0x0,
    0x0, 0x2f, 0xf2, 0x0, 0x0, 0x2f, 0xf2, 0x0,
    0x0, 0x9f, 0xf0, 0x0, 0x8f, 0xff, 0xa0, 0x0,
    0x8f, 0xd8, 0x0, 0x0,

    /* U+7E "~" */
    0x3, 0xcf, 0xd5, 0x0, 0x0, 0xf8, 0xe, 0xfd,
    0xff, 0x90, 0x5, 0xf5, 0x6f, 0x60, 0x1b, 0xfe,
    0xbf, 0xe0, 0x8f, 0x0, 0x0, 0x6d, 0xfc, 0x20,

    /* U+B0 "°" */
    0x0, 0x0, 0x0, 0x0, 0x2, 0xbf, 0xfb, 0x20,
    0x1e, 0xc5, 0x5c, 0xe2, 0x9d, 0x0, 0x0, 0xda,
    0xd8, 0x0, 0x0, 0x7e, 0xd7, 0x0, 0x0, 0x7e,
    0xac, 0x0, 0x0, 0xcb, 0x3f, 0xa2, 0x2a, 0xf3,
    0x4, 0xef, 0xfe, 0x40, 0x0, 0x2, 0x20, 0x0,

    /* U+2022 "•" */
    0x1, 0x64, 0x1, 0xef, 0xf7, 0x5f, 0xff, 0xd4,
    0xff, 0xfc, 0x8, 0xfd, 0x20,

    /* U+F001 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x7b, 0xe8,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x49, 0xdf, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x16, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x8c, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x3a,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0xaf, 0xff,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd8, 0x30, 0xf, 0xff, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xfb, 0x62, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0x0, 0xff, 0xfe, 0x94, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0x0, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0x0, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0x0, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0x0, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0x0, 0x0, 0x0, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0x0, 0x0, 0x0, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x3a, 0xdf, 0xef, 0xff, 0x0, 0x0, 0x0, 0xff,
    0xf0, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x0, 0x0, 0xff, 0xf0, 0x0, 0x0, 0xe,
    0xff, 0xff, 0xff, 0xff, 0x3, 0xad, 0xfe, 0xff,
    0xf0, 0x0, 0x0, 0xe, 0xff, 0xff, 0xff, 0xfe,
    0x6f, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0xff, 0xf6, 0xef, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x3a, 0xef, 0xea, 0x30,
    0xef, 0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0xae, 0xfe, 0xa3, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0,

    /* U+F008 "" */
    0xb7, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x7b, 0xfd, 0x88, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x88, 0xdf,
    0xff, 0xff, 0xff, 0xb4, 0x44, 0x44, 0x44, 0x44,
    0x5f, 0xff, 0xff, 0xff, 0xf9, 0x0, 0xcf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf5, 0x0, 0x9f,
    0xf8, 0x0, 0xbf, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf4, 0x0, 0x8f, 0xf9, 0x0, 0xcf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf5, 0x0, 0x9f,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xfd, 0x88, 0xef, 0xa2,
    0x22, 0x22, 0x22, 0x22, 0x3f, 0xfb, 0x88, 0xdf,
    0xf8, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x8f, 0xf8, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x8f,
    0xfd, 0x88, 0xef, 0xa2, 0x22, 0x22, 0x22, 0x22,
    0x3f, 0xfb, 0x88, 0xdf, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0xcf, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf5, 0x0, 0x9f, 0xf8, 0x0, 0xbf, 0x80,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xf4, 0x0, 0x8f,
    0xf9, 0x0, 0xcf, 0x80, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xf5, 0x0, 0x9f, 0xff, 0xff, 0xff, 0xb4,
    0x44, 0x44, 0x44, 0x44, 0x5f, 0xff, 0xff, 0xff,
    0xfd, 0x88, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x88, 0xdf, 0xb7, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x7b,

    /* U+F00B "" */
    0x14, 0x44, 0x44, 0x10, 0x3, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x41, 0xef, 0xff, 0xff, 0xe0,
    0x6f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x7b, 0xbb, 0xbb, 0x60, 0x2a, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xb7, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xb0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xf0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xbf, 0xff, 0xff, 0xb0,
    0x4f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x14, 0x44, 0x44, 0x10,
    0x3, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x41,
    0xef, 0xff, 0xff, 0xe0, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xf0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x7b, 0xbb, 0xbb, 0x60,
    0x2a, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xb7,

    /* U+F00C "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xbd, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xe2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xfd,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xfd, 0x10,
    0x2, 0xdb, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xff, 0xd1, 0x0, 0x2e, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xfd, 0x10, 0x0,
    0xdf, 0xff, 0xfc, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xff, 0xd1, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xc0,
    0x0, 0xc, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0,
    0x1d, 0xff, 0xff, 0xfc, 0x0, 0xcf, 0xff, 0xff,
    0xd1, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xff,
    0xcc, 0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf, 0xff,
    0xff, 0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1d, 0xff, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0xdf,
    0xff, 0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1d, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xcc, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F00D "" */
    0x1, 0x41, 0x0, 0x0, 0x0, 0x0, 0x3, 0x30,
    0x1, 0xdf, 0xd2, 0x0, 0x0, 0x0, 0x7, 0xff,
    0x70, 0xcf, 0xff, 0xe2, 0x0, 0x0, 0x8, 0xff,
    0xff, 0x4e, 0xff, 0xff, 0xe2, 0x0, 0x8, 0xff,
    0xff, 0xf6, 0x4f, 0xff, 0xff, 0xe2, 0x8, 0xff,
    0xff, 0xfb, 0x0, 0x4f, 0xff, 0xff, 0xe9, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xe2, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xe2, 0x0, 0x8, 0xff,
    0xff, 0xfb, 0x5f, 0xff, 0xff, 0xe2, 0x7, 0xff,
    0xff, 0xfb, 0x0, 0x4f, 0xff, 0xff, 0xe1, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x4f, 0xff, 0xff, 0x7a,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xf3,
    0xb, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xf4,
    0x0, 0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F011 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0x60, 0x0, 0xef, 0xfd, 0x0,
    0x7, 0x70, 0x0, 0x0, 0x0, 0x1, 0xcf, 0xf3,
    0x0, 0xef, 0xfd, 0x0, 0x5f, 0xfb, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xfb, 0x0, 0xef, 0xfd, 0x0,
    0xcf, 0xff, 0xb0, 0x0, 0x0, 0x9f, 0xff, 0xf6,
    0x0, 0xef, 0xfd, 0x0, 0x7f, 0xff, 0xf7, 0x0,
    0x3, 0xff, 0xff, 0x60, 0x0, 0xef, 0xfd, 0x0,
    0x8, 0xff, 0xff, 0x20, 0xa, 0xff, 0xf9, 0x0,
    0x0, 0xef, 0xfd, 0x0, 0x0, 0xbf, 0xff, 0x90,
    0x1f, 0xff, 0xe0, 0x0, 0x0, 0xef, 0xfd, 0x0,
    0x0, 0x1f, 0xff, 0xf0, 0x5f, 0xff, 0x90, 0x0,
    0x0, 0xef, 0xfd, 0x0, 0x0, 0xa, 0xff, 0xf3,
    0x8f, 0xff, 0x40, 0x0, 0x0, 0xef, 0xfd, 0x0,
    0x0, 0x6, 0xff, 0xf6, 0x9f, 0xff, 0x20, 0x0,
    0x0, 0xef, 0xfd, 0x0, 0x0, 0x4, 0xff, 0xf8,
    0x9f, 0xff, 0x20, 0x0, 0x0, 0xef, 0xfc, 0x0,
    0x0, 0x4, 0xff, 0xf7, 0x8f, 0xff, 0x40, 0x0,
    0x0, 0x37, 0x72, 0x0, 0x0, 0x6, 0xff, 0xf6,
    0x5f, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xf4, 0x1f, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xf0,
    0xb, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xa0, 0x3, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0x20,
    0x0, 0xaf, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x1,
    0x9f, 0xff, 0xf8, 0x0, 0x0, 0xd, 0xff, 0xff,
    0xe8, 0x42, 0x24, 0x9e, 0xff, 0xff, 0xb0, 0x0,
    0x0, 0x1, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x10, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3b, 0xff, 0xff, 0xff, 0xff,
    0xb3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x15, 0x89, 0x97, 0x51, 0x0, 0x0, 0x0, 0x0,

    /* U+F013 "" */
    0x0, 0x0, 0x0, 0x0, 0x4, 0x89, 0x98, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4f, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xc3, 0x2c, 0xff, 0xff, 0xff, 0xff,
    0xc2, 0x3c, 0xc0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x3, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x30, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0x94, 0x49, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x6, 0xef, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xfe, 0x60,
    0x0, 0x1e, 0xff, 0xff, 0x90, 0x0, 0x0, 0x9,
    0xff, 0xff, 0xe1, 0x0, 0x0, 0xf, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x4, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0x40, 0x0, 0x0, 0x4,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x1e, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x9, 0xff, 0xff, 0xe1, 0x0,
    0x6, 0xef, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xfe, 0x60, 0xf, 0xff, 0xff, 0xff,
    0xff, 0x94, 0x49, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xa, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xa0, 0x3, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x30,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0xc, 0xc3, 0x2c,
    0xff, 0xff, 0xff, 0xff, 0xc2, 0x3c, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0x89, 0x98, 0x40, 0x0, 0x0, 0x0, 0x0,

    /* U+F015 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x13, 0x10,
    0x0, 0x2, 0x44, 0x20, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5f, 0xfe, 0x40, 0x0, 0xdf,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0x70, 0xe, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xa0, 0xef, 0xfe, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0xcf, 0xff, 0xb3, 0xbf, 0xff, 0xce,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xef,
    0xff, 0x90, 0x0, 0x9f, 0xff, 0xff, 0xfe, 0x0,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xff, 0x60, 0x5d,
    0x50, 0x6f, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xfe, 0x30, 0x8f, 0xff, 0x70, 0x4e,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x1b, 0xff, 0xfd,
    0x20, 0xaf, 0xff, 0xff, 0xa0, 0x2d, 0xff, 0xfb,
    0x0, 0x0, 0x2d, 0xff, 0xfb, 0x1, 0xcf, 0xff,
    0xff, 0xff, 0xc1, 0xb, 0xff, 0xfd, 0x20, 0x4f,
    0xff, 0xf8, 0x3, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xe3, 0x8, 0xff, 0xfe, 0x4e, 0xff, 0xf5, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x5,
    0xff, 0xfe, 0x4f, 0xe3, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x3, 0xef, 0x40,
    0x41, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf7, 0x1, 0x40, 0x0, 0x0, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff,
    0xeb, 0xbb, 0xef, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x8f, 0xff, 0xff, 0xf4, 0x0, 0x4,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x4f, 0xff, 0xff,
    0xf8, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xf4, 0x0, 0x4, 0xff, 0xff, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x4f, 0xff, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x7f, 0xff, 0xff, 0xf3, 0x0, 0x3, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x3, 0xbb, 0xbb,
    0xba, 0x10, 0x0, 0x1a, 0xbb, 0xbb, 0xb3, 0x0,
    0x0,

    /* U+F019 "" */
    0x0, 0x0, 0x0, 0x0, 0x9, 0xdd, 0xdd, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xad, 0xdd,
    0xdf, 0xff, 0xff, 0xfd, 0xdd, 0xda, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x2d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xff, 0xff, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0xdf, 0xff, 0xff, 0xfd,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1d, 0xff, 0xff, 0xd1, 0x0, 0x0, 0x0, 0x0,
    0x25, 0x55, 0x55, 0x55, 0x11, 0xdf, 0xfd, 0x11,
    0x55, 0x55, 0x55, 0x52, 0xef, 0xff, 0xff, 0xff,
    0xd1, 0x1d, 0xd1, 0x1d, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x1, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf9, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x71, 0xe7, 0x1e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb6, 0xfb, 0x6f, 0xff, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F01C "" */
    0x0, 0x0, 0x3, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe1, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x5f, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0x50, 0x0, 0x0, 0x1e,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xfe, 0x10, 0x0, 0xa, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef, 0xfa,
    0x0, 0x5, 0xff, 0xf4, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4, 0xff, 0xf5, 0x1, 0xef,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xe1, 0xaf, 0xfe, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x0, 0x0, 0x2f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x0, 0xa, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd3, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd3,

    /* U+F021 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0x32, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x67, 0x75, 0x20, 0x0, 0x0, 0x4f, 0xff,
    0x0, 0x0, 0x0, 0x29, 0xff, 0xff, 0xff, 0xfd,
    0x70, 0x0, 0x4f, 0xff, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x60, 0x3f, 0xff,
    0x0, 0x1, 0xcf, 0xff, 0xff, 0xfe, 0xef, 0xff,
    0xff, 0xfb, 0x3f, 0xff, 0x0, 0xd, 0xff, 0xff,
    0xb4, 0x0, 0x0, 0x5b, 0xff, 0xff, 0xdf, 0xff,
    0x0, 0xaf, 0xff, 0xe4, 0x0, 0x0, 0x0, 0x0,
    0x4d, 0xff, 0xff, 0xff, 0x4, 0xff, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xff,
    0xc, 0xff, 0xf2, 0x0, 0x0, 0x0, 0x6, 0xff,
    0xee, 0xef, 0xff, 0xff, 0x1f, 0xff, 0x80, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x5f, 0xff, 0x10, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x4, 0x43, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x44, 0x44, 0x44, 0x44, 0x43,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x34, 0x44, 0x44, 0x44, 0x44, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x34, 0x40, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x0, 0x0, 0x0, 0x1, 0xff, 0xf5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xf1, 0xff, 0xff, 0xfd, 0xee,
    0xff, 0x60, 0x0, 0x0, 0x0, 0x2f, 0xff, 0xb0,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xdf, 0xff, 0x40, 0xff, 0xff, 0xff, 0xd3,
    0x0, 0x0, 0x0, 0x0, 0x4e, 0xff, 0xfa, 0x0,
    0xff, 0xfe, 0xff, 0xff, 0xb5, 0x0, 0x0, 0x4b,
    0xff, 0xff, 0xd0, 0x0, 0xff, 0xf3, 0xbf, 0xff,
    0xff, 0xfe, 0xef, 0xff, 0xff, 0xfc, 0x10, 0x0,
    0xff, 0xf3, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0xff, 0xf4, 0x0, 0x17,
    0xdf, 0xff, 0xff, 0xff, 0xa2, 0x0, 0x0, 0x0,
    0xff, 0xf4, 0x0, 0x0, 0x2, 0x67, 0x76, 0x30,
    0x0, 0x0, 0x0, 0x0, 0x23, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F026 "" */
    0x0, 0x0, 0x0, 0x0, 0x1, 0xa7, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0x0, 0x0, 0x0, 0x1,
    0xcf, 0xff, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xff,
    0x14, 0x44, 0x44, 0xcf, 0xff, 0xff, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x7b, 0xbb,
    0xbb, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x5f,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x5, 0xff, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x10,

    /* U+F027 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x96, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xff, 0x0, 0x0, 0x0, 0x13, 0x33, 0x33, 0xcf,
    0xff, 0xff, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0x4a, 0x20, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0xcf, 0xe1, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x3e, 0xfa,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x6,
    0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x5, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x2e, 0xfa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xcf, 0xf2, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x5b, 0x30, 0x7c, 0xcc, 0xcc,
    0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xff, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xff, 0xff, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xfd, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x21,
    0x0, 0x0, 0x0,

    /* U+F028 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7, 0xff, 0xd1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1a, 0x70, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1c, 0xff, 0x0, 0x0, 0x6, 0x30,
    0x5, 0xff, 0xa0, 0x0, 0x0, 0x0, 0x0, 0x1c,
    0xff, 0xf0, 0x0, 0x3, 0xff, 0x70, 0x8, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x1c, 0xff, 0xff, 0x0,
    0x0, 0xc, 0xff, 0x70, 0xc, 0xfc, 0x1, 0x44,
    0x44, 0x4c, 0xff, 0xff, 0xf0, 0x0, 0x0, 0xa,
    0xff, 0x30, 0x4f, 0xf3, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x5a, 0x20, 0xc, 0xfb, 0x0,
    0xdf, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0xc, 0xfe, 0x20, 0x4f, 0xf1, 0x8, 0xfb, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0x2e, 0xfa,
    0x0, 0xff, 0x50, 0x5f, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x6f, 0xe0, 0xc, 0xf7,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0x6, 0xfe, 0x0, 0xcf, 0x70, 0x4f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x2, 0xef,
    0xa0, 0xf, 0xf5, 0x6, 0xfe, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xcf, 0xe2, 0x4, 0xff,
    0x10, 0x9f, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x4, 0xa2, 0x0, 0xcf, 0xb0, 0xd, 0xf8,
    0x7b, 0xbb, 0xbb, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0xaf, 0xf3, 0x4, 0xff, 0x30, 0x0, 0x0,
    0x5, 0xff, 0xff, 0xf0, 0x0, 0x0, 0xcf, 0xf7,
    0x0, 0xcf, 0xc0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0x0, 0x0, 0x3f, 0xf7, 0x0, 0x6f, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xff, 0xf0, 0x0,
    0x0, 0x53, 0x0, 0x4f, 0xfa, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x5, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x7f, 0xfd, 0x10,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x9, 0xfb, 0x10, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x3, 0x0, 0x0, 0x0,

    /* U+F03E "" */
    0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xd3, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0xff, 0xff, 0xca, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0, 0x1d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x4, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0, 0x2,
    0xff, 0xff, 0xff, 0xff, 0x9f, 0xff, 0xff, 0xff,
    0xff, 0xe1, 0x0, 0x8, 0xff, 0xff, 0xff, 0xf4,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x42, 0x8f,
    0xff, 0xff, 0xff, 0x40, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0x6b,
    0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0xbf, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0x40, 0x0,
    0xb, 0x40, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x3d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd3,

    /* U+F048 "" */
    0x34, 0x40, 0x0, 0x0, 0x0, 0x0, 0x4, 0xf,
    0xff, 0x50, 0x0, 0x0, 0x0, 0x1d, 0xfb, 0xff,
    0xf6, 0x0, 0x0, 0x0, 0x2d, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x3f, 0xff, 0xff, 0xff, 0xff, 0x60,
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf6, 0x1d, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x60, 0x1c, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xea, 0xbb, 0x30,
    0x0, 0x0, 0x0, 0x5, 0xb4,

    /* U+F04B "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3, 0xdf, 0x91, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xe6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xfc, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xa1, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x40, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xa2, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x50,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xb2, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb, 0x20,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe5, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0x0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x20, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd4, 0x0, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xfa, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xd3, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xd, 0xff, 0xfe, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3d, 0xf9, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F04C "" */
    0x19, 0xcc, 0xcc, 0xc9, 0x10, 0x0, 0x19, 0xcc,
    0xcc, 0xc9, 0x1b, 0xff, 0xff, 0xff, 0xfb, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xfe, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xfe, 0x5f, 0xff, 0xff, 0xff, 0x50, 0x0,
    0x5f, 0xff, 0xff, 0xff, 0x50, 0x13, 0x33, 0x33,
    0x10, 0x0, 0x0, 0x13, 0x33, 0x33, 0x10,

    /* U+F04D "" */
    0x1, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x41, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf6, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xef,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb1, 0x8b, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x81,

    /* U+F051 "" */
    0x4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x44, 0x3b,
    0xfd, 0x10, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xfd, 0x20, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xfe, 0x30, 0x0, 0x0, 0x6f, 0xff, 0xff, 0xff,
    0xff, 0x40, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x50, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x60, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x86, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x8f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x16, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x6f, 0xff,
    0xff, 0xff, 0xff, 0xfb, 0x0, 0x6, 0xff, 0xff,
    0xff, 0xff, 0xfa, 0x0, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x6f, 0xff, 0xef, 0xf6,
    0x0, 0x0, 0x0, 0x6, 0xff, 0xf4, 0xb5, 0x0,
    0x0, 0x0, 0x0, 0x3b, 0xba,

    /* U+F052 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x37, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5f, 0xff, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff,
    0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2f, 0xff, 0xff, 0xff, 0xfe, 0x20, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x2e, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x20, 0x0, 0x0, 0x0, 0x0, 0x1d,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x0,
    0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfa, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0xd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x3d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfd,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x0,
    0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfb, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xe, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x0, 0x4b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0x40,

    /* U+F053 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xb0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0x80, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xfa, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf8, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0,

    /* U+F054 "" */
    0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xa0,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xa0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xff, 0xa0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xa0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xf8, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xfa, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xfa, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xfa,
    0x0, 0x0, 0x0, 0x8, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xfa, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F067 "" */
    0x0, 0x0, 0x0, 0x0, 0x3, 0x43, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xcf, 0xff,
    0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xc, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xcf, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd1, 0x77, 0x77, 0x77, 0x7d, 0xff, 0xfd, 0x77,
    0x77, 0x77, 0x71, 0x0, 0x0, 0x0, 0x0, 0xcf,
    0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xc, 0xff, 0xfc, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xcf, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xc, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xcf, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xab, 0xa2, 0x0, 0x0, 0x0, 0x0,

    /* U+F068 "" */
    0x16, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77, 0x77,
    0x77, 0x76, 0x1d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x9f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,

    /* U+F06E "" */
    0x0, 0x0, 0x0, 0x0, 0x16, 0xad, 0xef, 0xed,
    0xa6, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xb2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xe9, 0x54, 0x59, 0xef, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x2d, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x9f, 0xff, 0xfd, 0x20, 0x0, 0x0, 0x2e,
    0xff, 0xff, 0x70, 0x0, 0x14, 0x20, 0x0, 0x7f,
    0xff, 0xfe, 0x20, 0x0, 0x1e, 0xff, 0xff, 0xb0,
    0x0, 0x6, 0xff, 0xc2, 0x0, 0xbf, 0xff, 0xfe,
    0x10, 0xb, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x5f,
    0xff, 0xe2, 0x3, 0xff, 0xff, 0xfb, 0x6, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x1c, 0xff, 0xff, 0xa0,
    0xe, 0xff, 0xff, 0xf5, 0xef, 0xff, 0xff, 0xc0,
    0xd, 0xcf, 0xff, 0xff, 0xfe, 0x0, 0xcf, 0xff,
    0xff, 0xee, 0xff, 0xff, 0xfc, 0x0, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0xc, 0xff, 0xff, 0xfe, 0x5f,
    0xff, 0xff, 0xe0, 0xc, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0xef, 0xff, 0xff, 0x50, 0xbf, 0xff, 0xff,
    0x30, 0x4f, 0xff, 0xff, 0xff, 0x50, 0x3f, 0xff,
    0xff, 0xb0, 0x1, 0xdf, 0xff, 0xfb, 0x0, 0x7f,
    0xff, 0xff, 0x80, 0xb, 0xff, 0xff, 0xe1, 0x0,
    0x2, 0xef, 0xff, 0xf7, 0x0, 0x39, 0xb9, 0x30,
    0x7, 0xff, 0xff, 0xe2, 0x0, 0x0, 0x2, 0xcf,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x9, 0xff, 0xff,
    0xd2, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xfe,
    0x95, 0x45, 0x9e, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2a, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x1, 0x6a, 0xdf, 0xfe, 0xda, 0x71, 0x0,
    0x0, 0x0, 0x0,

    /* U+F070 "" */
    0x5, 0xe6, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xd,
    0xff, 0xfc, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xfe, 0x40, 0x0, 0x48, 0xce, 0xff, 0xeb,
    0x72, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0x88, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xef, 0xff, 0xff, 0xff, 0xb6, 0x45, 0x7d, 0xff,
    0xff, 0xb1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1,
    0xbf, 0xff, 0xfd, 0x20, 0x0, 0x0, 0x6, 0xff,
    0xff, 0xe4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xf6, 0x1, 0x76, 0x20, 0x4, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x10, 0x0,
    0x4e, 0xff, 0xfa, 0x1f, 0xff, 0x90, 0x8, 0xff,
    0xff, 0xf3, 0x0, 0x0, 0x0, 0x6e, 0x40, 0x0,
    0x2c, 0xff, 0xfd, 0xff, 0xff, 0x80, 0xf, 0xff,
    0xff, 0xe1, 0x0, 0x0, 0x2f, 0xff, 0x70, 0x0,
    0x9, 0xff, 0xff, 0xff, 0xff, 0x0, 0xbf, 0xff,
    0xff, 0x90, 0x0, 0x9, 0xff, 0xff, 0xb1, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xf3, 0x9, 0xff, 0xff,
    0xff, 0x10, 0x0, 0x9f, 0xff, 0xff, 0xd1, 0x0,
    0x3, 0xdf, 0xff, 0xff, 0x30, 0x9f, 0xff, 0xff,
    0xf1, 0x0, 0x2, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x1, 0xbf, 0xff, 0xf4, 0xb, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x7f, 0xff, 0xf7, 0xff, 0xff, 0xfd, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xf1, 0x0, 0x0,
    0x0, 0x4e, 0xff, 0xff, 0xff, 0xff, 0x20, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x1c, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xc2, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xfb, 0x64, 0x51,
    0x0, 0x5, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0x9f, 0xff, 0xff, 0xff, 0xe3,
    0x0, 0x2, 0xdf, 0xff, 0xc1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x5, 0x9d, 0xef, 0xfe, 0xa1,
    0x0, 0x0, 0xaf, 0xff, 0xe4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7f, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1b, 0xc0,

    /* U+F071 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1c, 0xfc,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xfa, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0xff, 0xff, 0xf4, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xdf, 0xff, 0xff, 0xd0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1e, 0xff, 0xff,
    0xff, 0xfe, 0x10, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xa4, 0x44, 0xaf, 0xff, 0xb0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0xff,
    0xf7, 0x0, 0x7, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xd, 0xff, 0xff, 0x80,
    0x0, 0x8f, 0xff, 0xfd, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xf9, 0x0, 0x9,
    0xff, 0xff, 0xf7, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0xff, 0xff, 0xff, 0xa0, 0x0, 0xaf, 0xff,
    0xff, 0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f,
    0xff, 0xff, 0xfb, 0x0, 0xb, 0xff, 0xff, 0xff,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0xcf, 0xff, 0xff, 0xff, 0x30,
    0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff, 0xfd,
    0x0, 0xd, 0xff, 0xff, 0xff, 0xfc, 0x0, 0x0,
    0x0, 0x5, 0xff, 0xff, 0xff, 0xff, 0xff, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0xef, 0xff, 0xff, 0xff, 0xfd, 0x10, 0x2d, 0xff,
    0xff, 0xff, 0xff, 0xe0, 0x0, 0x0, 0x7f, 0xff,
    0xff, 0xff, 0xff, 0x60, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x1f, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x10, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x26, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfa,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x3d, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0x30,

    /* U+F074 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x69, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xfc, 0x0, 0x9b, 0xbb, 0xbb, 0x30,
    0x0, 0x0, 0x0, 0x3b, 0xbb, 0xff, 0xff, 0xc0,
    0xff, 0xff, 0xff, 0xf3, 0x0, 0x0, 0x2, 0xef,
    0xff, 0xff, 0xff, 0xfb, 0xff, 0xff, 0xff, 0xfe,
    0x20, 0x0, 0x1e, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xff, 0xe1, 0x1, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xe3, 0x0, 0x0, 0x3e, 0xff,
    0xe2, 0xc, 0xff, 0xfe, 0x20, 0xff, 0xfe, 0x30,
    0x0, 0x0, 0x3, 0xff, 0x30, 0xaf, 0xff, 0xf3,
    0x0, 0xff, 0xe3, 0x0, 0x0, 0x0, 0x0, 0x54,
    0x9, 0xff, 0xff, 0x40, 0x0, 0xbe, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0xf6, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4f, 0xff, 0xf9, 0x4, 0x50,
    0x0, 0xbe, 0x30, 0x0, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xa0, 0x3f, 0xf3, 0x0, 0xff, 0xe3, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xfc, 0x2, 0xef, 0xfe,
    0x30, 0xff, 0xfe, 0x30, 0xff, 0xff, 0xff, 0xff,
    0xd1, 0x1, 0xef, 0xff, 0xff, 0xff, 0xff, 0xe3,
    0xff, 0xff, 0xff, 0xfe, 0x10, 0x0, 0x2e, 0xff,
    0xff, 0xff, 0xff, 0xfe, 0xff, 0xff, 0xff, 0xe2,
    0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x9b, 0xbb, 0xbb, 0x30, 0x0, 0x0, 0x0, 0x3b,
    0xbb, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xc0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x69, 0x0, 0x0,

    /* U+F077 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xfd, 0xff, 0xff, 0x90, 0x0, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xf9, 0xa, 0xff, 0xff, 0x90,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xf9, 0x0, 0xa,
    0xff, 0xff, 0x90, 0x0, 0x0, 0xaf, 0xff, 0xf9,
    0x0, 0x0, 0xa, 0xff, 0xff, 0x90, 0x0, 0xaf,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff,
    0x90, 0x9f, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x79, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xf8, 0xb, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xfa,
    0x0, 0x2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x0,

    /* U+F078 "" */
    0x0, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x20, 0x0, 0xbf, 0x90, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xaf, 0xa0, 0x9f, 0xff, 0x90,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x89,
    0xff, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0xaf,
    0xff, 0xf7, 0xa, 0xff, 0xff, 0x90, 0x0, 0x0,
    0x0, 0xaf, 0xff, 0xf9, 0x0, 0xa, 0xff, 0xff,
    0x90, 0x0, 0x0, 0xaf, 0xff, 0xf9, 0x0, 0x0,
    0xa, 0xff, 0xff, 0x90, 0x0, 0xaf, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0x90, 0xaf,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xdf, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xa, 0xff, 0xff, 0xf9,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0, 0x0,
    0x0, 0x0, 0x0,

    /* U+F079 "" */
    0x0, 0x0, 0x3, 0xd9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x3, 0xef, 0xfa, 0x0, 0x0, 0x34, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x10, 0x0, 0x0, 0x0,
    0x3, 0xef, 0xff, 0xfa, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0x3,
    0xef, 0xff, 0xff, 0xfa, 0x5, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x3, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x5, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbf, 0xff, 0x10, 0x0, 0x0, 0xef, 0xfe,
    0xdf, 0xfb, 0xff, 0xf8, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xef, 0xf1, 0x0, 0x0, 0xd, 0xfe, 0x2b,
    0xff, 0x48, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0xe, 0xff, 0x10, 0x0, 0x0, 0x17, 0x20, 0xbf,
    0xf4, 0x5, 0x50, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xef, 0xf1, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe,
    0xff, 0x10, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf4,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xef,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xe, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0x70, 0xef, 0xf1,
    0x4f, 0xc0, 0x0, 0x0, 0xb, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x3f, 0xff, 0x6e, 0xff, 0x5f,
    0xff, 0x60, 0x0, 0x0, 0xbf, 0xf7, 0x44, 0x44,
    0x44, 0x44, 0x20, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xd1, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x50, 0xbf, 0xff, 0xff, 0xff, 0xd1,
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0xbf, 0xff, 0xff, 0xd1, 0x0,
    0x0, 0x0, 0x4, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0x60, 0x0, 0xbf, 0xff, 0xd1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xbf, 0xd1, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x40, 0x0, 0x0, 0x0,

    /* U+F07B "" */
    0x3d, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd3,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfd, 0x3d, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd3,

    /* U+F093 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xbf, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xb0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff,
    0xb0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0xbf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x2, 0x22,
    0x2f, 0xff, 0xff, 0xf2, 0x22, 0x20, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x25, 0x55, 0x55, 0x52, 0xf, 0xff, 0xff, 0xf0,
    0x25, 0x55, 0x55, 0x52, 0xef, 0xff, 0xff, 0xf9,
    0xc, 0xff, 0xff, 0xc0, 0x9f, 0xff, 0xff, 0xfe,
    0xff, 0xff, 0xff, 0xff, 0x20, 0x1, 0x10, 0x2,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf9, 0x88, 0x88, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x71, 0xe7, 0x1e, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xb6, 0xfb, 0x6f, 0xff, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F095 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xd, 0xea, 0x63,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6, 0xff, 0xff, 0xff, 0x80, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4f, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xff, 0xff, 0xc0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9f, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xf2, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6f, 0xff,
    0xfc, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4, 0xff,
    0xff, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xdf, 0xff, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xbf,
    0xff, 0xfd, 0x0, 0x0, 0x0, 0x0, 0x1, 0x40,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0x30, 0x0,
    0x0, 0x0, 0x3a, 0xff, 0xa0, 0x0, 0x1, 0xbf,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x5, 0xcf, 0xff,
    0xff, 0x70, 0x3, 0xdf, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0xd, 0xff, 0xff, 0xff, 0xff, 0x6a, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0xa, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x70, 0x0, 0x0, 0x0, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfd, 0x30, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xe7, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xff, 0xfc, 0x60, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xfe, 0xc9,
    0x62, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F0C4 "" */
    0x0, 0x2, 0x42, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0xcf, 0xff, 0xc2, 0x0,
    0x0, 0x0, 0x0, 0x37, 0x73, 0x0, 0x2e, 0xff,
    0xff, 0xfe, 0x20, 0x0, 0x0, 0x8, 0xff, 0xff,
    0x90, 0xaf, 0xff, 0xcf, 0xff, 0xa0, 0x0, 0x0,
    0x9f, 0xff, 0xff, 0xb0, 0xef, 0xf4, 0x4, 0xff,
    0xe0, 0x0, 0x9, 0xff, 0xff, 0xfb, 0x0, 0xff,
    0xf1, 0x1, 0xff, 0xf0, 0x0, 0x9f, 0xff, 0xff,
    0xb0, 0x0, 0xcf, 0xfb, 0x5b, 0xff, 0xc0, 0x9,
    0xff, 0xff, 0xfb, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xf5, 0x9f, 0xff, 0xff, 0xb0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x39, 0xbe, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x2, 0xef, 0xff, 0xff, 0xfb, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xf5,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x49, 0xff,
    0xff, 0xff, 0xff, 0x50, 0x0, 0x0, 0x0, 0x2,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf5, 0x0,
    0x0, 0x0, 0x2e, 0xff, 0xff, 0xff, 0xfb, 0xef,
    0xff, 0xff, 0x50, 0x0, 0x0, 0xaf, 0xff, 0xcf,
    0xff, 0xd0, 0x2e, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0xef, 0xf4, 0x4, 0xff, 0xe0, 0x2, 0xef, 0xff,
    0xff, 0x50, 0x0, 0xff, 0xf1, 0x1, 0xff, 0xf0,
    0x0, 0x2e, 0xff, 0xff, 0xf5, 0x0, 0xcf, 0xfb,
    0x5b, 0xff, 0xc0, 0x0, 0x2, 0xef, 0xff, 0xff,
    0x50, 0x5f, 0xff, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x2e, 0xff, 0xff, 0xd0, 0x8, 0xff, 0xff, 0xf8,
    0x0, 0x0, 0x0, 0x1, 0xae, 0xea, 0x20, 0x0,
    0x39, 0xb9, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F0C5 "" */
    0x0, 0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xf0,
    0x7a, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xff, 0xff, 0x8, 0xfb, 0x0, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x8f, 0xfb, 0x0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0x8,
    0xff, 0xfa, 0x47, 0x88, 0x40, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x48, 0x88, 0x7f, 0xff, 0xf8, 0xf,
    0xff, 0xff, 0xff, 0xff, 0x40, 0x0, 0x0, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf8, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0xf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8,
    0xf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0xf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfc, 0x4, 0x78, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x74, 0xff, 0xff, 0xfa, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfb, 0x0, 0x0, 0x0,

    /* U+F0C7 "" */
    0x1, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x42,
    0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf8, 0x0, 0x0, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xf,
    0xff, 0xcb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbd, 0xff,
    0xf8, 0x0, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xf8, 0xf, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xff, 0xff, 0xf7, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xef, 0xff, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff,
    0x54, 0x44, 0x44, 0x44, 0x44, 0x45, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0xce, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0xc, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfa, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf2, 0x0,
    0x2, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf7, 0x57, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xb1, 0x8b, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0x81,

    /* U+F0E7 "" */
    0x0, 0x4f, 0xff, 0xff, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0xbf, 0xff, 0xff, 0xff, 0xf1, 0x0,
    0x0, 0x0, 0xd, 0xff, 0xff, 0xff, 0xfc, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0xff, 0x70,
    0x0, 0x0, 0x0, 0x1f, 0xff, 0xff, 0xff, 0xf2,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff, 0xff,
    0xc8, 0x88, 0x87, 0x30, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0xaf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xa0, 0xc, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf2, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0xf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x37,
    0x88, 0x88, 0xaf, 0xff, 0xff, 0x50, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xc0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xcf, 0xff, 0xf3, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x3, 0xff, 0xff, 0x10, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x7f, 0xff, 0x70, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xb, 0xff, 0xd0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3f, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x6, 0xff, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4f, 0x70,
    0x0, 0x0, 0x0, 0x0,

    /* U+F0EA "" */
    0x0, 0x0, 0x3, 0xdf, 0xd3, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x4, 0x78, 0x88, 0xff, 0xef, 0xf8,
    0x88, 0x74, 0x0, 0x0, 0x0, 0xff, 0xff, 0xff,
    0x90, 0x9f, 0xff, 0xff, 0xf0, 0x0, 0x0, 0xf,
    0xff, 0xff, 0xf9, 0x9, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0xff, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xf, 0xff, 0xff, 0xf4, 0x7, 0x88, 0x88,
    0x88, 0x3, 0x30, 0x0, 0xff, 0xff, 0xff, 0x7,
    0xff, 0xff, 0xff, 0xf0, 0x8f, 0x40, 0xf, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0x8, 0xff,
    0x40, 0xff, 0xff, 0xff, 0x8, 0xff, 0xff, 0xff,
    0xf0, 0x8f, 0xff, 0x4f, 0xff, 0xff, 0xf0, 0x8f,
    0xff, 0xff, 0xff, 0x8, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0x8, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0xf, 0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff,
    0xb8, 0x88, 0x88, 0xff, 0xff, 0xff, 0x8, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x78, 0x88, 0x80, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x0,
    0x0, 0x0, 0x7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xfb,

    /* U+F0F3 "" */
    0x0, 0x0, 0x0, 0x0, 0x8, 0xf8, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x6f, 0xff, 0x51, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xc, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0xa, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x4, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf3,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x90, 0x0, 0x0, 0xe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x1, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x0, 0x0, 0x4f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x0, 0xdf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xd0, 0x0, 0x5f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x3d, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0xdf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xd2,
    0x78, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88,
    0x88, 0x72, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x6f, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x1, 0xff, 0xff, 0xe1, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x3, 0xcf, 0xc3,
    0x0, 0x0, 0x0, 0x0,

    /* U+F11C "" */
    0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x3d, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfd, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x10, 0x1f, 0x90, 0x9, 0xf1, 0x1,
    0xf9, 0x0, 0x9f, 0x10, 0x1f, 0xff, 0xff, 0xf0,
    0x0, 0xf8, 0x0, 0x8f, 0x0, 0xf, 0x80, 0x8,
    0xf0, 0x0, 0xff, 0xff, 0xff, 0x10, 0x1f, 0x90,
    0x9, 0xf1, 0x1, 0xf9, 0x0, 0x9f, 0x10, 0x1f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xb8, 0x8f, 0xe8, 0x8b, 0xfb, 0x88, 0xfe,
    0x88, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0xcc, 0x0, 0x4f, 0x40, 0xc, 0xc0, 0x4, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x40, 0xc, 0xc0, 0x4,
    0xf4, 0x0, 0xcc, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x88, 0xfe, 0x88, 0xbf, 0xb8, 0x8f,
    0xe8, 0x8b, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf1, 0x1, 0xf9, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xf1, 0x1, 0xff, 0xff,
    0xff, 0x0, 0xf, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0x0, 0xf, 0xff, 0xff, 0xf1, 0x1,
    0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9, 0xf1,
    0x1, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xd3, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xd3,

    /* U+F124 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3a,
    0xfc, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x4b, 0xff, 0xff, 0xc0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5c,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x6e, 0xff, 0xff, 0xff, 0xff,
    0xa0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x1, 0x8e,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x30, 0x0, 0x0,
    0x0, 0x0, 0x2, 0x9f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0x0, 0x0, 0x0, 0x0, 0x3, 0xaf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4, 0x0,
    0x0, 0x0, 0x4, 0xcf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xc0, 0x0, 0x0, 0x6, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x3, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0xd,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf6, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x57,
    0x88, 0x88, 0x88, 0x8c, 0xff, 0xff, 0xff, 0xff,
    0x10, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xff, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xff, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xa0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0x30, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff, 0xfc,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff,
    0xff, 0xd0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0xff, 0x60, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x5, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xd3,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0,

    /* U+F15B "" */
    0xbf, 0xff, 0xff, 0xff, 0xff, 0x80, 0xe4, 0x0,
    0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80, 0xff,
    0x40, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0xff, 0xf4, 0x0, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x80, 0xff, 0xff, 0x40, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x80, 0xff, 0xff, 0xf4, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x80, 0xff, 0xff, 0xfe, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf8, 0x88, 0x88, 0x88,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xbf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfb,

    /* U+F1EB "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x3, 0x56, 0x77, 0x65,
    0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x2, 0x7c, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xc7, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5,
    0xcf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfc, 0x50, 0x0, 0x0, 0x0, 0x3, 0xdf, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xfd, 0x30, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff,
    0xc9, 0x65, 0x44, 0x56, 0x9c, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x1c, 0xff, 0xff, 0xfd, 0x61, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x16, 0xdf, 0xff, 0xff,
    0xc1, 0xdf, 0xff, 0xfe, 0x50, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x5, 0xef, 0xff, 0xfd,
    0x9f, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x9, 0xff, 0xf9, 0x9,
    0xf6, 0x0, 0x0, 0x0, 0x48, 0xcd, 0xff, 0xdc,
    0x84, 0x0, 0x0, 0x0, 0x6f, 0x90, 0x0, 0x10,
    0x0, 0x0, 0x6e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xe6, 0x0, 0x0, 0x1, 0x0, 0x0, 0x0, 0x0,
    0x3d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xd3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xff,
    0xff, 0xff, 0xfe, 0xcc, 0xef, 0xff, 0xff, 0xff,
    0x50, 0x0, 0x0, 0x0, 0x0, 0x8, 0xff, 0xff,
    0xc5, 0x10, 0x0, 0x1, 0x5c, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xe4, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4e, 0xfa, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6, 0x10, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x1, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x32,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1d, 0xff, 0xc1,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xbf, 0xff, 0xfb, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xff, 0xff, 0xff, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xdf, 0xff, 0xfd, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x4, 0xaa, 0x40, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F240 "" */
    0x1, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x30, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0xff, 0xfb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbd, 0xff, 0xa1, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xfe, 0xff, 0xf0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x8,
    0xff, 0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x5, 0xbf,
    0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x0, 0xf, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x0, 0xf, 0xff, 0xff,
    0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x2, 0x4f, 0xff, 0xff, 0xf0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x8, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xf4, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x4a, 0xff, 0xd7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x18, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xa4, 0x0,

    /* U+F241 "" */
    0x1, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x30, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0xff, 0xfb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbd, 0xff, 0xa1, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xfe, 0xff, 0xf0, 0xbf, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xf0, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf4, 0x0, 0x0, 0x5, 0xbf,
    0xff, 0xff, 0xf0, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf4, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf4, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xf0, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf4, 0x0, 0x0, 0x2, 0x4f, 0xff, 0xff, 0xf0,
    0xbf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf4,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xf4, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x4a, 0xff, 0xd7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x18, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xa4, 0x0,

    /* U+F242 "" */
    0x1, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x30, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0xff, 0xfb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbd, 0xff, 0xa1, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xfe, 0xff, 0xf0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xbf,
    0xff, 0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xf0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x4f, 0xff, 0xff, 0xf0,
    0x8f, 0xff, 0xff, 0xff, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xf4, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x4a, 0xff, 0xd7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x18, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xa4, 0x0,

    /* U+F243 "" */
    0x1, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x30, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0xff, 0xfb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbd, 0xff, 0xa1, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xfe, 0xff, 0xf0, 0xbf, 0xff, 0xff,
    0x40, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xf0, 0xbf, 0xff, 0xff, 0x40,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xbf,
    0xff, 0xff, 0xf0, 0xbf, 0xff, 0xff, 0x40, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf0, 0xbf, 0xff, 0xff, 0x40, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xf0, 0xbf, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x4f, 0xff, 0xff, 0xf0,
    0xbf, 0xff, 0xff, 0x40, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xf4, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x4a, 0xff, 0xd7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x18, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xa4, 0x0,

    /* U+F244 "" */
    0x1, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x30, 0x0, 0x6f,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfd, 0x0, 0xef, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0x60, 0xff, 0xfb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbd, 0xff, 0xa1, 0xff, 0xf0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x8, 0xff, 0xfe, 0xff, 0xf0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x5, 0xbf,
    0xff, 0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff,
    0xff, 0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xf, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x4f, 0xff, 0xff, 0xf0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xf0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xf4, 0x44, 0x44,
    0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44, 0x44,
    0x4a, 0xff, 0xd7, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x70, 0xbf, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x30, 0x18, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xa4, 0x0,

    /* U+F287 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2d,
    0xfe, 0x30, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x27, 0x7d, 0xff,
    0xfd, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x4f, 0xff, 0xff, 0xff,
    0xf0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xe, 0xf5, 0x3a, 0xff, 0xfb,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x7, 0xf6, 0x0, 0x9, 0xda, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x2, 0x0,
    0x0, 0x0, 0xed, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x9f, 0xfe, 0x50,
    0x0, 0x6f, 0x60, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0xa4, 0x0, 0x0, 0x8f, 0xff, 0xff, 0x30,
    0x1e, 0xe0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xf, 0xfa, 0x10, 0xe, 0xff, 0xff, 0xfd, 0xbe,
    0xfe, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xff, 0xff, 0x60, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xfb, 0xa, 0xff, 0xff, 0xf5, 0x0, 0x0,
    0x1c, 0xf2, 0x0, 0x0, 0x0, 0x0, 0x0, 0xff,
    0xd4, 0x0, 0x1c, 0xff, 0xf9, 0x0, 0x0, 0x0,
    0x2f, 0xa0, 0x0, 0x0, 0x0, 0x0, 0xe, 0x70,
    0x0, 0x0, 0x4, 0x63, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0x20, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0xfb, 0x0, 0x8f, 0xff, 0xf5, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xa,
    0xf8, 0x4b, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xb,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x3,
    0x5c, 0xff, 0xff, 0x60, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x9f, 0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x11, 0x10, 0x0, 0x0, 0x0, 0x0,

    /* U+F293 "" */
    0x0, 0x0, 0x2, 0x8c, 0xef, 0xfe, 0xa6, 0x0,
    0x0, 0x0, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff,
    0xfe, 0x30, 0x0, 0x0, 0xc, 0xff, 0xff, 0xf4,
    0xff, 0xff, 0xff, 0x40, 0x0, 0xa, 0xff, 0xff,
    0xff, 0x5, 0xff, 0xff, 0xfe, 0x10, 0x2, 0xff,
    0xff, 0xff, 0xf0, 0x5, 0xff, 0xff, 0xf8, 0x0,
    0x9f, 0xff, 0xff, 0xff, 0x0, 0x6, 0xff, 0xff,
    0xe0, 0xe, 0xff, 0xfc, 0xff, 0xf0, 0x16, 0x7,
    0xff, 0xff, 0x21, 0xff, 0xf8, 0x9, 0xff, 0x1,
    0xf6, 0x7, 0xff, 0xf5, 0x4f, 0xff, 0xd1, 0x9,
    0xf0, 0x1f, 0x70, 0x6f, 0xff, 0x86, 0xff, 0xff,
    0xd1, 0x7, 0x1, 0x70, 0x5f, 0xff, 0xf9, 0x7f,
    0xff, 0xff, 0xd1, 0x0, 0x0, 0x3f, 0xff, 0xff,
    0xa7, 0xff, 0xff, 0xff, 0xc1, 0x0, 0x2e, 0xff,
    0xff, 0xfb, 0x7f, 0xff, 0xff, 0xff, 0x30, 0x5,
    0xff, 0xff, 0xff, 0xb7, 0xff, 0xff, 0xff, 0x40,
    0x0, 0x6, 0xff, 0xff, 0xfa, 0x6f, 0xff, 0xff,
    0x40, 0x30, 0x3, 0x7, 0xff, 0xff, 0xa4, 0xff,
    0xff, 0x40, 0x4e, 0x1, 0xe2, 0x8, 0xff, 0xf8,
    0x2f, 0xff, 0x70, 0x4f, 0xf0, 0x1f, 0x90, 0x2f,
    0xff, 0x60, 0xef, 0xff, 0x7f, 0xff, 0x1, 0xb0,
    0x2e, 0xff, 0xf3, 0xa, 0xff, 0xff, 0xff, 0xf0,
    0x0, 0x2e, 0xff, 0xff, 0x0, 0x4f, 0xff, 0xff,
    0xff, 0x0, 0x2e, 0xff, 0xff, 0x90, 0x0, 0xbf,
    0xff, 0xff, 0xf0, 0x2e, 0xff, 0xff, 0xf2, 0x0,
    0x1, 0xdf, 0xff, 0xff, 0x3e, 0xff, 0xff, 0xf7,
    0x0, 0x0, 0x0, 0xaf, 0xff, 0xff, 0xff, 0xff,
    0xf7, 0x0, 0x0, 0x0, 0x0, 0x28, 0xce, 0xff,
    0xec, 0x82, 0x0, 0x0,

    /* U+F2ED "" */
    0x0, 0x0, 0x0, 0x1a, 0xcc, 0xcc, 0xca, 0x10,
    0x0, 0x0, 0x2, 0x44, 0x44, 0x49, 0xff, 0xff,
    0xff, 0xf9, 0x44, 0x44, 0x42, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0x9b, 0xbb, 0xbb, 0xbb, 0xbb, 0xbb,
    0xbb, 0xbb, 0xbb, 0xbb, 0x80, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x8,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf8, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x8, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x8f,
    0xff, 0x66, 0xff, 0xd1, 0xdf, 0xf6, 0x6f, 0xff,
    0x80, 0x8, 0xff, 0xf4, 0x4f, 0xfc, 0xc, 0xff,
    0x44, 0xff, 0xf8, 0x0, 0x8f, 0xff, 0x44, 0xff,
    0xc0, 0xcf, 0xf4, 0x4f, 0xff, 0x80, 0x8, 0xff,
    0xf4, 0x4f, 0xfc, 0xc, 0xff, 0x44, 0xff, 0xf8,
    0x0, 0x8f, 0xff, 0x44, 0xff, 0xc0, 0xcf, 0xf4,
    0x4f, 0xff, 0x80, 0x8, 0xff, 0xf4, 0x4f, 0xfc,
    0xc, 0xff, 0x44, 0xff, 0xf8, 0x0, 0x8f, 0xff,
    0x44, 0xff, 0xc0, 0xcf, 0xf4, 0x4f, 0xff, 0x80,
    0x8, 0xff, 0xf4, 0x4f, 0xfc, 0xc, 0xff, 0x44,
    0xff, 0xf8, 0x0, 0x8f, 0xff, 0x44, 0xff, 0xc0,
    0xcf, 0xf4, 0x4f, 0xff, 0x80, 0x8, 0xff, 0xf4,
    0x4f, 0xfc, 0xc, 0xff, 0x44, 0xff, 0xf8, 0x0,
    0x8f, 0xff, 0x44, 0xff, 0xc0, 0xcf, 0xf4, 0x4f,
    0xff, 0x80, 0x8, 0xff, 0xf6, 0x6f, 0xfd, 0x1d,
    0xff, 0x66, 0xff, 0xf8, 0x0, 0x7f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0x70, 0x5,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xf5, 0x0, 0x9, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xf9, 0x0,

    /* U+F304 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x6e, 0xe5, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xf6,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xf6, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x1f, 0xff, 0xff,
    0xff, 0xf5, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x50, 0x3f, 0xff, 0xff, 0xff, 0xe0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xb0, 0x3f,
    0xff, 0xff, 0xfe, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xb0, 0x3f, 0xff, 0xff, 0x60,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xb0, 0x3f, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xb0, 0x3f, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xb0, 0x10, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x50,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8f, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x5f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0xb, 0xff, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0xdf,
    0xff, 0xff, 0xff, 0x80, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0xf, 0xff, 0xff, 0xff, 0x80,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xbf, 0xdb, 0xa8, 0x50, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0,

    /* U+F55A "" */
    0x0, 0x0, 0x0, 0x2, 0xbf, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x80, 0x0,
    0x0, 0x0, 0x3e, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0, 0x0,
    0x3, 0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x0, 0x0, 0x3e,
    0xff, 0xff, 0xff, 0xff, 0xcf, 0xff, 0xff, 0xfc,
    0xff, 0xff, 0xff, 0xff, 0x0, 0x3, 0xef, 0xff,
    0xff, 0xff, 0xf8, 0x6, 0xff, 0xff, 0x60, 0x8f,
    0xff, 0xff, 0xff, 0x0, 0x3e, 0xff, 0xff, 0xff,
    0xff, 0xc0, 0x0, 0x6f, 0xf6, 0x0, 0xc, 0xff,
    0xff, 0xff, 0x3, 0xef, 0xff, 0xff, 0xff, 0xff,
    0xf6, 0x0, 0x6, 0x60, 0x0, 0x6f, 0xff, 0xff,
    0xff, 0x3f, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x60, 0x0, 0x0, 0x6, 0xff, 0xff, 0xff, 0xff,
    0xdf, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6,
    0x0, 0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0xdf,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf6, 0x0,
    0x0, 0x6f, 0xff, 0xff, 0xff, 0xff, 0x3f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0x60, 0x0, 0x0,
    0x6, 0xff, 0xff, 0xff, 0xff, 0x3, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xf6, 0x0, 0x6, 0x60, 0x0,
    0x6f, 0xff, 0xff, 0xff, 0x0, 0x3e, 0xff, 0xff,
    0xff, 0xff, 0xc0, 0x0, 0x6f, 0xf6, 0x0, 0xc,
    0xff, 0xff, 0xff, 0x0, 0x3, 0xef, 0xff, 0xff,
    0xff, 0xf8, 0x6, 0xff, 0xff, 0x60, 0x8f, 0xff,
    0xff, 0xff, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xff,
    0xff, 0xcf, 0xff, 0xff, 0xfc, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x0, 0x3, 0xef, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x0, 0x0, 0x0, 0x3e, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x0,
    0x0, 0x0, 0x2, 0xbf, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xfe, 0x80,

    /* U+F7C2 "" */
    0x0, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff, 0xfe,
    0x80, 0x0, 0x0, 0x8f, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xf8, 0x0, 0x8, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xfe, 0x0, 0x8f, 0xf4, 0x8, 0xf0,
    0xe, 0x90, 0xf, 0xff, 0x8, 0xff, 0xf4, 0x8,
    0xf0, 0xe, 0x90, 0xf, 0xff, 0x8f, 0xff, 0xf4,
    0x8, 0xf0, 0xe, 0x90, 0xf, 0xff, 0xff, 0xff,
    0xf4, 0x8, 0xf0, 0xe, 0x90, 0xf, 0xff, 0xff,
    0xff, 0xfa, 0x8c, 0xf8, 0x8f, 0xc8, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xef, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x8f, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xf8, 0x8,
    0xef, 0xff, 0xff, 0xff, 0xff, 0xff, 0xfe, 0x80,

    /* U+F8A2 "" */
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x2, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x7,
    0xf1, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x7, 0xff, 0x10, 0x0, 0x0,
    0x3, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x6,
    0xff, 0xf1, 0x0, 0x0, 0x1c, 0xf7, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x10, 0x0,
    0x1d, 0xff, 0x90, 0x0, 0x0, 0x0, 0x0, 0x0,
    0xa, 0xff, 0xf1, 0x0, 0x2d, 0xff, 0xf9, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0xaf, 0xff, 0x10,
    0x2e, 0xff, 0xff, 0xa2, 0x22, 0x22, 0x22, 0x22,
    0x22, 0x2b, 0xff, 0xf1, 0x3e, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0x1d, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xf1, 0x8f, 0xff, 0xff,
    0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff,
    0xff, 0x0, 0x8f, 0xff, 0xff, 0xd9, 0x99, 0x99,
    0x99, 0x99, 0x99, 0x99, 0x99, 0x60, 0x0, 0x7f,
    0xff, 0xf9, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x6f, 0xff, 0x90, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x5f, 0xf8, 0x0, 0x0, 0x0, 0x0, 0x0,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x4a, 0x20,
    0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0
};

/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 103, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 0, .adv_w = 103, .box_w = 4, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 34, .adv_w = 150, .box_w = 7, .box_h = 7, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 59, .adv_w = 270, .box_w = 17, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 204, .adv_w = 238, .box_w = 14, .box_h = 23, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 365, .adv_w = 324, .box_w = 20, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 535, .adv_w = 263, .box_w = 16, .box_h = 18, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 679, .adv_w = 81, .box_w = 3, .box_h = 7, .ofs_x = 1, .ofs_y = 10},
    {.bitmap_index = 690, .adv_w = 129, .box_w = 6, .box_h = 23, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 759, .adv_w = 130, .box_w = 6, .box_h = 23, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 828, .adv_w = 154, .box_w = 10, .box_h = 10, .ofs_x = 0, .ofs_y = 8},
    {.bitmap_index = 878, .adv_w = 223, .box_w = 12, .box_h = 11, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 944, .adv_w = 87, .box_w = 4, .box_h = 7, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 958, .adv_w = 147, .box_w = 7, .box_h = 3, .ofs_x = 1, .ofs_y = 6},
    {.bitmap_index = 969, .adv_w = 87, .box_w = 4, .box_h = 4, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 977, .adv_w = 135, .box_w = 11, .box_h = 23, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 1104, .adv_w = 256, .box_w = 14, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1223, .adv_w = 142, .box_w = 7, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1283, .adv_w = 220, .box_w = 13, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1394, .adv_w = 220, .box_w = 13, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1505, .adv_w = 257, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1641, .adv_w = 220, .box_w = 13, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1752, .adv_w = 237, .box_w = 14, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 1871, .adv_w = 230, .box_w = 14, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 1990, .adv_w = 247, .box_w = 14, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2109, .adv_w = 237, .box_w = 14, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2228, .adv_w = 87, .box_w = 4, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 2254, .adv_w = 87, .box_w = 4, .box_h = 17, .ofs_x = 1, .ofs_y = -4},
    {.bitmap_index = 2288, .adv_w = 223, .box_w = 12, .box_h = 11, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 2354, .adv_w = 223, .box_w = 12, .box_h = 8, .ofs_x = 1, .ofs_y = 5},
    {.bitmap_index = 2402, .adv_w = 223, .box_w = 12, .box_h = 11, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 2468, .adv_w = 220, .box_w = 13, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 2579, .adv_w = 397, .box_w = 23, .box_h = 22, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 2832, .adv_w = 281, .box_w = 19, .box_h = 17, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 2994, .adv_w = 291, .box_w = 15, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3122, .adv_w = 278, .box_w = 16, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3258, .adv_w = 317, .box_w = 17, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3403, .adv_w = 257, .box_w = 13, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3514, .adv_w = 244, .box_w = 13, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3625, .adv_w = 296, .box_w = 16, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 3761, .adv_w = 312, .box_w = 15, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3889, .adv_w = 119, .box_w = 3, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 3915, .adv_w = 197, .box_w = 11, .box_h = 17, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 4009, .adv_w = 276, .box_w = 16, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4145, .adv_w = 228, .box_w = 13, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4256, .adv_w = 367, .box_w = 19, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4418, .adv_w = 312, .box_w = 15, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4546, .adv_w = 323, .box_w = 19, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 4708, .adv_w = 277, .box_w = 15, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 4836, .adv_w = 323, .box_w = 19, .box_h = 20, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 5026, .adv_w = 279, .box_w = 15, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5154, .adv_w = 238, .box_w = 14, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5273, .adv_w = 225, .box_w = 14, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5392, .adv_w = 304, .box_w = 15, .box_h = 17, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 5520, .adv_w = 273, .box_w = 19, .box_h = 17, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 5682, .adv_w = 432, .box_w = 27, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 5912, .adv_w = 258, .box_w = 16, .box_h = 17, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 6048, .adv_w = 248, .box_w = 17, .box_h = 17, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 6193, .adv_w = 252, .box_w = 15, .box_h = 17, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6321, .adv_w = 128, .box_w = 6, .box_h = 23, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 6390, .adv_w = 135, .box_w = 11, .box_h = 23, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 6517, .adv_w = 128, .box_w = 6, .box_h = 23, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 6586, .adv_w = 224, .box_w = 12, .box_h = 10, .ofs_x = 1, .ofs_y = 3},
    {.bitmap_index = 6646, .adv_w = 192, .box_w = 12, .box_h = 2, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 6658, .adv_w = 230, .box_w = 7, .box_h = 3, .ofs_x = 2, .ofs_y = 15},
    {.bitmap_index = 6669, .adv_w = 230, .box_w = 12, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6747, .adv_w = 262, .box_w = 14, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 6873, .adv_w = 219, .box_w = 12, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 6951, .adv_w = 262, .box_w = 14, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7077, .adv_w = 235, .box_w = 13, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7162, .adv_w = 136, .box_w = 10, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 7252, .adv_w = 265, .box_w = 14, .box_h = 18, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 7378, .adv_w = 262, .box_w = 13, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7495, .adv_w = 107, .box_w = 4, .box_h = 18, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 7531, .adv_w = 109, .box_w = 9, .box_h = 23, .ofs_x = -3, .ofs_y = -5},
    {.bitmap_index = 7635, .adv_w = 237, .box_w = 13, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7752, .adv_w = 107, .box_w = 3, .box_h = 18, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7779, .adv_w = 406, .box_w = 22, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 7922, .adv_w = 262, .box_w = 13, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8007, .adv_w = 244, .box_w = 14, .box_h = 13, .ofs_x = 1, .ofs_y = 0},
    {.bitmap_index = 8098, .adv_w = 262, .box_w = 14, .box_h = 18, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 8224, .adv_w = 262, .box_w = 14, .box_h = 18, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 8350, .adv_w = 157, .box_w = 8, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8402, .adv_w = 192, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8480, .adv_w = 159, .box_w = 10, .box_h = 16, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8560, .adv_w = 260, .box_w = 13, .box_h = 13, .ofs_x = 2, .ofs_y = 0},
    {.bitmap_index = 8645, .adv_w = 215, .box_w = 15, .box_h = 13, .ofs_x = -1, .ofs_y = 0},
    {.bitmap_index = 8743, .adv_w = 345, .box_w = 22, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8886, .adv_w = 212, .box_w = 13, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 8971, .adv_w = 215, .box_w = 15, .box_h = 18, .ofs_x = -1, .ofs_y = -5},
    {.bitmap_index = 9106, .adv_w = 200, .box_w = 12, .box_h = 13, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9184, .adv_w = 135, .box_w = 7, .box_h = 23, .ofs_x = 1, .ofs_y = -5},
    {.bitmap_index = 9265, .adv_w = 115, .box_w = 3, .box_h = 23, .ofs_x = 2, .ofs_y = -5},
    {.bitmap_index = 9300, .adv_w = 135, .box_w = 8, .box_h = 23, .ofs_x = 0, .ofs_y = -5},
    {.bitmap_index = 9392, .adv_w = 223, .box_w = 12, .box_h = 4, .ofs_x = 1, .ofs_y = 7},
    {.bitmap_index = 9416, .adv_w = 161, .box_w = 8, .box_h = 10, .ofs_x = 1, .ofs_y = 9},
    {.bitmap_index = 9456, .adv_w = 121, .box_w = 5, .box_h = 5, .ofs_x = 1, .ofs_y = 4},
    {.bitmap_index = 9469, .adv_w = 384, .box_w = 24, .box_h = 25, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 9769, .adv_w = 384, .box_w = 24, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 9985, .adv_w = 384, .box_w = 24, .box_h = 22, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 10249, .adv_w = 384, .box_w = 24, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10465, .adv_w = 264, .box_w = 17, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 10618, .adv_w = 384, .box_w = 24, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 10906, .adv_w = 384, .box_w = 24, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11194, .adv_w = 432, .box_w = 27, .box_h = 22, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 11491, .adv_w = 384, .box_w = 24, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 11779, .adv_w = 432, .box_w = 27, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 12022, .adv_w = 384, .box_w = 24, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 12334, .adv_w = 192, .box_w = 12, .box_h = 19, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 12448, .adv_w = 288, .box_w = 18, .box_h = 19, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 12619, .adv_w = 432, .box_w = 27, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 12943, .adv_w = 384, .box_w = 24, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 13159, .adv_w = 336, .box_w = 15, .box_h = 22, .ofs_x = 3, .ofs_y = -2},
    {.bitmap_index = 13324, .adv_w = 336, .box_w = 21, .box_h = 26, .ofs_x = 0, .ofs_y = -4},
    {.bitmap_index = 13597, .adv_w = 336, .box_w = 21, .box_h = 22, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 13828, .adv_w = 336, .box_w = 21, .box_h = 22, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 14059, .adv_w = 336, .box_w = 15, .box_h = 22, .ofs_x = 3, .ofs_y = -2},
    {.bitmap_index = 14224, .adv_w = 336, .box_w = 23, .box_h = 22, .ofs_x = -1, .ofs_y = -2},
    {.bitmap_index = 14477, .adv_w = 240, .box_w = 13, .box_h = 22, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 14620, .adv_w = 240, .box_w = 13, .box_h = 22, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 14763, .adv_w = 336, .box_w = 21, .box_h = 22, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 14994, .adv_w = 336, .box_w = 21, .box_h = 6, .ofs_x = 0, .ofs_y = 6},
    {.bitmap_index = 15057, .adv_w = 432, .box_w = 27, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 15300, .adv_w = 480, .box_w = 31, .box_h = 24, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 15672, .adv_w = 432, .box_w = 29, .box_h = 24, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 16020, .adv_w = 384, .box_w = 24, .box_h = 22, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 16284, .adv_w = 336, .box_w = 21, .box_h = 14, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 16431, .adv_w = 336, .box_w = 21, .box_h = 14, .ofs_x = 0, .ofs_y = 2},
    {.bitmap_index = 16578, .adv_w = 480, .box_w = 31, .box_h = 19, .ofs_x = -1, .ofs_y = -1},
    {.bitmap_index = 16873, .adv_w = 384, .box_w = 24, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 17089, .adv_w = 384, .box_w = 24, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 17377, .adv_w = 384, .box_w = 25, .box_h = 25, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 17690, .adv_w = 336, .box_w = 22, .box_h = 22, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 17932, .adv_w = 336, .box_w = 21, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 18184, .adv_w = 336, .box_w = 21, .box_h = 22, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 18415, .adv_w = 240, .box_w = 17, .box_h = 24, .ofs_x = -1, .ofs_y = -3},
    {.bitmap_index = 18619, .adv_w = 336, .box_w = 21, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 18871, .adv_w = 336, .box_w = 21, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 19123, .adv_w = 432, .box_w = 27, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 19366, .adv_w = 384, .box_w = 26, .box_h = 26, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 19704, .adv_w = 288, .box_w = 18, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 19920, .adv_w = 480, .box_w = 30, .box_h = 23, .ofs_x = 0, .ofs_y = -2},
    {.bitmap_index = 20265, .adv_w = 480, .box_w = 30, .box_h = 16, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 20505, .adv_w = 480, .box_w = 30, .box_h = 16, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 20745, .adv_w = 480, .box_w = 30, .box_h = 16, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 20985, .adv_w = 480, .box_w = 30, .box_h = 16, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 21225, .adv_w = 480, .box_w = 30, .box_h = 16, .ofs_x = 0, .ofs_y = 1},
    {.bitmap_index = 21465, .adv_w = 480, .box_w = 31, .box_h = 20, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 21775, .adv_w = 336, .box_w = 19, .box_h = 24, .ofs_x = 1, .ofs_y = -3},
    {.bitmap_index = 22003, .adv_w = 336, .box_w = 21, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 22255, .adv_w = 384, .box_w = 25, .box_h = 25, .ofs_x = -1, .ofs_y = -4},
    {.bitmap_index = 22568, .adv_w = 480, .box_w = 30, .box_h = 18, .ofs_x = 0, .ofs_y = 0},
    {.bitmap_index = 22838, .adv_w = 288, .box_w = 18, .box_h = 24, .ofs_x = 0, .ofs_y = -3},
    {.bitmap_index = 23054, .adv_w = 386, .box_w = 25, .box_h = 16, .ofs_x = 0, .ofs_y = 1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_1[] = {
    0x0, 0x1f72, 0xef51, 0xef58, 0xef5b, 0xef5c, 0xef5d, 0xef61,
    0xef63, 0xef65, 0xef69, 0xef6c, 0xef71, 0xef76, 0xef77, 0xef78,
    0xef8e, 0xef98, 0xef9b, 0xef9c, 0xef9d, 0xefa1, 0xefa2, 0xefa3,
    0xefa4, 0xefb7, 0xefb8, 0xefbe, 0xefc0, 0xefc1, 0xefc4, 0xefc7,
    0xefc8, 0xefc9, 0xefcb, 0xefe3, 0xefe5, 0xf014, 0xf015, 0xf017,
    0xf037, 0xf03a, 0xf043, 0xf06c, 0xf074, 0xf0ab, 0xf13b, 0xf190,
    0xf191, 0xf192, 0xf193, 0xf194, 0xf1d7, 0xf1e3, 0xf23d, 0xf254,
    0xf4aa, 0xf712, 0xf7f2
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 32, .range_length = 95, .glyph_id_start = 1,
        .unicode_list = NULL, .glyph_id_ofs_list = NULL, .list_length = 0, .type = LV_FONT_FMT_TXT_CMAP_FORMAT0_TINY
    },
    {
        .range_start = 176, .range_length = 63475, .glyph_id_start = 96,
        .unicode_list = unicode_list_1, .glyph_id_ofs_list = NULL, .list_length = 59, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};

/*-----------------
 *    KERNING
 *----------------*/

/*Map glyph_ids to kern left classes*/
static const uint8_t kern_left_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 0, 13, 14, 15, 16, 17,
    18, 19, 12, 20, 20, 0, 0, 0,
    21, 22, 23, 24, 25, 22, 26, 27,
    28, 29, 29, 30, 31, 32, 29, 29,
    22, 33, 34, 35, 3, 36, 30, 37,
    37, 38, 39, 40, 41, 42, 43, 0,
    44, 0, 45, 46, 47, 48, 49, 50,
    51, 45, 52, 52, 53, 48, 45, 45,
    46, 46, 54, 55, 56, 57, 51, 58,
    58, 59, 58, 60, 41, 0, 0, 9,
    61, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0
};

/*Map glyph_ids to kern right classes*/
static const uint8_t kern_right_class_mapping[] =
{
    0, 0, 1, 2, 0, 3, 4, 5,
    2, 6, 7, 8, 9, 10, 9, 10,
    11, 12, 13, 14, 15, 16, 17, 12,
    18, 19, 20, 21, 21, 0, 0, 0,
    22, 23, 24, 25, 23, 25, 25, 25,
    23, 25, 25, 26, 25, 25, 25, 25,
    23, 25, 23, 25, 3, 27, 28, 29,
    29, 30, 31, 32, 33, 34, 35, 0,
    36, 0, 37, 38, 39, 39, 39, 0,
    39, 38, 40, 41, 38, 38, 42, 42,
    39, 42, 39, 42, 43, 44, 45, 46,
    46, 47, 46, 48, 0, 0, 35, 9,
    49, 9, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0
};

/*Kern values between classes*/
static const int8_t kern_class_values[] =
{
    0, 1, 0, 0, 0, 0, 0, 0,
    0, 1, 0, 0, 4, 0, 0, 0,
    0, 3, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, 17, 0, 10, -8, 0, 0,
    0, 0, -21, -23, 3, 18, 8, 7,
    -15, 3, 19, 1, 16, 4, 12, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 23, 3, -3, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 8, 0, -12, 0, 0, 0, 0,
    0, -8, 7, 8, 0, 0, -4, 0,
    -3, 4, 0, -4, 0, -4, -2, -8,
    0, 0, 0, 0, -4, 0, 0, -5,
    -6, 0, 0, -4, 0, -8, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    -4, 0, -6, 0, -10, 0, -46, 0,
    0, -8, 0, 8, 12, 0, 0, -8,
    4, 4, 13, 8, -7, 8, 0, 0,
    -22, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -14, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -10, -5, -19, 0, -15,
    -3, 0, 0, 0, 0, 1, 15, 0,
    -12, -3, -1, 1, 0, -7, 0, 0,
    -3, -28, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -31, -3, 15,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -16, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 13,
    0, 4, 0, 0, -8, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 15, 3,
    1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -14, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 3,
    8, 4, 12, -4, 0, 0, 8, -4,
    -13, -53, 3, 10, 8, 1, -5, 0,
    14, 0, 12, 0, 12, 0, -36, 0,
    -5, 12, 0, 13, -4, 8, 4, 0,
    0, 1, -4, 0, 0, -7, 31, 0,
    31, 0, 12, 0, 16, 5, 7, 12,
    0, 0, 0, -14, 0, 0, 0, 0,
    1, -3, 0, 3, -7, -5, -8, 3,
    0, -4, 0, 0, 0, -15, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -25, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 1, -21, 0, -24, 0, 0, 0,
    0, -3, 0, 38, -5, -5, 4, 4,
    -3, 0, -5, 4, 0, 0, -20, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -37, 0, 4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -24, 0, 23, 0, 0, -14, 0,
    13, 0, -26, -37, -26, -8, 12, 0,
    0, -26, 0, 5, -9, 0, -6, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 10, 12, -47, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 18, 0, 3, 0, 0, 0,
    0, 0, 3, 3, -5, -8, 0, -1,
    -1, -4, 0, 0, -3, 0, 0, 0,
    -8, 0, -3, 0, -9, -8, 0, -10,
    -13, -13, -7, 0, -8, 0, -8, 0,
    0, 0, 0, -3, 0, 0, 4, 0,
    3, -4, 0, 1, 0, 0, 0, 4,
    -3, 0, 0, 0, -3, 4, 4, -1,
    0, 0, 0, -7, 0, -1, 0, 0,
    0, 0, 0, 1, 0, 5, -3, 0,
    -5, 0, -7, 0, 0, -3, 0, 12,
    0, 0, -4, 0, 0, 0, 0, 0,
    -1, 1, -3, -3, 0, 0, -4, 0,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -2, -2, 0, -4, -5, 0,
    0, 0, 0, 0, 1, 0, 0, -3,
    0, -4, -4, -4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -2, 0, 0,
    0, 0, -3, -5, 0, -6, 0, -12,
    -3, -12, 8, 0, 0, -8, 4, 8,
    10, 0, -10, -1, -5, 0, -1, -18,
    4, -3, 3, -20, 4, 0, 0, 1,
    -20, 0, -20, -3, -33, -3, 0, -19,
    0, 8, 11, 0, 5, 0, 0, 0,
    0, 1, 0, -7, -5, 0, -12, 0,
    0, 0, -4, 0, 0, 0, -4, 0,
    0, 0, 0, 0, -2, -2, 0, -2,
    -5, 0, 0, 0, 0, 0, 0, 0,
    -4, -4, 0, -3, -5, -3, 0, 0,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, -3, 0, -5,
    0, -3, 0, -8, 4, 0, 0, -5,
    2, 4, 4, 0, 0, 0, 0, 0,
    0, -3, 0, 0, 0, 0, 0, 3,
    0, 0, -4, 0, -4, -3, -5, 0,
    0, 0, 0, 0, 0, 0, 3, 0,
    -3, 0, 0, 0, 0, -4, -6, 0,
    -7, 0, 12, -3, 1, -12, 0, 0,
    10, -19, -20, -16, -8, 4, 0, -3,
    -25, -7, 0, -7, 0, -8, 6, -7,
    -25, 0, -10, 0, 0, 2, -1, 3,
    -3, 0, 4, 0, -12, -15, 0, -19,
    -9, -8, -9, -12, -5, -10, -1, -7,
    -10, 2, 0, 1, 0, -4, 0, 0,
    0, 3, 0, 4, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -4,
    0, -2, 0, -1, -4, 0, -7, -8,
    -8, -1, 0, -12, 0, 0, 0, 0,
    0, 0, -3, 0, 0, 0, 0, 2,
    -2, 0, 0, 0, 4, 0, 0, 0,
    0, 0, 0, 0, 0, 18, 0, 0,
    0, 0, 0, 0, 3, 0, 0, 0,
    -4, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, 4, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, 0, 0,
    -7, 0, 0, 0, 0, -19, -12, 0,
    0, 0, -6, -19, 0, 0, -4, 4,
    0, -10, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 0, 0, -7,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 4, 0, -7, 0,
    0, 0, 0, 5, 0, 3, -8, -8,
    0, -4, -4, -5, 0, 0, 0, 0,
    0, 0, -12, 0, -4, 0, -6, -4,
    0, -8, -10, -12, -3, 0, -8, 0,
    -12, 0, 0, 0, 0, 31, 0, 0,
    2, 0, 0, -5, 0, 4, 0, -17,
    0, 0, 0, 0, 0, -36, -7, 13,
    12, -3, -16, 0, 4, -6, 0, -19,
    -2, -5, 4, -27, -4, 5, 0, 6,
    -13, -6, -14, -13, -16, 0, 0, -23,
    0, 22, 0, 0, -2, 0, 0, 0,
    -2, -2, -4, -10, -13, -1, -36, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    1, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -4, 0, -2, -4, -6, 0, 0,
    -8, 0, -4, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, -8, 0, 0, 8,
    -1, 5, 0, -8, 4, -3, -1, -10,
    -4, 0, -5, -4, -3, 0, -6, -7,
    0, 0, -3, -1, -3, -7, -5, 0,
    0, -4, 0, 4, -3, 0, -8, 0,
    0, 0, -8, 0, -7, 0, -7, -7,
    4, 0, 0, 0, 0, 0, 0, 0,
    0, -8, 4, 0, -5, 0, -3, -5,
    -12, -3, -3, -3, -1, -3, -5, -1,
    0, 0, 0, 0, 0, -4, -3, -3,
    0, 0, 0, 0, 5, -3, 0, -3,
    0, 0, 0, -3, -5, -3, -3, -5,
    -3, 0, 3, 15, -1, 0, -10, 0,
    -3, 8, 0, -4, -16, -5, 6, 0,
    0, -18, -7, 4, -7, 3, 0, -3,
    -3, -12, 0, -6, 2, 0, 0, -7,
    0, 0, 0, 4, 4, -8, -7, 0,
    -7, -4, -6, -4, -4, 0, -7, 2,
    -7, -7, 12, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 4, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -7, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -3, -4,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 0, 0, -5,
    0, 0, -4, -4, 0, 0, 0, 0,
    -4, 0, 0, 0, 0, -2, 0, 0,
    0, 0, 0, -3, 0, 0, 0, 0,
    -6, 0, -8, 0, 0, 0, -13, 0,
    3, -8, 8, 1, -3, -18, 0, 0,
    -8, -4, 0, -15, -10, -11, 0, 0,
    -17, -4, -15, -15, -18, 0, -10, 0,
    3, 26, -5, 0, -9, -4, -1, -4,
    -7, -10, -7, -14, -16, -9, -4, 0,
    0, -3, 0, 1, 0, 0, -27, -3,
    12, 8, -8, -14, 0, 1, -12, 0,
    -19, -3, -4, 8, -35, -5, 1, 0,
    0, -25, -5, -20, -4, -28, 0, 0,
    -27, 0, 23, 1, 0, -3, 0, 0,
    0, 0, -2, -3, -15, -3, 0, -25,
    0, 0, 0, 0, -12, 0, -3, 0,
    -1, -11, -18, 0, 0, -2, -6, -12,
    -4, 0, -3, 0, 0, 0, 0, -17,
    -4, -13, -12, -3, -7, -10, -4, -7,
    0, -8, -3, -13, -6, 0, -5, -7,
    -4, -7, 0, 2, 0, -3, -13, 0,
    8, 0, -7, 0, 0, 0, 0, 5,
    0, 3, -8, 16, 0, -4, -4, -5,
    0, 0, 0, 0, 0, 0, -12, 0,
    -4, 0, -6, -4, 0, -8, -10, -12,
    -3, 0, -8, 3, 15, 0, 0, 0,
    0, 31, 0, 0, 2, 0, 0, -5,
    0, 4, 0, 0, 0, 0, 0, 0,
    0, 0, -1, 0, 0, 0, 0, 0,
    -3, -8, 0, 0, 0, 0, 0, -2,
    0, 0, 0, -4, -4, 0, 0, -8,
    -4, 0, 0, -8, 0, 7, -2, 0,
    0, 0, 0, 0, 0, 2, 0, 0,
    0, 0, 6, 8, 3, -3, 0, -12,
    -6, 0, 12, -13, -12, -8, -8, 15,
    7, 4, -33, -3, 8, -4, 0, -4,
    4, -4, -13, 0, -4, 4, -5, -3,
    -12, -3, 0, 0, 12, 8, 0, -11,
    0, -21, -5, 11, -5, -15, 1, -5,
    -13, -13, -4, 15, 4, 0, -6, 0,
    -10, 0, 3, 13, -9, -14, -15, -10,
    12, 0, 1, -28, -3, 4, -7, -3,
    -9, 0, -8, -14, -6, -6, -3, 0,
    0, -9, -8, -4, 0, 12, 9, -4,
    -21, 0, -21, -5, 0, -13, -22, -1,
    -12, -7, -13, -11, 10, 0, 0, -5,
    0, -8, -3, 0, -4, -7, 0, 7,
    -13, 4, 0, 0, -20, 0, -4, -8,
    -7, -3, -12, -10, -13, -9, 0, -12,
    -4, -9, -7, -12, -4, 0, 0, 1,
    18, -7, 0, -12, -4, 0, -4, -8,
    -9, -10, -11, -15, -5, -8, 8, 0,
    -6, 0, -19, -5, 2, 8, -12, -14,
    -8, -13, 13, -4, 2, -36, -7, 8,
    -8, -7, -14, 0, -12, -16, -5, -4,
    -3, -4, -8, -12, -1, 0, 0, 12,
    11, -3, -25, 0, -23, -9, 9, -15,
    -26, -8, -13, -16, -19, -13, 8, 0,
    0, 0, 0, -5, 0, 0, 4, -5,
    8, 3, -7, 8, 0, 0, -12, -1,
    0, -1, 0, 1, 1, -3, 0, 0,
    0, 0, 0, 0, -4, 0, 0, 0,
    0, 3, 12, 1, 0, -5, 0, 0,
    0, 0, -3, -3, -5, 0, 0, 0,
    1, 3, 0, 0, 0, 0, 3, 0,
    -3, 0, 15, 0, 7, 1, 1, -5,
    0, 8, 0, 0, 0, 3, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 12, 0, 11, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -23, 0, -4, 7, 0, 12,
    0, 0, 38, 5, -8, -8, 4, 4,
    -3, 1, -19, 0, 0, 18, -23, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, -26, 15, 54, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -23, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -6, 0, 0, -7,
    -3, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -3, 0, -10, 0,
    0, 1, 0, 0, 4, 50, -8, -3,
    12, 10, -10, 4, 0, 0, 4, 4,
    -5, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -50, 11, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -11,
    0, 0, 0, -10, 0, 0, 0, 0,
    -8, -2, 0, 0, 0, -8, 0, -5,
    0, -18, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -26, 0, 0,
    0, 0, 1, 0, 0, 0, 0, 0,
    0, -4, 0, 0, -7, 0, -6, 0,
    -10, 0, 0, 0, -7, 4, -5, 0,
    0, -10, -4, -9, 0, 0, -10, 0,
    -4, 0, -18, 0, -4, 0, 0, -31,
    -7, -15, -4, -14, 0, 0, -26, 0,
    -10, -2, 0, 0, 0, 0, 0, 0,
    0, 0, -6, -7, -3, -7, 0, 0,
    0, 0, -8, 0, -8, 5, -4, 8,
    0, -3, -9, -3, -7, -7, 0, -5,
    -2, -3, 3, -10, -1, 0, 0, 0,
    -34, -3, -5, 0, -8, 0, -3, -18,
    -3, 0, 0, -3, -3, 0, 0, 0,
    0, 3, 0, -3, -7, -3, 7, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 5, 0, 0, 0, 0, 0,
    0, -8, 0, -3, 0, 0, 0, -8,
    4, 0, 0, 0, -10, -4, -8, 0,
    0, -11, 0, -4, 0, -18, 0, 0,
    0, 0, -37, 0, -8, -14, -19, 0,
    0, -26, 0, -3, -6, 0, 0, 0,
    0, 0, 0, 0, 0, -4, -6, -2,
    -6, 1, 0, 0, 7, -5, 0, 12,
    19, -4, -4, -12, 5, 19, 7, 8,
    -10, 5, 16, 5, 11, 8, 10, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 24, 18, -7, -4, 0, -3,
    31, 17, 31, 0, 0, 0, 4, 0,
    0, 14, 0, 0, -6, 0, 0, 0,
    0, 0, 0, 0, 0, 0, -3, 0,
    0, 0, 0, 0, 0, 0, 0, 5,
    0, 0, 0, 0, -32, -5, -3, -16,
    -19, 0, 0, -26, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, -6, 0, 0,
    0, 0, 0, 0, 0, 0, 0, -3,
    0, 0, 0, 0, 0, 0, 0, 0,
    5, 0, 0, 0, 0, -32, -5, -3,
    -16, -19, 0, 0, -15, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -3, 0, 0, 0, -9, 4, 0, -4,
    3, 7, 4, -12, 0, -1, -3, 4,
    0, 3, 0, 0, 0, 0, -10, 0,
    -3, -3, -8, 0, -3, -15, 0, 24,
    -4, 0, -8, -3, 0, -3, -7, 0,
    -4, -11, -8, -5, 0, 0, 0, -6,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -3, 0, 0, 0, 0, 0, 0,
    0, 0, 5, 0, 0, 0, 0, -32,
    -5, -3, -16, -19, 0, 0, -26, 0,
    0, 0, 0, 0, 0, 19, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -6, 0, -12, -5, -3, 12, -3, -4,
    -15, 1, -2, 1, -3, -10, 1, 8,
    1, 3, 1, 3, -9, -15, -5, 0,
    -15, -7, -10, -16, -15, 0, -6, -8,
    -5, -5, -3, -3, -5, -3, 0, -3,
    -1, 6, 0, 6, -3, 0, 12, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, -3, -4, -4, 0, 0,
    -10, 0, -2, 0, -7, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    -23, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -4, -4, 0, -5,
    0, 0, 0, 0, -3, 0, 0, -7,
    -4, 4, 0, -7, -7, -3, 0, -11,
    -3, -8, -3, -5, 0, -7, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, -26, 0, 12, 0, 0, -7, 0,
    0, 0, 0, -5, 0, -4, 0, 0,
    -2, 0, 0, -3, 0, -9, 0, 0,
    16, -5, -13, -12, 3, 4, 4, -1,
    -11, 3, 6, 3, 12, 3, 13, -3,
    -10, 0, 0, -15, 0, 0, -12, -10,
    0, 0, -8, 0, -5, -7, 0, -6,
    0, -6, 0, -3, 6, 0, -3, -12,
    -4, 14, 0, 0, -3, 0, -8, 0,
    0, 5, -9, 0, 4, -4, 3, 0,
    0, -13, 0, -3, -1, 0, -4, 4,
    -3, 0, 0, 0, -16, -5, -8, 0,
    -12, 0, 0, -18, 0, 14, -4, 0,
    -7, 0, 2, 0, -4, 0, -4, -12,
    0, -4, 4, 0, 0, 0, 0, -3,
    0, 0, 4, -5, 1, 0, 0, -5,
    -3, 0, -5, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, -24, 0, 8, 0,
    0, -3, 0, 0, 0, 0, 1, 0,
    -4, -4, 0, 0, 0, 8, 0, 9,
    0, 0, 0, 0, 0, -24, -22, 1,
    17, 12, 7, -15, 3, 16, 0, 14,
    0, 8, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0, 20, 0, 0,
    0, 0, 0, 0, 0, 0, 0, 0,
    0, 0, 0, 0, 0
};

/*Collect the kern class' data in one place*/
static const lv_font_fmt_txt_kern_classes_t kern_classes =
{
    .class_pair_values   = kern_class_values,
    .left_class_mapping  = kern_left_class_mapping,
    .right_class_mapping = kern_right_class_mapping,
    .left_class_cnt      = 61,
    .right_class_cnt     = 49,
};

/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

/*Store all the custom data of the font*/
static lv_font_fmt_txt_dsc_t font_dsc = {
    .glyph_bitmap = gylph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = &kern_classes,
    .kern_scale = 16,
    .cmap_num = 2,
    .bpp = 4,
    .kern_classes = 1,
    .bitmap_format = 0
};

/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
lv_font_t lv_font_montserrat_24 = {
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 27,          /*The maximum line height required by the font*/
    .base_line = 5,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0)
    .underline_position = -2,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc           /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
};

#endif /*#if LV_FONT_MONTSERRAT_24*/
