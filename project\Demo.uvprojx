<?xml version="1.0" encoding="UTF-8" standalone="no" ?>
<Project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="project_projx.xsd">

  <SchemaVersion>2.1</SchemaVersion>

  <Header>### uVision Project, (C) Keil Software</Header>

  <Targets>
    <Target>
      <TargetName>Target 1</TargetName>
      <ToolsetNumber>0x4</ToolsetNumber>
      <ToolsetName>ARM-ADS</ToolsetName>
      <pCCUsed>5060750::V5.06 update 6 (build 750)::ARMCC</pCCUsed>
      <uAC6>0</uAC6>
      <TargetOption>
        <TargetCommonOption>
          <Device>STM32F103ZE</Device>
          <Vendor>STMicroelectronics</Vendor>
          <PackID>Keil.STM32F1xx_DFP.1.0.5</PackID>
          <PackURL>http://www.keil.com/pack/</PackURL>
          <Cpu>IRAM(0x20000000,0x10000) IROM(0x08000000,0x80000) CPUTYPE("Cortex-M3") CLOCK(12000000) ELITTLE</Cpu>
          <FlashUtilSpec></FlashUtilSpec>
          <StartupFile></StartupFile>
          <FlashDriverDll>UL2CM3(-S0 -C0 -P0 -********** -FC1000 -FN1 -FF0STM32F10x_512 -********** -FL080000 -FP0($$Device:STM32F103ZE$Flash\STM32F10x_512.FLM))</FlashDriverDll>
          <DeviceId>0</DeviceId>
          <RegisterFile>$$Device:STM32F103ZE$Device\Include\stm32f10x.h</RegisterFile>
          <MemoryEnv></MemoryEnv>
          <Cmp></Cmp>
          <Asm></Asm>
          <Linker></Linker>
          <OHString></OHString>
          <InfinionOptionDll></InfinionOptionDll>
          <SLE66CMisc></SLE66CMisc>
          <SLE66AMisc></SLE66AMisc>
          <SLE66LinkerMisc></SLE66LinkerMisc>
          <SFDFile>$$Device:STM32F103ZE$SVD\STM32F103xx.svd</SFDFile>
          <bCustSvd>0</bCustSvd>
          <UseEnv>0</UseEnv>
          <BinPath></BinPath>
          <IncludePath></IncludePath>
          <LibPath></LibPath>
          <RegisterFilePath></RegisterFilePath>
          <DBRegisterFilePath></DBRegisterFilePath>
          <TargetStatus>
            <Error>0</Error>
            <ExitCodeStop>0</ExitCodeStop>
            <ButtonStop>0</ButtonStop>
            <NotGenerated>0</NotGenerated>
            <InvalidFlash>1</InvalidFlash>
          </TargetStatus>
          <OutputDirectory>.\Objects\</OutputDirectory>
          <OutputName>Demo</OutputName>
          <CreateExecutable>1</CreateExecutable>
          <CreateLib>0</CreateLib>
          <CreateHexFile>1</CreateHexFile>
          <DebugInformation>1</DebugInformation>
          <BrowseInformation>1</BrowseInformation>
          <ListingPath>.\Listings\</ListingPath>
          <HexFormatSelection>1</HexFormatSelection>
          <Merge32K>0</Merge32K>
          <CreateBatchFile>0</CreateBatchFile>
          <BeforeCompile>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopU1X>0</nStopU1X>
            <nStopU2X>0</nStopU2X>
          </BeforeCompile>
          <BeforeMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopB1X>0</nStopB1X>
            <nStopB2X>0</nStopB2X>
          </BeforeMake>
          <AfterMake>
            <RunUserProg1>0</RunUserProg1>
            <RunUserProg2>0</RunUserProg2>
            <UserProg1Name></UserProg1Name>
            <UserProg2Name></UserProg2Name>
            <UserProg1Dos16Mode>0</UserProg1Dos16Mode>
            <UserProg2Dos16Mode>0</UserProg2Dos16Mode>
            <nStopA1X>0</nStopA1X>
            <nStopA2X>0</nStopA2X>
          </AfterMake>
          <SelectedForBatchBuild>0</SelectedForBatchBuild>
          <SVCSIdString></SVCSIdString>
        </TargetCommonOption>
        <CommonProperty>
          <UseCPPCompiler>0</UseCPPCompiler>
          <RVCTCodeConst>0</RVCTCodeConst>
          <RVCTZI>0</RVCTZI>
          <RVCTOtherData>0</RVCTOtherData>
          <ModuleSelection>0</ModuleSelection>
          <IncludeInBuild>1</IncludeInBuild>
          <AlwaysBuild>0</AlwaysBuild>
          <GenerateAssemblyFile>0</GenerateAssemblyFile>
          <AssembleAssemblyFile>0</AssembleAssemblyFile>
          <PublicsOnly>0</PublicsOnly>
          <StopOnExitCode>3</StopOnExitCode>
          <CustomArgument></CustomArgument>
          <IncludeLibraryModules></IncludeLibraryModules>
          <ComprImg>1</ComprImg>
        </CommonProperty>
        <DllOption>
          <SimDllName>SARMCM3.DLL</SimDllName>
          <SimDllArguments> -REMAP</SimDllArguments>
          <SimDlgDll>DCM.DLL</SimDlgDll>
          <SimDlgDllArguments>-pCM3</SimDlgDllArguments>
          <TargetDllName>SARMCM3.DLL</TargetDllName>
          <TargetDllArguments></TargetDllArguments>
          <TargetDlgDll>TCM.DLL</TargetDlgDll>
          <TargetDlgDllArguments>-pCM3</TargetDlgDllArguments>
        </DllOption>
        <DebugOption>
          <OPTHX>
            <HexSelection>1</HexSelection>
            <HexRangeLowAddress>0</HexRangeLowAddress>
            <HexRangeHighAddress>0</HexRangeHighAddress>
            <HexOffset>0</HexOffset>
            <Oh166RecLen>16</Oh166RecLen>
          </OPTHX>
        </DebugOption>
        <Utilities>
          <Flash1>
            <UseTargetDll>1</UseTargetDll>
            <UseExternalTool>0</UseExternalTool>
            <RunIndependent>0</RunIndependent>
            <UpdateFlashBeforeDebugging>1</UpdateFlashBeforeDebugging>
            <Capability>1</Capability>
            <DriverSelection>4096</DriverSelection>
          </Flash1>
          <bUseTDR>1</bUseTDR>
          <Flash2>BIN\UL2CM3.DLL</Flash2>
          <Flash3>"" ()</Flash3>
          <Flash4></Flash4>
          <pFcarmOut></pFcarmOut>
          <pFcarmGrp></pFcarmGrp>
          <pFcArmRoot></pFcArmRoot>
          <FcArmLst>0</FcArmLst>
        </Utilities>
        <TargetArmAds>
          <ArmAdsMisc>
            <GenerateListings>0</GenerateListings>
            <asHll>1</asHll>
            <asAsm>1</asAsm>
            <asMacX>1</asMacX>
            <asSyms>1</asSyms>
            <asFals>1</asFals>
            <asDbgD>1</asDbgD>
            <asForm>1</asForm>
            <ldLst>0</ldLst>
            <ldmm>1</ldmm>
            <ldXref>1</ldXref>
            <BigEnd>0</BigEnd>
            <AdsALst>1</AdsALst>
            <AdsACrf>1</AdsACrf>
            <AdsANop>0</AdsANop>
            <AdsANot>0</AdsANot>
            <AdsLLst>1</AdsLLst>
            <AdsLmap>1</AdsLmap>
            <AdsLcgr>1</AdsLcgr>
            <AdsLsym>1</AdsLsym>
            <AdsLszi>1</AdsLszi>
            <AdsLtoi>1</AdsLtoi>
            <AdsLsun>1</AdsLsun>
            <AdsLven>1</AdsLven>
            <AdsLsxf>1</AdsLsxf>
            <RvctClst>0</RvctClst>
            <GenPPlst>0</GenPPlst>
            <AdsCpuType>"Cortex-M3"</AdsCpuType>
            <RvctDeviceName></RvctDeviceName>
            <mOS>0</mOS>
            <uocRom>0</uocRom>
            <uocRam>0</uocRam>
            <hadIROM>1</hadIROM>
            <hadIRAM>1</hadIRAM>
            <hadXRAM>0</hadXRAM>
            <uocXRam>0</uocXRam>
            <RvdsVP>0</RvdsVP>
            <RvdsMve>0</RvdsMve>
            <hadIRAM2>0</hadIRAM2>
            <hadIROM2>0</hadIROM2>
            <StupSel>8</StupSel>
            <useUlib>1</useUlib>
            <EndSel>0</EndSel>
            <uLtcg>0</uLtcg>
            <nSecure>0</nSecure>
            <RoSelD>3</RoSelD>
            <RwSelD>3</RwSelD>
            <CodeSel>0</CodeSel>
            <OptFeed>0</OptFeed>
            <NoZi1>0</NoZi1>
            <NoZi2>0</NoZi2>
            <NoZi3>0</NoZi3>
            <NoZi4>0</NoZi4>
            <NoZi5>0</NoZi5>
            <Ro1Chk>0</Ro1Chk>
            <Ro2Chk>0</Ro2Chk>
            <Ro3Chk>0</Ro3Chk>
            <Ir1Chk>1</Ir1Chk>
            <Ir2Chk>0</Ir2Chk>
            <Ra1Chk>0</Ra1Chk>
            <Ra2Chk>0</Ra2Chk>
            <Ra3Chk>0</Ra3Chk>
            <Im1Chk>1</Im1Chk>
            <Im2Chk>0</Im2Chk>
            <OnChipMemories>
              <Ocm1>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm1>
              <Ocm2>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm2>
              <Ocm3>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm3>
              <Ocm4>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm4>
              <Ocm5>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm5>
              <Ocm6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </Ocm6>
              <IRAM>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x10000</Size>
              </IRAM>
              <IROM>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x80000</Size>
              </IROM>
              <XRAM>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </XRAM>
              <OCR_RVCT1>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT1>
              <OCR_RVCT2>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT2>
              <OCR_RVCT3>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT3>
              <OCR_RVCT4>
                <Type>1</Type>
                <StartAddress>0x8000000</StartAddress>
                <Size>0x80000</Size>
              </OCR_RVCT4>
              <OCR_RVCT5>
                <Type>1</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT5>
              <OCR_RVCT6>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT6>
              <OCR_RVCT7>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT7>
              <OCR_RVCT8>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT8>
              <OCR_RVCT9>
                <Type>0</Type>
                <StartAddress>0x20000000</StartAddress>
                <Size>0x10000</Size>
              </OCR_RVCT9>
              <OCR_RVCT10>
                <Type>0</Type>
                <StartAddress>0x0</StartAddress>
                <Size>0x0</Size>
              </OCR_RVCT10>
            </OnChipMemories>
            <RvctStartVector></RvctStartVector>
          </ArmAdsMisc>
          <Cads>
            <interw>1</interw>
            <Optim>1</Optim>
            <oTime>0</oTime>
            <SplitLS>0</SplitLS>
            <OneElfS>1</OneElfS>
            <Strict>0</Strict>
            <EnumInt>0</EnumInt>
            <PlainCh>0</PlainCh>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <wLevel>2</wLevel>
            <uThumb>0</uThumb>
            <uSurpInc>0</uSurpInc>
            <uC99>1</uC99>
            <uGnu>1</uGnu>
            <useXO>0</useXO>
            <v6Lang>1</v6Lang>
            <v6LangP>1</v6LangP>
            <vShortEn>1</vShortEn>
            <vShortWch>1</vShortWch>
            <v6Lto>0</v6Lto>
            <v6WtE>0</v6WtE>
            <v6Rtti>0</v6Rtti>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define>STM32F10X_HD,USE_STDPERIPH_DRIVER</Define>
              <Undefine></Undefine>
              <IncludePath>..\user\API;..\user;..\STM32F10x_StdPeriph_Driver\inc;..\Mqtt;..\FreeRTOS\Port\RVDS\ARM_CM3;..\FreeRTOS\include;..\Lvgl;..\Lvgl\app;..\Lvgl\porting;..\Lvgl\src;..\Lvgl\src\lv_core;..\Lvgl\src\lv_draw;..\Lvgl\src\lv_font;..\Lvgl\src\lv_hal;..\Lvgl\src\lv_misc;..\Lvgl\src\lv_widgets;..\Lvgl\src\lv_themes;..\Lvgl\generated;..\Lvgl\generated\guider_customer_fonts;..\Lvgl\generated\guider_fonts;..\Lvgl\generated\images</IncludePath>
            </VariousControls>
          </Cads>
          <Aads>
            <interw>1</interw>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <thumb>0</thumb>
            <SplitLS>0</SplitLS>
            <SwStkChk>0</SwStkChk>
            <NoWarn>0</NoWarn>
            <uSurpInc>0</uSurpInc>
            <useXO>0</useXO>
            <uClangAs>0</uClangAs>
            <VariousControls>
              <MiscControls></MiscControls>
              <Define></Define>
              <Undefine></Undefine>
              <IncludePath></IncludePath>
            </VariousControls>
          </Aads>
          <LDads>
            <umfTarg>1</umfTarg>
            <Ropi>0</Ropi>
            <Rwpi>0</Rwpi>
            <noStLib>0</noStLib>
            <RepFail>1</RepFail>
            <useFile>0</useFile>
            <TextAddressRange>0x08000000</TextAddressRange>
            <DataAddressRange>0x20000000</DataAddressRange>
            <pXoBase></pXoBase>
            <ScatterFile></ScatterFile>
            <IncludeLibs></IncludeLibs>
            <IncludeLibsPath></IncludeLibsPath>
            <Misc></Misc>
            <LinkerInputFile></LinkerInputFile>
            <DisabledWarnings></DisabledWarnings>
          </LDads>
        </TargetArmAds>
      </TargetOption>
      <Groups>
        <Group>
          <GroupName>startup</GroupName>
          <Files>
            <File>
              <FileName>startup_stm32f10x_hd.s</FileName>
              <FileType>2</FileType>
              <FilePath>..\startup\startup_stm32f10x_hd.s</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>user</GroupName>
          <Files>
            <File>
              <FileName>stm32f10x_it.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\stm32f10x_it.c</FilePath>
            </File>
            <File>
              <FileName>system_stm32f10x.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\system_stm32f10x.c</FilePath>
            </File>
            <File>
              <FileName>main.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\main.c</FilePath>
            </File>
            <File>
              <FileName>FreeRTOSConfig.h</FileName>
              <FileType>5</FileType>
              <FilePath>..\user\FreeRTOSConfig.h</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>api</GroupName>
          <Files>
            <File>
              <FileName>led.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\led.c</FilePath>
            </File>
            <File>
              <FileName>delay.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\delay.c</FilePath>
            </File>
            <File>
              <FileName>key.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\key.c</FilePath>
            </File>
            <File>
              <FileName>relay.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\relay.c</FilePath>
            </File>
            <File>
              <FileName>exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\exti.c</FilePath>
            </File>
            <File>
              <FileName>time6.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\time6.c</FilePath>
            </File>
            <File>
              <FileName>time7.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\time7.c</FilePath>
            </File>
            <File>
              <FileName>uart1.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\uart1.c</FilePath>
            </File>
            <File>
              <FileName>kqm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\kqm.c</FilePath>
            </File>
            <File>
              <FileName>su03t.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\su03t.c</FilePath>
            </File>
            <File>
              <FileName>dht11.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\dht11.c</FilePath>
            </File>
            <File>
              <FileName>pwm.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\pwm.c</FilePath>
            </File>
            <File>
              <FileName>adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\adc.c</FilePath>
            </File>
            <File>
              <FileName>dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\dma.c</FilePath>
            </File>
            <File>
              <FileName>BH1750.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\BH1750.c</FilePath>
            </File>
            <File>
              <FileName>IIC.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\IIC.c</FilePath>
            </File>
            <File>
              <FileName>SPI.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\SPI.c</FilePath>
            </File>
            <File>
              <FileName>w25q64.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\w25q64.c</FilePath>
            </File>
            <File>
              <FileName>RTC.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\RTC.c</FilePath>
            </File>
            <File>
              <FileName>ESP8266.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\ESP8266.c</FilePath>
            </File>
            <File>
              <FileName>Cloud.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\Cloud.c</FilePath>
            </File>
            <File>
              <FileName>beep.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\beep.c</FilePath>
            </File>
            <File>
              <FileName>lcd.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\lcd.c</FilePath>
            </File>
            <File>
              <FileName>app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\user\API\app.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>stdlib</GroupName>
          <Files>
            <File>
              <FileName>stm32f10x_can.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_can.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_cec.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_cec.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_crc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_crc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_dac.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_dac.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_dbgmcu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_dbgmcu.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_dma.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_dma.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_exti.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_exti.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_flash.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_flash.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_fsmc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_fsmc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_gpio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_gpio.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_i2c.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_i2c.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_iwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_iwdg.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_pwr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_pwr.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_rcc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_rcc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_rtc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_rtc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_sdio.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_sdio.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_spi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_spi.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_tim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_tim.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_usart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_usart.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_wwdg.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_wwdg.c</FilePath>
            </File>
            <File>
              <FileName>misc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\misc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_adc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_adc.c</FilePath>
            </File>
            <File>
              <FileName>stm32f10x_bkp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\STM32F10x_StdPeriph_Driver\src\stm32f10x_bkp.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>Mqtt</GroupName>
          <Files>
            <File>
              <FileName>MQTTConnectClient.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Mqtt\MQTTConnectClient.c</FilePath>
            </File>
            <File>
              <FileName>MQTTConnectServer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Mqtt\MQTTConnectServer.c</FilePath>
            </File>
            <File>
              <FileName>MQTTDeserializePublish.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Mqtt\MQTTDeserializePublish.c</FilePath>
            </File>
            <File>
              <FileName>MQTTFormat.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Mqtt\MQTTFormat.c</FilePath>
            </File>
            <File>
              <FileName>MQTTPacket.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Mqtt\MQTTPacket.c</FilePath>
            </File>
            <File>
              <FileName>MQTTSerializePublish.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Mqtt\MQTTSerializePublish.c</FilePath>
            </File>
            <File>
              <FileName>MQTTSubscribeClient.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Mqtt\MQTTSubscribeClient.c</FilePath>
            </File>
            <File>
              <FileName>MQTTSubscribeServer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Mqtt\MQTTSubscribeServer.c</FilePath>
            </File>
            <File>
              <FileName>MQTTUnsubscribeClient.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Mqtt\MQTTUnsubscribeClient.c</FilePath>
            </File>
            <File>
              <FileName>MQTTUnsubscribeServer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Mqtt\MQTTUnsubscribeServer.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FreeRTOS/port</GroupName>
          <Files>
            <File>
              <FileName>heap_4.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FreeRTOS\Port\MemMang\heap_4.c</FilePath>
            </File>
            <File>
              <FileName>port.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FreeRTOS\Port\RVDS\ARM_CM3\port.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>FreeRTOS/src</GroupName>
          <Files>
            <File>
              <FileName>croutine.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FreeRTOS\Src\croutine.c</FilePath>
            </File>
            <File>
              <FileName>event_groups.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FreeRTOS\Src\event_groups.c</FilePath>
            </File>
            <File>
              <FileName>list.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FreeRTOS\Src\list.c</FilePath>
            </File>
            <File>
              <FileName>queue.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FreeRTOS\Src\queue.c</FilePath>
            </File>
            <File>
              <FileName>stream_buffer.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FreeRTOS\Src\stream_buffer.c</FilePath>
            </File>
            <File>
              <FileName>timers.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FreeRTOS\Src\timers.c</FilePath>
            </File>
            <File>
              <FileName>tasks.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\FreeRTOS\Src\tasks.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>lvgl/guider</GroupName>
          <Files>
            <File>
              <FileName>events_init.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\generated\events_init.c</FilePath>
            </File>
            <File>
              <FileName>gui_guider.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\generated\gui_guider.c</FilePath>
            </File>
            <File>
              <FileName>setup_scr_screen.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\generated\setup_scr_screen.c</FilePath>
            </File>
            <File>
              <FileName>setup_scr_screen_1.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\generated\setup_scr_screen_1.c</FilePath>
            </File>
            <File>
              <FileName>_9_alpha_320x240.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\generated\images\_9_alpha_320x240.c</FilePath>
            </File>
            <File>
              <FileName>_CO2_alpha_40x40.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\generated\images\_CO2_alpha_40x40.c</FilePath>
            </File>
            <File>
              <FileName>_dianliang_alpha_39x27.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\generated\images\_dianliang_alpha_39x27.c</FilePath>
            </File>
            <File>
              <FileName>_didian_alpha_39x30.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\generated\images\_didian_alpha_39x30.c</FilePath>
            </File>
            <File>
              <FileName>_HMP_alpha_20x20.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\generated\images\_HMP_alpha_20x20.c</FilePath>
            </File>
            <File>
              <FileName>_SUN_alpha_40x40.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\generated\images\_SUN_alpha_40x40.c</FilePath>
            </File>
            <File>
              <FileName>_sunshine_alpha_36x38.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\generated\images\_sunshine_alpha_36x38.c</FilePath>
            </File>
            <File>
              <FileName>_TMP_alpha_20x20.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\generated\images\_TMP_alpha_20x20.c</FilePath>
            </File>
            <File>
              <FileName>_wifi_alpha_55x55.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\generated\images\_wifi_alpha_55x55.c</FilePath>
            </File>
            <File>
              <FileName>_wifi_you_alpha_55x55.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\generated\images\_wifi_you_alpha_55x55.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_simsun_12.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\generated\guider_fonts\lv_font_simsun_12.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_Slideyouran_Regular_15.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\generated\guider_fonts\lv_font_Slideyouran_Regular_15.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_Slideyouran_Regular_20.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\generated\guider_fonts\lv_font_Slideyouran_Regular_20.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_Slideyouran_Regular_22.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\generated\guider_fonts\lv_font_Slideyouran_Regular_22.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_Slideyouran_Regular_25.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\generated\guider_fonts\lv_font_Slideyouran_Regular_25.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>lvgl/app</GroupName>
          <Files>
            <File>
              <FileName>hum.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\app\hum.c</FilePath>
            </File>
            <File>
              <FileName>lvgl_app.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\app\lvgl_app.c</FilePath>
            </File>
            <File>
              <FileName>My_font.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\app\My_font.c</FilePath>
            </File>
            <File>
              <FileName>Myfont_16.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\app\Myfont_16.c</FilePath>
            </File>
            <File>
              <FileName>Myfont_24.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\app\Myfont_24.c</FilePath>
            </File>
            <File>
              <FileName>shidu.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\app\shidu.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>lval/src</GroupName>
          <Files>
            <File>
              <FileName>lv_disp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_core\lv_disp.c</FilePath>
            </File>
            <File>
              <FileName>lv_group.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_core\lv_group.c</FilePath>
            </File>
            <File>
              <FileName>lv_indev.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_core\lv_indev.c</FilePath>
            </File>
            <File>
              <FileName>lv_obj.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_core\lv_obj.c</FilePath>
            </File>
            <File>
              <FileName>lv_refr.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_core\lv_refr.c</FilePath>
            </File>
            <File>
              <FileName>lv_style.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_core\lv_style.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_arc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_draw\lv_draw_arc.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_blend.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_draw\lv_draw_blend.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_img.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_draw\lv_draw_img.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_label.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_draw\lv_draw_label.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_line.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_draw\lv_draw_line.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_mask.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_draw\lv_draw_mask.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_rect.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_draw\lv_draw_rect.c</FilePath>
            </File>
            <File>
              <FileName>lv_draw_triangle.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_draw\lv_draw_triangle.c</FilePath>
            </File>
            <File>
              <FileName>lv_img_buf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_draw\lv_img_buf.c</FilePath>
            </File>
            <File>
              <FileName>lv_img_cache.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_draw\lv_img_cache.c</FilePath>
            </File>
            <File>
              <FileName>lv_img_decoder.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_draw\lv_img_decoder.c</FilePath>
            </File>
            <File>
              <FileName>lv_font.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_dejavu_16_persian_hebrew.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_dejavu_16_persian_hebrew.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_fmt_txt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_fmt_txt.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_loader.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_loader.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_8.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_8.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_10.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_10.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_12.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_12.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_12_subpx.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_12_subpx.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_14.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_14.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_16.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_16.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_18.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_18.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_20.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_20.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_22.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_22.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_24.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_24.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_26.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_26.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_28.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_28.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_28_compressed.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_28_compressed.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_30.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_30.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_32.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_32.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_34.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_34.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_36.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_36.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_38.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_38.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_40.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_40.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_42.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_42.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_44.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_44.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_46.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_46.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_montserrat_48.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_montserrat_48.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_simsun_16_cjk.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_simsun_16_cjk.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_unscii_8.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_unscii_8.c</FilePath>
            </File>
            <File>
              <FileName>lv_font_unscii_16.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_font\lv_font_unscii_16.c</FilePath>
            </File>
            <File>
              <FileName>lv_hal_disp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_hal\lv_hal_disp.c</FilePath>
            </File>
            <File>
              <FileName>lv_hal_indev.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_hal\lv_hal_indev.c</FilePath>
            </File>
            <File>
              <FileName>lv_hal_tick.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_hal\lv_hal_tick.c</FilePath>
            </File>
            <File>
              <FileName>lv_anim.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_misc\lv_anim.c</FilePath>
            </File>
            <File>
              <FileName>lv_area.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_misc\lv_area.c</FilePath>
            </File>
            <File>
              <FileName>lv_async.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_misc\lv_async.c</FilePath>
            </File>
            <File>
              <FileName>lv_bidi.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_misc\lv_bidi.c</FilePath>
            </File>
            <File>
              <FileName>lv_color.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_misc\lv_color.c</FilePath>
            </File>
            <File>
              <FileName>lv_debug.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_misc\lv_debug.c</FilePath>
            </File>
            <File>
              <FileName>lv_fs.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_misc\lv_fs.c</FilePath>
            </File>
            <File>
              <FileName>lv_gc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_misc\lv_gc.c</FilePath>
            </File>
            <File>
              <FileName>lv_ll.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_misc\lv_ll.c</FilePath>
            </File>
            <File>
              <FileName>lv_log.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_misc\lv_log.c</FilePath>
            </File>
            <File>
              <FileName>lv_math.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_misc\lv_math.c</FilePath>
            </File>
            <File>
              <FileName>lv_mem.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_misc\lv_mem.c</FilePath>
            </File>
            <File>
              <FileName>lv_printf.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_misc\lv_printf.c</FilePath>
            </File>
            <File>
              <FileName>lv_task.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_misc\lv_task.c</FilePath>
            </File>
            <File>
              <FileName>lv_templ.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_misc\lv_templ.c</FilePath>
            </File>
            <File>
              <FileName>lv_txt.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_misc\lv_txt.c</FilePath>
            </File>
            <File>
              <FileName>lv_txt_ap.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_misc\lv_txt_ap.c</FilePath>
            </File>
            <File>
              <FileName>lv_utils.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_misc\lv_utils.c</FilePath>
            </File>
            <File>
              <FileName>lv_theme.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_themes\lv_theme.c</FilePath>
            </File>
            <File>
              <FileName>lv_theme_empty.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_themes\lv_theme_empty.c</FilePath>
            </File>
            <File>
              <FileName>lv_theme_material.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_themes\lv_theme_material.c</FilePath>
            </File>
            <File>
              <FileName>lv_theme_mono.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_themes\lv_theme_mono.c</FilePath>
            </File>
            <File>
              <FileName>lv_theme_template.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_themes\lv_theme_template.c</FilePath>
            </File>
            <File>
              <FileName>lv_arc.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_arc.c</FilePath>
            </File>
            <File>
              <FileName>lv_bar.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_bar.c</FilePath>
            </File>
            <File>
              <FileName>lv_btn.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_btn.c</FilePath>
            </File>
            <File>
              <FileName>lv_btnmatrix.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_btnmatrix.c</FilePath>
            </File>
            <File>
              <FileName>lv_calendar.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_calendar.c</FilePath>
            </File>
            <File>
              <FileName>lv_canvas.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_canvas.c</FilePath>
            </File>
            <File>
              <FileName>lv_chart.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_chart.c</FilePath>
            </File>
            <File>
              <FileName>lv_checkbox.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_checkbox.c</FilePath>
            </File>
            <File>
              <FileName>lv_cont.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_cont.c</FilePath>
            </File>
            <File>
              <FileName>lv_cpicker.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_cpicker.c</FilePath>
            </File>
            <File>
              <FileName>lv_dropdown.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_dropdown.c</FilePath>
            </File>
            <File>
              <FileName>lv_gauge.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_gauge.c</FilePath>
            </File>
            <File>
              <FileName>lv_img.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_img.c</FilePath>
            </File>
            <File>
              <FileName>lv_imgbtn.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_imgbtn.c</FilePath>
            </File>
            <File>
              <FileName>lv_keyboard.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_keyboard.c</FilePath>
            </File>
            <File>
              <FileName>lv_label.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_label.c</FilePath>
            </File>
            <File>
              <FileName>lv_led.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_led.c</FilePath>
            </File>
            <File>
              <FileName>lv_line.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_line.c</FilePath>
            </File>
            <File>
              <FileName>lv_linemeter.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_linemeter.c</FilePath>
            </File>
            <File>
              <FileName>lv_list.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_list.c</FilePath>
            </File>
            <File>
              <FileName>lv_msgbox.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_msgbox.c</FilePath>
            </File>
            <File>
              <FileName>lv_objmask.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_objmask.c</FilePath>
            </File>
            <File>
              <FileName>lv_objx_templ.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_objx_templ.c</FilePath>
            </File>
            <File>
              <FileName>lv_page.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_page.c</FilePath>
            </File>
            <File>
              <FileName>lv_roller.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_roller.c</FilePath>
            </File>
            <File>
              <FileName>lv_slider.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_slider.c</FilePath>
            </File>
            <File>
              <FileName>lv_spinbox.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_spinbox.c</FilePath>
            </File>
            <File>
              <FileName>lv_spinner.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_spinner.c</FilePath>
            </File>
            <File>
              <FileName>lv_switch.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_switch.c</FilePath>
            </File>
            <File>
              <FileName>lv_table.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_table.c</FilePath>
            </File>
            <File>
              <FileName>lv_tabview.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_tabview.c</FilePath>
            </File>
            <File>
              <FileName>lv_textarea.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_textarea.c</FilePath>
            </File>
            <File>
              <FileName>lv_tileview.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_tileview.c</FilePath>
            </File>
            <File>
              <FileName>lv_win.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\src\lv_widgets\lv_win.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>lvgl/porting</GroupName>
          <Files>
            <File>
              <FileName>lv_port_disp.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\porting\lv_port_disp.c</FilePath>
            </File>
            <File>
              <FileName>lv_port_fs_template.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\porting\lv_port_fs_template.c</FilePath>
            </File>
            <File>
              <FileName>lv_port_indev_template.c</FileName>
              <FileType>1</FileType>
              <FilePath>..\Lvgl\porting\lv_port_indev_template.c</FilePath>
            </File>
          </Files>
        </Group>
        <Group>
          <GroupName>::CMSIS</GroupName>
        </Group>
      </Groups>
    </Target>
  </Targets>

  <RTE>
    <apis/>
    <components>
      <component Cclass="CMSIS" Cgroup="CORE" Cvendor="ARM" Cversion="5.2.0" condition="ARMv6_7_8-M Device">
        <package name="CMSIS" schemaVersion="1.3" url="http://www.keil.com/pack/" vendor="ARM" version="5.5.1"/>
        <targetInfos>
          <targetInfo name="Target 1"/>
        </targetInfos>
      </component>
    </components>
    <files/>
  </RTE>

</Project>
