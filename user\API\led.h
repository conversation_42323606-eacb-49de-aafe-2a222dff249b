#ifndef __LED_H_
#define __LED_H_

#include "stm32f10x.h"

void LED_Config(void);
void LED1_ON(void);
void LED1_OFF(void);
void LED1_TOGGLE(void);
void LED2_ON(void);
void LED2_OFF(void);
void LED2_TOGGLE(void);
void LED3_ON(void);
void LED3_OFF(void);
void LED3_TOGGLE(void);
void LED4_ON(void);
void LED4_OFF(void);
void LED4_TOGGLE(void);
void RGB_Config(void);
void RGB_R_ON(void);
void RGB_R_OFF(void);
void RGB_R_TOGGLE(void);
void RGB_G_ON(void);
void RGB_G_OFF(void);
void RGB_G_TOGGLE(void);
void RGB_B_ON(void);
void RGB_B_OFF(void);
void RGB_B_TOGGLE(void);
void RGB_SHOW(void);
#endif
