#include "beep.h"

void BEEP_Config(void)
{
	RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC,ENABLE);
	GPIO_InitTypeDef GPIO_InitStruct={0};
	GPIO_InitStruct.GPIO_Pin=GPIO_Pin_0;
	GPIO_InitStruct.GPIO_Mode=GPIO_Mode_Out_PP;
	GPIO_InitStruct.GPIO_Speed=GPIO_Speed_10MHz;
	GPIO_Init(GPIOC,&GPIO_InitStruct);
	
}
void Beep_On(void)
{
	GPIO_SetBits(GPIOC,GPIO_Pin_0);
}
void Beep_Off(void)
{
	GPIO_ResetBits(GPIOC,GPIO_Pin_0);
}
void Beep_Turn(void)
{
	GPIO_WriteBit(GPIOC,GPIO_Pin_0,(BitAction)(1-GPIO_ReadOutputDataBit(GPIOC,GPIO_Pin_0)));
}
