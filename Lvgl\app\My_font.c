/*******************************************************************************
 * Size: 16 px
 * Bpp: 1
 * Opts: --bpp 1 --size 16 --no-compress --font simkai.ttf --symbols 空气质量检测仪 --format lvgl -o My_font.c
 ******************************************************************************/

#ifdef LV_LVGL_H_INCLUDE_SIMPLE
#include "lvgl.h"
#else
#include "lvgl.h"
#endif

#ifndef MY_FONT
#define MY_FONT 1
#endif

#if MY_FONT

/*-----------------
 *    BITMAPS
 *----------------*/

/*Store the image of the glyphs*/
static LV_ATTRIBUTE_LARGE_CONST const uint8_t glyph_bitmap[] = {
    /* U+4EEA "仪" */
    0xc, 0x0, 0xd, 0x10, 0x8, 0x98, 0x8, 0x10,
    0x10, 0x10, 0x29, 0x10, 0x28, 0x90, 0x48, 0x60,
    0x88, 0x20, 0x8, 0xf0, 0x9, 0x18, 0x8, 0x6,
    0x8, 0x0,

    /* U+68C0 "检" */
    0xc, 0x40, 0x8, 0x40, 0x8, 0xa0, 0x8, 0x90,
    0x3d, 0xc, 0xa, 0xe7, 0x18, 0x10, 0x2c, 0x18,
    0x49, 0x50, 0x9, 0x10, 0x8, 0x20, 0x1b, 0xfe,
    0x0, 0x0,

    /* U+6C14 "气" */
    0x8, 0x0, 0xc4, 0x7, 0xc0, 0x40, 0x0, 0xe0,
    0x0, 0x0, 0x3c, 0xe, 0x20, 0x1, 0x0, 0x8,
    0x0, 0x40, 0x2, 0x0, 0x9, 0x0, 0x3c,

    /* U+6D4B "测" */
    0x0, 0x0, 0x0, 0x4b, 0xc2, 0x12, 0x10, 0xd4,
    0xa6, 0xa5, 0x35, 0x25, 0xa9, 0x4d, 0x4a, 0x30,
    0x52, 0x62, 0x21, 0x50, 0x1, 0x80, 0x0,

    /* U+7A7A "空" */
    0x6, 0x0, 0x0, 0x13, 0xf8, 0xe0, 0xe9, 0x90,
    0x8, 0xc0, 0x80, 0x8, 0x20, 0x1e, 0x0, 0x20,
    0x1, 0x0, 0x1f, 0xe7, 0x0, 0x0,

    /* U+8D28 "质" */
    0x0, 0x40, 0x3, 0x80, 0xf4, 0x2, 0x20, 0xb,
    0xf8, 0x22, 0x0, 0xbf, 0x4, 0x84, 0x12, 0xd0,
    0x4a, 0x42, 0x29, 0x10, 0x20, 0x1, 0x30, 0x8,
    0x60, 0x0, 0x0,

    /* U+91CF "量" */
    0x0, 0xc0, 0x3d, 0x0, 0xe4, 0x2, 0x60, 0x6,
    0x1b, 0xff, 0x80, 0x3c, 0x7, 0x90, 0x17, 0x40,
    0x3e, 0x0, 0x38, 0x3, 0x80, 0x3, 0xf0, 0xf0,
    0x0
};


/*---------------------
 *  GLYPH DESCRIPTION
 *--------------------*/

static const lv_font_fmt_txt_glyph_dsc_t glyph_dsc[] = {
    {.bitmap_index = 0, .adv_w = 0, .box_w = 0, .box_h = 0, .ofs_x = 0, .ofs_y = 0} /* id = 0 reserved */,
    {.bitmap_index = 0, .adv_w = 256, .box_w = 16, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 26, .adv_w = 256, .box_w = 16, .box_h = 13, .ofs_x = 0, .ofs_y = -1},
    {.bitmap_index = 52, .adv_w = 256, .box_w = 13, .box_h = 14, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 75, .adv_w = 256, .box_w = 13, .box_h = 14, .ofs_x = 1, .ofs_y = -1},
    {.bitmap_index = 98, .adv_w = 256, .box_w = 13, .box_h = 13, .ofs_x = 2, .ofs_y = -1},
    {.bitmap_index = 120, .adv_w = 256, .box_w = 14, .box_h = 15, .ofs_x = 1, .ofs_y = -2},
    {.bitmap_index = 147, .adv_w = 256, .box_w = 14, .box_h = 14, .ofs_x = 1, .ofs_y = -1}
};

/*---------------------
 *  CHARACTER MAPPING
 *--------------------*/

static const uint16_t unicode_list_0[] = {
    0x0, 0x19d6, 0x1d2a, 0x1e61, 0x2b90, 0x3e3e, 0x42e5
};

/*Collect the unicode lists and glyph_id offsets*/
static const lv_font_fmt_txt_cmap_t cmaps[] =
{
    {
        .range_start = 20202, .range_length = 17126, .glyph_id_start = 1,
        .unicode_list = unicode_list_0, .glyph_id_ofs_list = NULL, .list_length = 7, .type = LV_FONT_FMT_TXT_CMAP_SPARSE_TINY
    }
};



/*--------------------
 *  ALL CUSTOM DATA
 *--------------------*/

#if LVGL_VERSION_MAJOR == 8
/*Store all the custom data of the font*/
static  lv_font_fmt_txt_glyph_cache_t cache;
#endif

#if LVGL_VERSION_MAJOR >= 8
static const lv_font_fmt_txt_dsc_t font_dsc = {
#else
static lv_font_fmt_txt_dsc_t font_dsc = {
#endif
    .glyph_bitmap = glyph_bitmap,
    .glyph_dsc = glyph_dsc,
    .cmaps = cmaps,
    .kern_dsc = NULL,
    .kern_scale = 0,
    .cmap_num = 1,
    .bpp = 1,
    .kern_classes = 0,
    .bitmap_format = 0,
#if LVGL_VERSION_MAJOR == 8
    .cache = &cache
#endif
};



/*-----------------
 *  PUBLIC FONT
 *----------------*/

/*Initialize a public general font descriptor*/
#if LVGL_VERSION_MAJOR >= 8
const lv_font_t My_font = {
#else
lv_font_t My_font = {
#endif
    .get_glyph_dsc = lv_font_get_glyph_dsc_fmt_txt,    /*Function pointer to get glyph's data*/
    .get_glyph_bitmap = lv_font_get_bitmap_fmt_txt,    /*Function pointer to get glyph's bitmap*/
    .line_height = 15,          /*The maximum line height required by the font*/
    .base_line = 2,             /*Baseline measured from the bottom of the line*/
#if !(LVGL_VERSION_MAJOR == 6 && LVGL_VERSION_MINOR == 0)
    .subpx = LV_FONT_SUBPX_NONE,
#endif
#if LV_VERSION_CHECK(7, 4, 0) || LVGL_VERSION_MAJOR >= 8
    .underline_position = -1,
    .underline_thickness = 1,
#endif
    .dsc = &font_dsc,          /*The custom font data. Will be accessed by `get_glyph_bitmap/dsc` */
#if LV_VERSION_CHECK(8, 2, 0) || LVGL_VERSION_MAJOR >= 9
    .fallback = NULL,
#endif
//    .user_data = NULL,
};



#endif /*#if MY_FONT*/

