/*
 * Copyright 2025 NXP
 * SPDX-License-Identifier: MIT
 */

#include "lvgl.h"

#include "lvgl.h"

#include "lvgl.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMG__DIANLIANG_ALPHA_39X27
#define LV_ATTRIBUTE_IMG__DIANLIANG_ALPHA_39X27
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_IMG__DIANLIANG_ALPHA_39X27 uint8_t _dianliang_alpha_39x27_map[] = {
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP == 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit*/
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x69, 0x00, 0x00, 0x56, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf6, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x2c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd5, 0x00, 0x00, 0x2e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xf0, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xf2, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd4, 0x00, 0x00, 0x2e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x90, 0x00, 0x00, 0x44, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x43, 0x00, 0x00, 0xa0, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd4, 0x00, 0x00, 0x2e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x81, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x95, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd4, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x19, 0x00, 0x00, 0x7a, 0x00, 0x00, 0x90, 0x00, 0x00, 0x90, 0x00, 0x00, 0x6a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5e, 0x00, 0x00, 0x8f, 0x00, 0x00, 0x90, 0x00, 0x00, 0x83, 0x00, 0x00, 0x25, 0x00, 0x00, 0x00, 0x00, 0x00, 0x36, 0x00, 0x00, 0x87, 0x00, 0x00, 0x90, 0x00, 0x00, 0x8e, 0x00, 0x00, 0x4e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0b, 0x00, 0x00, 0x75, 0x00, 0x00, 0x90, 0x00, 0x00, 0x90, 0x00, 0x00, 0x71, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xe3, 0x00, 0x00, 0x94, 0x00, 0x00, 0x8f, 0x00, 0x00, 0x90, 0x00, 0x00, 0x77, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x32, 0x00, 0x00, 0xd7, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xbd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa9, 0x00, 0x00, 0xfb, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xe6, 0x00, 0x00, 0x47, 0x00, 0x00, 0x00, 0x00, 0x00, 0x62, 0x00, 0x00, 0xed, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xf9, 0x00, 0x00, 0x8d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0xcd, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xc9, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xfb, 0x00, 0x00, 0xfb, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xd2, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 0xda, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xab, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xea, 0x00, 0x00, 0x49, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x00, 0x00, 0xf1, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfd, 0x00, 0x00, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0xd1, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd6, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 0xda, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xab, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xea, 0x00, 0x00, 0x49, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x00, 0x00, 0xf1, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfd, 0x00, 0x00, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0xd0, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd5, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 0xda, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xab, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xea, 0x00, 0x00, 0x49, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x00, 0x00, 0xf1, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfd, 0x00, 0x00, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0xd0, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd5, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 0xda, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xab, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xea, 0x00, 0x00, 0x49, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x00, 0x00, 0xf1, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfd, 0x00, 0x00, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0xd0, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd5, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 0xda, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xab, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xea, 0x00, 0x00, 0x49, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x00, 0x00, 0xf1, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfd, 0x00, 0x00, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0xd0, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd5, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 0xda, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xab, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xea, 0x00, 0x00, 0x49, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x00, 0x00, 0xf1, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfd, 0x00, 0x00, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0xd0, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd5, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 0xda, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xab, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xea, 0x00, 0x00, 0x49, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x00, 0x00, 0xf1, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfd, 0x00, 0x00, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0xd1, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd6, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x32, 0x00, 0x00, 0xd7, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xbd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa9, 0x00, 0x00, 0xfb, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xe6, 0x00, 0x00, 0x47, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x00, 0x00, 0xed, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xf9, 0x00, 0x00, 0x8d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0xcd, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xc9, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xfb, 0x00, 0x00, 0xfb, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xd2, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x19, 0x00, 0x00, 0x7a, 0x00, 0x00, 0x90, 0x00, 0x00, 0x90, 0x00, 0x00, 0x6b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5e, 0x00, 0x00, 0x8f, 0x00, 0x00, 0x90, 0x00, 0x00, 0x83, 0x00, 0x00, 0x25, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 0x87, 0x00, 0x00, 0x90, 0x00, 0x00, 0x8e, 0x00, 0x00, 0x4e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 0x73, 0x00, 0x00, 0x90, 0x00, 0x00, 0x90, 0x00, 0x00, 0x72, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xe3, 0x00, 0x00, 0x94, 0x00, 0x00, 0x8f, 0x00, 0x00, 0x90, 0x00, 0x00, 0x77, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x81, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x95, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd4, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x90, 0x00, 0x00, 0x44, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x43, 0x00, 0x00, 0xa2, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd4, 0x00, 0x00, 0x2e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xf0, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xf2, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd4, 0x00, 0x00, 0x2e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd5, 0x00, 0x00, 0x2e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf6, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x2c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x69, 0x00, 0x00, 0x56, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP != 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit  BUT the 2  color bytes are swapped*/
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x69, 0x00, 0x00, 0x56, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf6, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x2c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd5, 0x00, 0x00, 0x2e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xf0, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xf2, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd4, 0x00, 0x00, 0x2e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x90, 0x00, 0x00, 0x44, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x43, 0x00, 0x00, 0xa0, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd4, 0x00, 0x00, 0x2e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x81, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x95, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd4, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x19, 0x00, 0x00, 0x7a, 0x00, 0x00, 0x90, 0x00, 0x00, 0x90, 0x00, 0x00, 0x6a, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5e, 0x00, 0x00, 0x8f, 0x00, 0x00, 0x90, 0x00, 0x00, 0x83, 0x00, 0x00, 0x25, 0x00, 0x00, 0x00, 0x00, 0x00, 0x36, 0x00, 0x00, 0x87, 0x00, 0x00, 0x90, 0x00, 0x00, 0x8e, 0x00, 0x00, 0x4e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x0b, 0x00, 0x00, 0x75, 0x00, 0x00, 0x90, 0x00, 0x00, 0x90, 0x00, 0x00, 0x71, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xe3, 0x00, 0x00, 0x94, 0x00, 0x00, 0x8f, 0x00, 0x00, 0x90, 0x00, 0x00, 0x77, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x32, 0x00, 0x00, 0xd7, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xbd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa9, 0x00, 0x00, 0xfb, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xe6, 0x00, 0x00, 0x47, 0x00, 0x00, 0x00, 0x00, 0x00, 0x62, 0x00, 0x00, 0xed, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xf9, 0x00, 0x00, 0x8d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0xcd, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xc9, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xfb, 0x00, 0x00, 0xfb, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xd2, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 0xda, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xab, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xea, 0x00, 0x00, 0x49, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x00, 0x00, 0xf1, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfd, 0x00, 0x00, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0xd1, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd6, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 0xda, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xab, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xea, 0x00, 0x00, 0x49, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x00, 0x00, 0xf1, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfd, 0x00, 0x00, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0xd0, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd5, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 0xda, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xab, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xea, 0x00, 0x00, 0x49, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x00, 0x00, 0xf1, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfd, 0x00, 0x00, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0xd0, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd5, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 0xda, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xab, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xea, 0x00, 0x00, 0x49, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x00, 0x00, 0xf1, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfd, 0x00, 0x00, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0xd0, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd5, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 0xda, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xab, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xea, 0x00, 0x00, 0x49, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x00, 0x00, 0xf1, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfd, 0x00, 0x00, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0xd0, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd5, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 0xda, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xab, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xea, 0x00, 0x00, 0x49, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x00, 0x00, 0xf1, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfd, 0x00, 0x00, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0xd0, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd5, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 0xda, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xc0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xab, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xea, 0x00, 0x00, 0x49, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63, 0x00, 0x00, 0xf1, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfd, 0x00, 0x00, 0x90, 0x00, 0x00, 0x00, 0x00, 0x00, 0x15, 0x00, 0x00, 0xd1, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd6, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x32, 0x00, 0x00, 0xd7, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xbd, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa9, 0x00, 0x00, 0xfb, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xe6, 0x00, 0x00, 0x47, 0x00, 0x00, 0x00, 0x00, 0x00, 0x61, 0x00, 0x00, 0xed, 0x00, 0x00, 0xfc, 0x00, 0x00, 0xf9, 0x00, 0x00, 0x8d, 0x00, 0x00, 0x00, 0x00, 0x00, 0x14, 0x00, 0x00, 0xcd, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xc9, 0x00, 0x00, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xfb, 0x00, 0x00, 0xfb, 0x00, 0x00, 0xfd, 0x00, 0x00, 0xd2, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x82, 0x00, 0x00, 0x00, 0x00, 0x00, 0x19, 0x00, 0x00, 0x7a, 0x00, 0x00, 0x90, 0x00, 0x00, 0x90, 0x00, 0x00, 0x6b, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5e, 0x00, 0x00, 0x8f, 0x00, 0x00, 0x90, 0x00, 0x00, 0x83, 0x00, 0x00, 0x25, 0x00, 0x00, 0x00, 0x00, 0x00, 0x33, 0x00, 0x00, 0x87, 0x00, 0x00, 0x90, 0x00, 0x00, 0x8e, 0x00, 0x00, 0x4e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x05, 0x00, 0x00, 0x73, 0x00, 0x00, 0x90, 0x00, 0x00, 0x90, 0x00, 0x00, 0x72, 0x00, 0x00, 0x06, 0x00, 0x00, 0x00, 0x00, 0x00, 0x96, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xe3, 0x00, 0x00, 0x94, 0x00, 0x00, 0x8f, 0x00, 0x00, 0x90, 0x00, 0x00, 0x77, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x81, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x95, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd4, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfb, 0x00, 0x00, 0x90, 0x00, 0x00, 0x44, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x4b, 0x00, 0x00, 0x43, 0x00, 0x00, 0xa2, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd4, 0x00, 0x00, 0x2e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xfe, 0x00, 0x00, 0xf0, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xeb, 0x00, 0x00, 0xf2, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd4, 0x00, 0x00, 0x2e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xff, 0x00, 0x00, 0xd5, 0x00, 0x00, 0x2e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf5, 0x00, 0x00, 0xf6, 0x00, 0x00, 0xcc, 0x00, 0x00, 0x2c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x68, 0x00, 0x00, 0x69, 0x00, 0x00, 0x56, 0x00, 0x00, 0x0c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
#endif
};

const lv_img_dsc_t _dianliang_alpha_39x27 = {
  .header.always_zero = 0,
  .header.w = 39,
  .header.h = 27,
  .data_size = 1053 * LV_COLOR_SIZE / 8,
  .header.cf = LV_IMG_CF_TRUE_COLOR_ALPHA,
  .data = _dianliang_alpha_39x27_map,
};
