/*
 * Copyright 2025 NXP
 * SPDX-License-Identifier: MIT
 */

#include "lvgl.h"

#include "lvgl.h"

#include "lvgl.h"

#ifndef LV_ATTRIBUTE_MEM_ALIGN
#define LV_ATTRIBUTE_MEM_ALIGN
#endif

#ifndef LV_ATTRIBUTE_IMG__HMP_ALPHA_20X20
#define LV_ATTRIBUTE_IMG__HMP_ALPHA_20X20
#endif

const LV_ATTRIBUTE_MEM_ALIGN LV_ATTRIBUTE_IMG__HMP_ALPHA_20X20 uint8_t _HMP_alpha_20x20_map[] = {
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP == 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit*/
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x34, 0x00, 0x00, 0x9a, 0x0a, 0x12, 0xcf, 0x4b, 0x12, 0xd4, 0xc4, 0x00, 0xab, 0x00, 0x00, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3d, 0x87, 0x09, 0xc6, 0x54, 0x2c, 0xfc, 0x79, 0x3d, 0xff, 0x9a, 0x3d, 0xff, 0xb6, 0x34, 0xfe, 0x6b, 0x12, 0xdb, 0x00, 0x00, 0x67, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0x66, 0x09, 0xc0, 0xb6, 0x34, 0xff, 0xdb, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0x38, 0x35, 0xff, 0x8b, 0x12, 0xdc, 0x00, 0x00, 0x58, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9b, 0x33, 0x2c, 0xfa, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0xf7, 0x34, 0xff, 0x67, 0x09, 0xc2, 0x00, 0x00, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4b, 0xcd, 0x1a, 0xe2, 0x9a, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xf2, 0x23, 0xf6, 0x00, 0x00, 0x84, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x62, 0x00, 0xa1, 0xb6, 0x34, 0xfd, 0xfb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0x59, 0x3d, 0xff, 0x09, 0x12, 0xcd, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x34, 0xac, 0x1a, 0xda, 0x9a, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xd1, 0x23, 0xf2, 0x00, 0x00, 0x71, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7b, 0x13, 0x2c, 0xf6, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xba, 0x35, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0xbb, 0x3d, 0xff, 0x79, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0xf7, 0x34, 0xff, 0xc3, 0x00, 0xb0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe4, 0x00, 0xb1, 0xf7, 0x34, 0xff, 0xfb, 0x3d, 0xff, 0xba, 0x3d, 0xff, 0x74, 0x54, 0xff, 0x52, 0x6c, 0xff, 0x38, 0x35, 0xff, 0xfb, 0x3d, 0xff, 0xbb, 0x3d, 0xff, 0x33, 0x2c, 0xff, 0x95, 0x2c, 0xff, 0xfb, 0x3d, 0xff, 0x9a, 0x3d, 0xff, 0x0e, 0x1b, 0xc9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x2a, 0x12, 0xcf, 0x79, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0x59, 0x2d, 0xff, 0xb3, 0x8c, 0xff, 0x96, 0xb5, 0xff, 0x74, 0x34, 0xff, 0xdb, 0x3d, 0xff, 0x34, 0x2c, 0xff, 0x54, 0x2c, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0x54, 0x2c, 0xdd, 0x62, 0x00, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x42, 0x2f, 0x1b, 0xe5, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0x95, 0x44, 0xff, 0x33, 0x44, 0xff, 0x59, 0x35, 0xff, 0x34, 0x2c, 0xff, 0x54, 0x2c, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0xb6, 0x34, 0xee, 0xa3, 0x00, 0x67, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0x70, 0x23, 0xec, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xbb, 0x3d, 0xff, 0x34, 0x2c, 0xff, 0x54, 0x2c, 0xff, 0x9a, 0x35, 0xff, 0x9a, 0x35, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0x95, 0x2c, 0xfc, 0x00, 0x00, 0x8c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5b, 0x90, 0x23, 0xed, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0xbb, 0x3d, 0xff, 0x34, 0x2c, 0xff, 0x54, 0x2c, 0xff, 0x79, 0x35, 0xff, 0x53, 0x64, 0xff, 0x52, 0x74, 0xff, 0x38, 0x35, 0xff, 0xfb, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0x95, 0x2c, 0xfc, 0x00, 0x00, 0x91, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4b, 0x4f, 0x23, 0xe9, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xbb, 0x3d, 0xff, 0x34, 0x2c, 0xff, 0x54, 0x2c, 0xff, 0xdb, 0x3d, 0xff, 0x18, 0x2d, 0xff, 0xf3, 0x9c, 0xff, 0x75, 0xb5, 0xff, 0xb6, 0x34, 0xff, 0xfb, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0x75, 0x2c, 0xfb, 0x00, 0x00, 0x89, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x29, 0x6b, 0x12, 0xd7, 0x9a, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0x17, 0x35, 0xff, 0x54, 0x2c, 0xff, 0xdb, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0xbb, 0x3d, 0xff, 0x95, 0x3c, 0xff, 0x33, 0x3c, 0xff, 0x9a, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xb1, 0x23, 0xf2, 0x00, 0x00, 0x6c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa3, 0x00, 0xab, 0xb6, 0x34, 0xfe, 0xfb, 0x3d, 0xff, 0xbb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0x59, 0x3d, 0xff, 0x09, 0x12, 0xd2, 0x00, 0x00, 0x25, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0xcd, 0x1a, 0xe3, 0x9a, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xf2, 0x23, 0xf6, 0x00, 0x00, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x89, 0x70, 0x23, 0xf2, 0x79, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xdb, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0xfb, 0x3d, 0xff, 0xbb, 0x3d, 0xff, 0x54, 0x2c, 0xfc, 0x05, 0x01, 0xb4, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x87, 0xad, 0x1a, 0xe1, 0xb5, 0x34, 0xfd, 0x79, 0x3d, 0xff, 0xba, 0x3d, 0xff, 0xba, 0x3d, 0xff, 0x9a, 0x3d, 0xff, 0xd7, 0x34, 0xff, 0x4f, 0x23, 0xef, 0xc3, 0x00, 0xab, 0x00, 0x00, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4f, 0x62, 0x00, 0x9e, 0x09, 0x12, 0xcb, 0xac, 0x1a, 0xdd, 0xad, 0x1a, 0xe0, 0x4a, 0x12, 0xd5, 0xc4, 0x00, 0xb1, 0x00, 0x00, 0x6c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
#endif
#if LV_COLOR_DEPTH == 16 && LV_COLOR_16_SWAP != 0
  /*Pixel format: Alpha 8 bit, Red: 5 bit, Green: 6 bit, Blue: 5 bit  BUT the 2  color bytes are swapped*/
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x34, 0x00, 0x00, 0x9a, 0x12, 0x0a, 0xcf, 0x12, 0x4b, 0xd4, 0x00, 0xc4, 0xab, 0x00, 0x00, 0x50, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3d, 0x09, 0x87, 0xc6, 0x2c, 0x54, 0xfc, 0x3d, 0x79, 0xff, 0x3d, 0x9a, 0xff, 0x34, 0xb6, 0xfe, 0x12, 0x6b, 0xdb, 0x00, 0x00, 0x67, 0x00, 0x00, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x22, 0x09, 0x66, 0xc0, 0x34, 0xb6, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xfb, 0xff, 0x3d, 0xfb, 0xff, 0x3d, 0xfb, 0xff, 0x35, 0x38, 0xff, 0x12, 0x8b, 0xdc, 0x00, 0x00, 0x58, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x9b, 0x2c, 0x33, 0xfa, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xfb, 0xff, 0x34, 0xf7, 0xff, 0x09, 0x67, 0xc2, 0x00, 0x00, 0x17, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4b, 0x1a, 0xcd, 0xe2, 0x3d, 0x9a, 0xff, 0x3d, 0xfb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x23, 0xf2, 0xf6, 0x00, 0x00, 0x84, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x62, 0xa1, 0x34, 0xb6, 0xfd, 0x3d, 0xfb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xfb, 0xff, 0x3d, 0x59, 0xff, 0x12, 0x09, 0xcd, 0x00, 0x00, 0x1e, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x34, 0x1a, 0xac, 0xda, 0x3d, 0x9a, 0xff, 0x3d, 0xfb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xfb, 0xff, 0x3d, 0xdb, 0xff, 0x23, 0xd1, 0xf2, 0x00, 0x00, 0x71, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7b, 0x2c, 0x13, 0xf6, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x35, 0xba, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xfb, 0xff, 0x3d, 0xbb, 0xff, 0x3d, 0x79, 0xff, 0x3d, 0xfb, 0xff, 0x34, 0xf7, 0xff, 0x00, 0xc3, 0xb0, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xe4, 0xb1, 0x34, 0xf7, 0xff, 0x3d, 0xfb, 0xff, 0x3d, 0xba, 0xff, 0x54, 0x74, 0xff, 0x6c, 0x52, 0xff, 0x35, 0x38, 0xff, 0x3d, 0xfb, 0xff, 0x3d, 0xbb, 0xff, 0x2c, 0x33, 0xff, 0x2c, 0x95, 0xff, 0x3d, 0xfb, 0xff, 0x3d, 0x9a, 0xff, 0x1b, 0x0e, 0xc9, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x12, 0x2a, 0xcf, 0x3d, 0x79, 0xff, 0x3d, 0xfb, 0xff, 0x2d, 0x59, 0xff, 0x8c, 0xb3, 0xff, 0xb5, 0x96, 0xff, 0x34, 0x74, 0xff, 0x3d, 0xdb, 0xff, 0x2c, 0x34, 0xff, 0x2c, 0x54, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x2c, 0x54, 0xdd, 0x00, 0x62, 0x44, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x42, 0x1b, 0x2f, 0xe5, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x44, 0x95, 0xff, 0x44, 0x33, 0xff, 0x35, 0x59, 0xff, 0x2c, 0x34, 0xff, 0x2c, 0x54, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xfb, 0xff, 0x34, 0xb6, 0xee, 0x00, 0xa3, 0x67, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x57, 0x23, 0x70, 0xec, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xbb, 0xff, 0x2c, 0x34, 0xff, 0x2c, 0x54, 0xff, 0x35, 0x9a, 0xff, 0x35, 0x9a, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xfb, 0xff, 0x2c, 0x95, 0xfc, 0x00, 0x00, 0x8c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x5b, 0x23, 0x90, 0xed, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xfb, 0xff, 0x3d, 0xbb, 0xff, 0x2c, 0x34, 0xff, 0x2c, 0x54, 0xff, 0x35, 0x79, 0xff, 0x64, 0x53, 0xff, 0x74, 0x52, 0xff, 0x35, 0x38, 0xff, 0x3d, 0xfb, 0xff, 0x3d, 0xfb, 0xff, 0x2c, 0x95, 0xfc, 0x00, 0x00, 0x91, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4b, 0x23, 0x4f, 0xe9, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xbb, 0xff, 0x2c, 0x34, 0xff, 0x2c, 0x54, 0xff, 0x3d, 0xdb, 0xff, 0x2d, 0x18, 0xff, 0x9c, 0xf3, 0xff, 0xb5, 0x75, 0xff, 0x34, 0xb6, 0xff, 0x3d, 0xfb, 0xff, 0x3d, 0xfb, 0xff, 0x2c, 0x75, 0xfb, 0x00, 0x00, 0x89, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x29, 0x12, 0x6b, 0xd7, 0x3d, 0x9a, 0xff, 0x3d, 0xfb, 0xff, 0x35, 0x17, 0xff, 0x2c, 0x54, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xfb, 0xff, 0x3d, 0xbb, 0xff, 0x3c, 0x95, 0xff, 0x3c, 0x33, 0xff, 0x3d, 0x9a, 0xff, 0x3d, 0xfb, 0xff, 0x3d, 0xdb, 0xff, 0x23, 0xb1, 0xf2, 0x00, 0x00, 0x6c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xa3, 0xab, 0x34, 0xb6, 0xfe, 0x3d, 0xfb, 0xff, 0x3d, 0xbb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xfb, 0xff, 0x3d, 0x59, 0xff, 0x12, 0x09, 0xd2, 0x00, 0x00, 0x25, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x55, 0x1a, 0xcd, 0xe3, 0x3d, 0x9a, 0xff, 0x3d, 0xfb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xfb, 0xff, 0x3d, 0xdb, 0xff, 0x23, 0xf2, 0xf6, 0x00, 0x00, 0x87, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x89, 0x23, 0x70, 0xf2, 0x3d, 0x79, 0xff, 0x3d, 0xfb, 0xff, 0x3d, 0xfb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xdb, 0xff, 0x3d, 0xfb, 0xff, 0x3d, 0xfb, 0xff, 0x3d, 0xbb, 0xff, 0x2c, 0x54, 0xfc, 0x01, 0x05, 0xb4, 0x00, 0x00, 0x10, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x87, 0x1a, 0xad, 0xe1, 0x34, 0xb5, 0xfd, 0x3d, 0x79, 0xff, 0x3d, 0xba, 0xff, 0x3d, 0xba, 0xff, 0x3d, 0x9a, 0xff, 0x34, 0xd7, 0xff, 0x23, 0x4f, 0xef, 0x00, 0xc3, 0xab, 0x00, 0x00, 0x29, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
  0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x4f, 0x00, 0x62, 0x9e, 0x12, 0x09, 0xcb, 0x1a, 0xac, 0xdd, 0x1a, 0xad, 0xe0, 0x12, 0x4a, 0xd5, 0x00, 0xc4, 0xb1, 0x00, 0x00, 0x6c, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 
#endif
};

const lv_img_dsc_t _HMP_alpha_20x20 = {
  .header.always_zero = 0,
  .header.w = 20,
  .header.h = 20,
  .data_size = 400 * LV_COLOR_SIZE / 8,
  .header.cf = LV_IMG_CF_TRUE_COLOR_ALPHA,
  .data = _HMP_alpha_20x20_map,
};
